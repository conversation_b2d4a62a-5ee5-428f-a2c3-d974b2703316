# ????????
# Production Environment Configuration

# ???????????????
file.upload.path=${FILE_UPLOAD_PATH:/var/lib/llb/uploads/}
file.temp.path=${FILE_TEMP_PATH:/var/lib/llb/temp/}

# ?????????????????
spring.datasource.url=${DATABASE_URL:********************************************************************************************************************}
spring.datasource.username=${DATABASE_USERNAME:llb_user}
spring.datasource.password=${DATABASE_PASSWORD:your_secure_password}

# ????Redis??????????
spring.data.redis.host=${REDIS_HOST:localhost}
spring.data.redis.port=${REDIS_PORT:6379}
spring.data.redis.password=${REDIS_PASSWORD:}
spring.data.redis.database=${REDIS_DATABASE:0}

# ????????
logging.level.com.example.v14=INFO
logging.level.org.springframework.web=WARN
logging.level.org.apache.ibatis.logging.stdout.StdOutImpl=WARN

# ????Sa-Token???????????
sa-token.timeout=2592000
sa-token.is-log=false

# ?????????
server.port=${SERVER_PORT:8080}

# ??????????
spring.servlet.multipart.max-file-size=${MAX_FILE_SIZE:10GB}
spring.servlet.multipart.max-request-size=${MAX_REQUEST_SIZE:10GB}
