# Docker Compose 配置示例
version: '3.8'

services:
  # LLB 应用服务
  llb-app:
    build: .
    ports:
      - "8080:8080"
    environment:
      # 文件存储配置
      - FILE_UPLOAD_PATH=/app/data/uploads/
      - FILE_TEMP_PATH=/app/data/temp/
      
      # 数据库配置
      - DATABASE_URL=*****************************************************************************************************************
      - DATABASE_USERNAME=llb_user
      - DATABASE_PASSWORD=llb_password
      
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_DATABASE=0
      
      # 其他配置
      - SERVER_PORT=8080
      - MAX_FILE_SIZE=10GB
      - MAX_REQUEST_SIZE=10GB
    volumes:
      # 挂载数据目录
      - ./data/uploads:/app/data/uploads
      - ./data/temp:/app/data/temp
      # 挂载日志目录
      - ./logs:/app/logs
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    networks:
      - llb-network

  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=llb_prod
      - MYSQL_USER=llb_user
      - MYSQL_PASSWORD=llb_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    restart: unless-stopped
    networks:
      - llb-network

  # Redis 缓存服务
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - llb-network

volumes:
  mysql_data:
  redis_data:

networks:
  llb-network:
    driver: bridge
