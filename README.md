# LLB-v14 文件管理系统

一个基于 Spring Boot 的现代化文件管理系统，提供完整的文件上传、下载、分享和管理功能。

## 🚀 功能特性

### 核心功能
- **文件管理**: 支持文件上传、下载、预览、重命名、移动、复制、删除
- **文件夹管理**: 多级目录结构，支持文件夹创建、重命名、移动
- **文件分享**: 生成安全分享链接，支持密码保护和有效期设置
- **回收站**: 软删除机制，支持文件恢复和永久删除
- **批量操作**: 支持批量上传、下载、删除等操作

### 用户系统
- **用户注册/登录**: 基于 Sa-Token 的安全认证
- **权限管理**: 管理员和普通用户角色区分
- **存储配额**: 用户存储空间限制和使用量监控
- **个人中心**: 用户信息管理和密码修改

### 管理功能
- **用户管理**: 用户列表、状态管理、权限分配
- **系统监控**: 文件统计、用户活跃度、存储使用情况
- **IP管理**: IP黑白名单，访问控制
- **日志记录**: 用户行为日志和系统操作记录

### 安全特性
- **文件类型检查**: 支持文件类型白名单/黑名单配置
- **MD5校验**: 文件完整性验证和重复文件检测
- **访问控制**: 基于角色的权限控制
- **IP限制**: 支持IP黑白名单管理

## 🛠️ 技术栈

- **后端**: Spring Boot 3.5.4 + MyBatis + MySQL 8.0
- **安全**: Sa-Token (认证授权)
- **前端**: Thymeleaf + 原生 JavaScript + CSS3
- **数据库**: MySQL 8.0
- **构建工具**: Maven

## 📋 系统要求

- Java 17+
- MySQL 8.0+
- Maven 3.6+

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd v14
```

### 2. 数据库配置
创建数据库并执行初始化脚本：
```sql
CREATE DATABASE llb_v14 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

执行初始化脚本：
```bash
mysql -u root -p llb_v14 < src/main/resources/sql/init.sql
```

### 3. 配置文件
复制配置示例并修改：
```bash
cp config-examples/application-dev.properties src/main/resources/application-dev.properties
```

修改数据库连接信息：
```properties
spring.datasource.url=********************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=your_password
```

### 4. 启动应用
```bash
./mvnw spring-boot:run
```

或者使用IDE运行 `V14Application.java`

### 5. 访问系统
- 访问地址: http://localhost:8080
- 默认管理员账号: `admin` / `admin123`
- 默认测试账号: `testuser` / `admin123`

## 📁 项目结构

```
v14/
├── src/main/java/com/example/v14/
│   ├── config/          # 配置类
│   ├── controller/      # 控制器
│   ├── entity/          # 实体类
│   ├── mapper/          # MyBatis映射器
│   ├── service/         # 业务逻辑
│   └── V14Application.java
├── src/main/resources/
│   ├── mapper/          # MyBatis XML映射文件
│   ├── sql/             # 数据库脚本
│   ├── static/          # 静态资源
│   ├── templates/       # Thymeleaf模板
│   └── application.properties
├── config-examples/     # 配置示例
├── docs/               # 文档
└── pom.xml
```

## ⚙️ 配置说明

### 文件存储配置
系统支持灵活的文件存储路径配置，详见 [文件存储配置说明](docs/file-storage-config.md)

### 用户权限配置
- **管理员**: 5GB单文件上传，无存储限制，系统管理权限
- **普通用户**: 1GB单文件上传，1.5GB总存储限制

### 环境配置
- **开发环境**: 使用 `application-dev.properties`
- **生产环境**: 使用 `application-prod.properties`

## 🐳 Docker 部署

使用提供的 Docker Compose 配置：
```bash
cp config-examples/docker-compose.yml .
docker-compose up -d
```

## 📝 API 文档

主要API端点：
- `/api/auth/**` - 认证相关
- `/api/files/**` - 文件操作
- `/api/folders/**` - 文件夹操作
- `/api/shares/**` - 分享功能
- `/api/admin/**` - 管理功能

## 🔧 开发指南

### 运行测试
```bash
./mvnw test
```

### 构建项目
```bash
./mvnw clean package
```

### 代码规范
- 使用 Lombok 简化代码
- 遵循 RESTful API 设计规范
- 使用事务注解确保数据一致性

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 问题反馈

如果您遇到问题或有建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件至项目维护者

## 📊 项目状态

- ✅ 用户认证与权限管理
- ✅ 文件上传下载功能
- ✅ 文件夹管理
- ✅ 分享功能
- ✅ 回收站功能
- ✅ 管理员功能
- ✅ IP访问控制
- ✅ 统计分析功能

---

**注意**: 这是一个功能完整的文件管理系统，适用于个人或小团队使用。在生产环境部署前，请确保进行充分的安全配置和性能优化。
