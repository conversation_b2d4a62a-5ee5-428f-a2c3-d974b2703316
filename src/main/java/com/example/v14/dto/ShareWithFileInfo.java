package com.example.v14.dto;

import com.example.v14.entity.Share;
import com.example.v14.entity.FileInfo;
import com.example.v14.entity.Folder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 包含文件/文件夹信息的分享DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShareWithFileInfo {
    
    // 分享信息
    private Long id;
    private String shareCode;
    private Long fileId;
    private Long folderId;
    private Long userId;
    private Share.ShareType shareType;
    private String password;
    private LocalDateTime expireTime;
    private Integer downloadLimit;
    private Integer downloadCount;
    private Boolean isActive;
    private LocalDateTime createdTime;
    
    // 文件信息
    private String fileName;
    private String originalFileName;
    private String fileType;
    private Long fileSize;
    private Boolean fileDeleted;
    
    // 内容类型 (file 或 folder)
    private String contentType;
    
    /**
     * 从Share和FileInfo构造
     */
    public ShareWithFileInfo(Share share, FileInfo fileInfo) {
        copyShareInfo(share);
        
        if (fileInfo != null) {
            this.fileName = fileInfo.getName();
            this.originalFileName = fileInfo.getOriginalName();
            this.fileType = fileInfo.getFileType();
            this.fileSize = fileInfo.getFileSize();
            this.fileDeleted = fileInfo.getIsDeleted();
            this.contentType = "file";
        } else if (share.getFolderId() != null) {
            // 如果是文件夹分享但没有提供FileInfo，表示是文件夹分享
            this.fileName = "文件夹分享";
            this.originalFileName = "文件夹分享";
            this.fileType = "folder";
            this.fileSize = 0L;
            this.fileDeleted = false;
            this.contentType = "folder";
        } else {
            this.fileName = "文件不存在";
            this.originalFileName = "文件不存在";
            this.fileType = "unknown";
            this.fileSize = 0L;
            this.fileDeleted = true;
            this.contentType = "file";
        }
    }
    
    /**
     * 从Share和Folder构造
     */
    public ShareWithFileInfo(Share share, Folder folder) {
        copyShareInfo(share);
        
        if (folder != null) {
            this.fileName = folder.getName();
            this.originalFileName = folder.getName();
            this.fileType = "folder";
            this.fileSize = 0L; // 文件夹大小可以后续计算
            this.fileDeleted = folder.isDeleted();
            this.contentType = "folder";
        } else {
            this.fileName = "文件夹不存在";
            this.originalFileName = "文件夹不存在";
            this.fileType = "folder";
            this.fileSize = 0L;
            this.fileDeleted = true;
            this.contentType = "folder";
        }
    }
    
    /**
     * 复制分享信息
     */
    private void copyShareInfo(Share share) {
        this.id = share.getId();
        this.shareCode = share.getShareCode();
        this.fileId = share.getFileId();
        this.folderId = share.getFolderId();
        this.userId = share.getUserId();
        this.shareType = share.getShareType();
        this.password = share.getPassword();
        this.expireTime = share.getExpireTime();
        this.downloadLimit = share.getDownloadLimit();
        this.downloadCount = share.getDownloadCount();
        this.isActive = share.getIsActive();
        this.createdTime = share.getCreatedTime();
    }
    
    /**
     * 检查分享是否过期
     */
    public boolean isExpired() {
        return expireTime != null && LocalDateTime.now().isAfter(expireTime);
    }
    
    /**
     * 检查是否达到下载限制
     */
    public boolean isDownloadLimitReached() {
        return downloadLimit != null && downloadLimit != -1 && 
               downloadCount != null && downloadCount >= downloadLimit;
    }
    
    /**
     * 检查分享是否可用
     */
    public boolean isAvailable() {
        return Boolean.TRUE.equals(isActive) && !isExpired() && !isDownloadLimitReached() && !Boolean.TRUE.equals(fileDeleted);
    }
    
    /**
     * 检查是否需要密码
     */
    public boolean hasPassword() {
        return password != null && !password.trim().isEmpty();
    }
    
    /**
     * 检查是否允许下载
     */
    public boolean allowDownload() {
        return Share.ShareType.DOWNLOAD.equals(shareType);
    }
    
    /**
     * 获取剩余下载次数
     */
    public Integer getRemainingDownloads() {
        if (downloadLimit == null || downloadLimit == -1) {
            return -1; // 无限制
        }
        int current = downloadCount != null ? downloadCount : 0;
        return Math.max(0, downloadLimit - current);
    }
}
