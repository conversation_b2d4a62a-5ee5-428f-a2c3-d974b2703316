package com.example.v14.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 文件信息实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FileInfo {
    
    /**
     * 文件ID
     */
    private Long id;
    
    /**
     * 文件名
     */
    private String name;
    
    /**
     * 原始文件名
     */
    private String originalName;
    
    /**
     * 文件类型
     */
    private String fileType;
    
    /**
     * 文件大小(字节)
     */
    private Long fileSize;
    
    /**
     * 文件存储路径
     */
    private String filePath;
    
    /**
     * 所属文件夹ID(NULL表示根目录)
     */
    private Long folderId;
    
    /**
     * 所属用户ID
     */
    private Long userId;
    
    /**
     * MD5哈希值
     */
    private String md5Hash;
    
    /**
     * 下载次数
     */
    private Integer downloadCount;
    
    /**
     * 是否删除
     */
    private Boolean isDeleted;
    
    /**
     * 删除时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime deletedTime;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;

    /**
     * 便捷方法：检查文件是否被删除
     */
    public boolean isFileDeleted() {
        return Boolean.TRUE.equals(this.isDeleted);
    }

    /**
     * 便捷方法：检查文件是否激活（未删除）
     */
    public boolean isActive() {
        return !isFileDeleted();
    }
    
    /**
     * 获取文件扩展名
     */
    public String getFileExtension() {
        if (originalName == null || !originalName.contains(".")) {
            return "";
        }
        return originalName.substring(originalName.lastIndexOf(".") + 1).toLowerCase();
    }
    
    /**
     * 获取格式化的文件大小
     */
    public String getFormattedFileSize() {
        if (fileSize == null || fileSize == 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        double size = fileSize.doubleValue();
        int unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }
    
    /**
     * 检查是否为图片文件
     */
    public boolean isImage() {
        String extension = getFileExtension();
        return extension.matches("(?i)(jpg|jpeg|png|gif|bmp|webp|svg)");
    }
    
    /**
     * 检查是否为视频文件
     */
    public boolean isVideo() {
        String extension = getFileExtension();
        return extension.matches("(?i)(mp4|avi|mkv|mov|wmv|flv|webm|m4v)");
    }
    
    /**
     * 检查是否为音频文件
     */
    public boolean isAudio() {
        String extension = getFileExtension();
        return extension.matches("(?i)(mp3|wav|flac|aac|ogg|wma|m4a)");
    }
    
    /**
     * 检查是否为文档文件
     */
    public boolean isDocument() {
        String extension = getFileExtension();
        return extension.matches("(?i)(pdf|doc|docx|xls|xlsx|ppt|pptx|txt|rtf)");
    }
    
    /**
     * 检查是否可以预览
     */
    public boolean canPreview() {
        return isImage() || isVideo() || isAudio() || 
               getFileExtension().matches("(?i)(pdf|txt)");
    }
    
    /**
     * 检查文件是否在根目录
     */
    public boolean isInRoot() {
        return folderId == null || Long.valueOf(0).equals(folderId);
    }
}
