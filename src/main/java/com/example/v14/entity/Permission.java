package com.example.v14.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 权限实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Permission {
    
    /**
     * 权限ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 资源类型
     */
    private ResourceType resourceType;
    
    /**
     * 资源ID
     */
    private Long resourceId;
    
    /**
     * 权限类型
     */
    private PermissionType permissionType;
    
    /**
     * 授权用户ID
     */
    private Long grantedBy;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 资源类型枚举
     */
    public enum ResourceType {
        FILE, FOLDER
    }
    
    /**
     * 权限类型枚举
     */
    public enum PermissionType {
        READ, write, delete, share
    }
}
