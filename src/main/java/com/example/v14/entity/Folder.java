package com.example.v14.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 文件夹实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Folder {
    
    /**
     * 文件夹ID
     */
    private Long id;
    
    /**
     * 文件夹名称
     */
    private String name;
    
    /**
     * 父文件夹ID(0表示根目录)
     */
    private Long parentId;
    
    /**
     * 所属用户ID
     */
    private Long userId;
    
    /**
     * 完整路径
     */
    private String path;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 删除时间
     */
    private LocalDateTime deletedTime;

    /**
     * 检查是否为根目录
     */
    public boolean isRoot() {
        return parentId == null || parentId == 0;
    }

    /**
     * 获取文件夹层级
     */
    public int getLevel() {
        if (path == null || path.isEmpty()) {
            return 0;
        }
        return path.split("/").length - 1;
    }

    /**
     * 便捷方法：检查文件夹是否被删除
     */
    public boolean isDeleted() {
        return Boolean.TRUE.equals(this.isDeleted);
    }

    /**
     * 便捷方法：检查文件夹是否激活（未删除）
     */
    public boolean isActive() {
        return !isDeleted();
    }
}
