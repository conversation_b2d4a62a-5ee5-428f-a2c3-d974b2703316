package com.example.v14.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * IP访问记录实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IpAccessLog {
    
    /**
     * 记录ID
     */
    private Long id;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 用户ID（如果已登录）
     */
    private Long userId;
    
    /**
     * 请求URL
     */
    private String requestUrl;
    
    /**
     * 请求方法
     */
    private String requestMethod;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 来源页面
     */
    private String referer;
    
    /**
     * 访问状态：allowed(允许)、blocked(拦截)、rate_limited(限流)
     */
    private String accessStatus;
    
    /**
     * 访问结果：whitelist(白名单)、blacklist(黑名单)、normal(正常)
     */
    private String accessResult;
    
    /**
     * 地理位置信息（国家/地区）
     */
    private String location;
    
    /**
     * 请求处理时间（毫秒）
     */
    private Long processingTime;
    
    /**
     * 访问时间
     */
    private LocalDateTime accessTime;
}
