package com.example.v14.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 分享实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Share {
    
    /**
     * 分享ID
     */
    private Long id;
    
    /**
     * 分享码
     */
    private String shareCode;
    
    /**
     * 文件ID（与文件夹ID二选一）
     */
    private Long fileId;
    
    /**
     * 文件夹ID（与文件ID二选一）
     */
    private Long folderId;
    
    /**
     * 分享用户ID
     */
    private Long userId;
    
    /**
     * 分享类型
     */
    private ShareType shareType;
    
    /**
     * 访问密码
     */
    private String password;
    
    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expireTime;
    
    /**
     * 下载次数限制(-1表示无限制)
     */
    private Integer downloadLimit;
    
    /**
     * 已下载次数
     */
    private Integer downloadCount;
    
    /**
     * 是否有效
     */
    private Boolean isActive;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;
    
    /**
     * 分享类型枚举
     */
    public enum ShareType {
        VIEW, DOWNLOAD
    }
    
    /**
     * 检查分享是否过期
     */
    public boolean isExpired() {
        return expireTime != null && LocalDateTime.now().isAfter(expireTime);
    }
    
    /**
     * 检查是否达到下载限制
     */
    public boolean isDownloadLimitReached() {
        return downloadLimit != null && downloadLimit != -1 && 
               downloadCount != null && downloadCount >= downloadLimit;
    }
    
    /**
     * 检查分享是否可用
     */
    public boolean isAvailable() {
        return Boolean.TRUE.equals(isActive) && !isExpired() && !isDownloadLimitReached();
    }
    
    /**
     * 检查是否需要密码
     */
    public boolean hasPassword() {
        return password != null && !password.trim().isEmpty();
    }
    
    /**
     * 检查是否允许下载
     */
    public boolean allowDownload() {
        return ShareType.DOWNLOAD.equals(shareType);
    }
    
    /**
     * 获取剩余下载次数
     */
    public Integer getRemainingDownloads() {
        if (downloadLimit == null || downloadLimit == -1) {
            return -1; // 无限制
        }
        int current = downloadCount != null ? downloadCount : 0;
        return Math.max(0, downloadLimit - current);
    }
    
    /**
     * 检查是否为文件分享
     */
    public boolean isFileShare() {
        return fileId != null && folderId == null;
    }
    
    /**
     * 检查是否为文件夹分享
     */
    public boolean isFolderShare() {
        return folderId != null && fileId == null;
    }
    
    /**
     * 获取分享内容ID（文件ID或文件夹ID）
     */
    public Long getContentId() {
        return isFileShare() ? fileId : folderId;
    }
}
