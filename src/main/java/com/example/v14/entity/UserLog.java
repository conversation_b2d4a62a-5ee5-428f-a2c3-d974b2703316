package com.example.v14.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 用户行为日志实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserLog {
    
    /**
     * 日志ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 操作类型
     */
    private String action;
    
    /**
     * 资源类型
     */
    private String resourceType;
    
    /**
     * 资源ID
     */
    private Long resourceId;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 详细信息
     */
    private String details;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 常用操作类型常量
     */
    public static class Actions {
        public static final String LOGIN = "LOGIN";
        public static final String LOGOUT = "LOGOUT";
        public static final String UPLOAD = "UPLOAD";
        public static final String DOWNLOAD = "DOWNLOAD";
        public static final String DELETE = "DELETE";
        public static final String SHARE = "SHARE";
        public static final String CREATE_FOLDER = "CREATE_FOLDER";
        public static final String RENAME = "RENAME";
        public static final String MOVE = "MOVE";
        public static final String COPY = "COPY";
        public static final String VIEW = "VIEW";
    }
}
