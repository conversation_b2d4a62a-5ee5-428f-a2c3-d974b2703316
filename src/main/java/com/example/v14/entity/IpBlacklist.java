package com.example.v14.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * IP黑名单实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IpBlacklist {
    
    /**
     * ID
     */
    private Long id;
    
    /**
     * IP地址或IP段（支持CIDR格式，如：***********/24）
     */
    private String ipAddress;
    
    /**
     * IP类型：single(单IP) 或 range(IP段)
     */
    private String ipType;
    
    /**
     * 加入黑名单原因
     */
    private String reason;
    
    /**
     * 状态：enabled(启用) 或 disabled(禁用)
     */
    private String status;
    
    /**
     * 过期时间（null表示永久有效）
     */
    private LocalDateTime expiresAt;
    
    /**
     * 操作管理员ID
     */
    private Long createdBy;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 触发次数（被拦截的访问次数）
     */
    private Integer triggerCount;
    
    /**
     * 最后触发时间
     */
    private LocalDateTime lastTriggeredAt;
}
