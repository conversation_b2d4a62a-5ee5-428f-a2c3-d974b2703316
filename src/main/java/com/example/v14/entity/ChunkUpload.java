package com.example.v14.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * 分片上传实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChunkUpload {
    
    /**
     * 上传ID
     */
    private String uploadId;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件大小
     */
    private Long fileSize;
    
    /**
     * 文件MD5
     */
    private String fileMd5;
    
    /**
     * 分片大小
     */
    private Long chunkSize;
    
    /**
     * 总分片数
     */
    private Integer totalChunks;
    
    /**
     * 已上传的分片
     */
    private Set<Integer> uploadedChunks;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 目标文件夹ID
     */
    private Long folderId;
    
    /**
     * 临时文件路径
     */
    private String tempPath;
    
    /**
     * 上传状态
     */
    private UploadStatus status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 上传状态枚举
     */
    public enum UploadStatus {
        UPLOADING, COMPLETED, FAILED, EXPIRED
    }
    
    /**
     * 检查是否已完成
     */
    public boolean isCompleted() {
        return uploadedChunks != null && uploadedChunks.size() == totalChunks;
    }
    
    /**
     * 获取上传进度百分比
     */
    public double getProgress() {
        if (totalChunks == null || totalChunks == 0) {
            return 0.0;
        }
        if (uploadedChunks == null) {
            return 0.0;
        }
        return (double) uploadedChunks.size() / totalChunks * 100;
    }
    
    /**
     * 检查是否过期
     */
    public boolean isExpired() {
        return expireTime != null && LocalDateTime.now().isAfter(expireTime);
    }
}
