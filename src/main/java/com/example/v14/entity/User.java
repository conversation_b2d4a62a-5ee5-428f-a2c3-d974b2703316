package com.example.v14.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class User {
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 密码(加密)
     */
    private String password;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 昵称
     */
    private String nickname;
    
    /**
     * 头像路径
     */
    private String avatar;
    
    /**
     * 角色
     */
    private UserRole role;
    
    /**
     * 状态
     */
    private UserStatus status;
    
    /**
     * 已使用存储空间(字节)
     */
    private Long storageUsed;
    
    /**
     * 存储限制(字节)
     */
    private Long storageLimit;
    
    /**
     * 单文件大小限制(字节)
     */
    private Long singleFileLimit;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;
    
    /**
     * 最后登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastLoginTime;
    
    /**
     * 最后登录IP
     */
    private String lastLoginIp;
    
    /**
     * 用户角色枚举
     */
    public enum UserRole {
        ADMIN, USER
    }
    
    /**
     * 用户状态枚举
     */
    public enum UserStatus {
        ACTIVE, DISABLED, LOCKED
    }
    
    /**
     * 检查是否为管理员
     */
    public boolean isAdmin() {
        return UserRole.ADMIN.equals(this.role);
    }
    
    /**
     * 检查用户是否可用
     */
    public boolean isActive() {
        return UserStatus.ACTIVE.equals(this.status);
    }
    
    /**
     * 获取剩余存储空间
     */
    public Long getRemainingStorage() {
        if (storageLimit == null || storageLimit == -1) {
            return -1L; // 无限制
        }
        return Math.max(0, storageLimit - (storageUsed != null ? storageUsed : 0));
    }
    
    /**
     * 检查是否可以上传指定大小的文件
     */
    public boolean canUploadFile(Long fileSize) {
        if (fileSize == null || fileSize <= 0) {
            return false;
        }
        
        // 检查单文件大小限制
        if (singleFileLimit != null && singleFileLimit != -1 && fileSize > singleFileLimit) {
            return false;
        }
        
        // 检查总存储空间限制
        if (storageLimit != null && storageLimit != -1) {
            Long currentUsed = storageUsed != null ? storageUsed : 0;
            return (currentUsed + fileSize) <= storageLimit;
        }
        
        return true;
    }
}
