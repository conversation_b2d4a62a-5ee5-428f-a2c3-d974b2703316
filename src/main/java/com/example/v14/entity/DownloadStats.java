package com.example.v14.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 下载统计实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DownloadStats {
    
    /**
     * 统计ID
     */
    private Long id;
    
    /**
     * 文件ID
     */
    private Long fileId;
    
    /**
     * 下载用户ID
     */
    private Long userId;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 下载时间
     */
    private LocalDateTime downloadTime;
    
    /**
     * 文件大小
     */
    private Long fileSize;
}
