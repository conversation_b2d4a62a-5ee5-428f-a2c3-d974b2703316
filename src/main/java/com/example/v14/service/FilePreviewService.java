package com.example.v14.service;

import com.example.v14.config.FileConfig;
import com.example.v14.entity.FileInfo;
import com.example.v14.mapper.FileMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import javax.imageio.ImageIO;

/**
 * 文件预览服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FilePreviewService {

    private final FileMapper fileMapper;
    private final FileConfig fileConfig;
    
    // 支持预览的图片格式
    private static final List<String> IMAGE_TYPES = Arrays.asList(
        "image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp", "image/webp"
    );
    
    // 支持预览的文本格式
    private static final List<String> TEXT_TYPES = Arrays.asList(
        "text/plain", "text/html", "text/css", "text/javascript", 
        "application/json", "application/xml", "text/xml"
    );
    
    // 支持预览的视频格式
    private static final List<String> VIDEO_TYPES = Arrays.asList(
        "video/mp4", "video/webm", "video/ogg"
    );
    
    // 支持预览的音频格式
    private static final List<String> AUDIO_TYPES = Arrays.asList(
        "audio/mp3", "audio/wav", "audio/ogg", "audio/mpeg"
    );
    
    /**
     * 检查文件是否支持预览
     */
    public boolean isPreviewable(String fileType) {
        return IMAGE_TYPES.contains(fileType) || 
               TEXT_TYPES.contains(fileType) || 
               VIDEO_TYPES.contains(fileType) || 
               AUDIO_TYPES.contains(fileType);
    }
    
    /**
     * 获取文件预览类型
     */
    public String getPreviewType(String fileType) {
        if (IMAGE_TYPES.contains(fileType)) {
            return "image";
        } else if (TEXT_TYPES.contains(fileType)) {
            return "text";
        } else if (VIDEO_TYPES.contains(fileType)) {
            return "video";
        } else if (AUDIO_TYPES.contains(fileType)) {
            return "audio";
        }
        return "unsupported";
    }
    
    /**
     * 预览文件
     */
    public void previewFile(Long fileId, HttpServletResponse response) throws IOException {
        FileInfo fileInfo = fileMapper.findById(fileId);
        if (fileInfo == null) {
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件不存在");
            return;
        }
        
        if (Boolean.TRUE.equals(fileInfo.getIsDeleted())) {
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件已删除");
            return;
        }
        
        String fileType = fileInfo.getFileType();
        if (!isPreviewable(fileType)) {
            response.sendError(HttpServletResponse.SC_UNSUPPORTED_MEDIA_TYPE, "文件类型不支持预览");
            return;
        }
        
        Path filePath = Paths.get(fileConfig.getUploadPath(), fileInfo.getFilePath());
        if (!Files.exists(filePath)) {
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件不存在");
            return;
        }
        
        // 设置响应头
        response.setContentType(fileType);
        response.setHeader("Content-Disposition", "inline; filename=\"" + fileInfo.getOriginalName() + "\"");
        response.setContentLengthLong(fileInfo.getFileSize());
        
        // 支持范围请求（用于视频播放）
        String range = response.getHeader("Range");
        if (range != null && (VIDEO_TYPES.contains(fileType) || AUDIO_TYPES.contains(fileType))) {
            handleRangeRequest(filePath, range, response);
        } else {
            // 普通文件传输
            try (InputStream inputStream = Files.newInputStream(filePath);
                 OutputStream outputStream = response.getOutputStream()) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }
        }
        
        log.info("文件预览: fileId={}, fileName={}", fileId, fileInfo.getOriginalName());
    }
    
    /**
     * 处理范围请求（用于视频/音频流式传输）
     */
    private void handleRangeRequest(Path filePath, String range, HttpServletResponse response) throws IOException {
        long fileSize = Files.size(filePath);
        
        // 解析Range头
        String[] ranges = range.replace("bytes=", "").split("-");
        long start = Long.parseLong(ranges[0]);
        long end = ranges.length > 1 && !ranges[1].isEmpty() ? 
                   Long.parseLong(ranges[1]) : fileSize - 1;
        
        // 确保范围有效
        if (start >= fileSize || end >= fileSize || start > end) {
            response.setStatus(HttpServletResponse.SC_REQUESTED_RANGE_NOT_SATISFIABLE);
            response.setHeader("Content-Range", "bytes */" + fileSize);
            return;
        }
        
        long contentLength = end - start + 1;
        
        // 设置响应头
        response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
        response.setHeader("Content-Range", "bytes " + start + "-" + end + "/" + fileSize);
        response.setHeader("Accept-Ranges", "bytes");
        response.setContentLengthLong(contentLength);
        
        // 传输指定范围的数据
        try (RandomAccessFile randomAccessFile = new RandomAccessFile(filePath.toFile(), "r");
             OutputStream outputStream = response.getOutputStream()) {
            
            randomAccessFile.seek(start);
            
            byte[] buffer = new byte[8192];
            long remaining = contentLength;
            
            while (remaining > 0) {
                int bytesToRead = (int) Math.min(buffer.length, remaining);
                int bytesRead = randomAccessFile.read(buffer, 0, bytesToRead);
                
                if (bytesRead == -1) {
                    break;
                }
                
                outputStream.write(buffer, 0, bytesRead);
                remaining -= bytesRead;
            }
            
            outputStream.flush();
        }
    }
    
    /**
     * 获取文本文件内容（用于文本预览）
     */
    public String getTextContent(Long fileId, int maxLines) throws IOException {
        FileInfo fileInfo = fileMapper.findById(fileId);
        if (fileInfo == null || Boolean.TRUE.equals(fileInfo.getIsDeleted())) {
            throw new RuntimeException("文件不存在");
        }
        
        String fileType = fileInfo.getFileType();
        if (!TEXT_TYPES.contains(fileType)) {
            throw new RuntimeException("文件类型不支持文本预览");
        }
        
        Path filePath = Paths.get(fileConfig.getUploadPath(), fileInfo.getFilePath());
        if (!Files.exists(filePath)) {
            throw new RuntimeException("文件不存在");
        }
        
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = Files.newBufferedReader(filePath)) {
            String line;
            int lineCount = 0;
            
            while ((line = reader.readLine()) != null && lineCount < maxLines) {
                content.append(line).append("\n");
                lineCount++;
            }
            
            if (lineCount >= maxLines) {
                content.append("... (文件内容过长，仅显示前").append(maxLines).append("行)");
            }
        }
        
        return content.toString();
    }
    
    /**
     * 生成缩略图
     */
    public void generateThumbnail(Long fileId, int width, int height, HttpServletResponse response) throws IOException {
        FileInfo fileInfo = fileMapper.findById(fileId);
        if (fileInfo == null || Boolean.TRUE.equals(fileInfo.getIsDeleted())) {
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件不存在");
            return;
        }

        String fileType = fileInfo.getFileType();
        if (!IMAGE_TYPES.contains(fileType)) {
            response.sendError(HttpServletResponse.SC_UNSUPPORTED_MEDIA_TYPE, "文件类型不支持缩略图");
            return;
        }

        Path filePath = Paths.get(fileConfig.getUploadPath(), fileInfo.getFilePath());
        if (!Files.exists(filePath)) {
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件不存在");
            return;
        }

        try {
            // 生成缩略图
            BufferedImage thumbnail = generateThumbnailImage(filePath.toFile(), width, height);

            // 设置响应头
            response.setContentType("image/jpeg");
            response.setHeader("Cache-Control", "max-age=3600"); // 缓存1小时
            response.setHeader("Content-Disposition", "inline; filename=\"thumbnail_" + fileInfo.getOriginalName() + "\"");

            // 输出缩略图
            ImageIO.write(thumbnail, "jpeg", response.getOutputStream());
            response.getOutputStream().flush();

        } catch (Exception e) {
            log.error("生成缩略图失败: fileId={}, error={}", fileId, e.getMessage(), e);
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "生成缩略图失败");
        }
    }

    /**
     * 生成缩略图图像
     */
    private BufferedImage generateThumbnailImage(File sourceFile, int width, int height) throws IOException {
        try {
            // 使用Thumbnailator库生成高质量缩略图
            return Thumbnails.of(sourceFile)
                    .size(width, height)
                    .keepAspectRatio(true)
                    .outputQuality(0.8)
                    .asBufferedImage();
        } catch (Exception e) {
            log.error("使用Thumbnailator生成缩略图失败，尝试使用原生方法: {}", e.getMessage());

            // 备用方案：使用Java原生ImageIO
            BufferedImage originalImage = ImageIO.read(sourceFile);
            if (originalImage == null) {
                throw new IOException("无法读取图像文件");
            }

            // 计算缩放比例，保持宽高比
            double scaleX = (double) width / originalImage.getWidth();
            double scaleY = (double) height / originalImage.getHeight();
            double scale = Math.min(scaleX, scaleY);

            int newWidth = (int) (originalImage.getWidth() * scale);
            int newHeight = (int) (originalImage.getHeight() * scale);

            // 创建缩略图
            BufferedImage thumbnail = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
            thumbnail.createGraphics().drawImage(originalImage.getScaledInstance(newWidth, newHeight, java.awt.Image.SCALE_SMOOTH), 0, 0, null);

            return thumbnail;
        }
    }
}
