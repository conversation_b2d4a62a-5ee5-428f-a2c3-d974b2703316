package com.example.v14.service;

import com.example.v14.entity.Permission;
import com.example.v14.entity.User;
import com.example.v14.mapper.PermissionMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 权限管理服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionService {
    
    private final PermissionMapper permissionMapper;
    
    /**
     * 授予权限
     */
    @Transactional
    public void grantPermission(Long userId, Permission.ResourceType resourceType, Long resourceId, 
                               Permission.PermissionType permissionType, Long grantedBy) {
        // 检查权限是否已存在
        if (hasPermission(userId, resourceType, resourceId, permissionType)) {
            log.warn("权限已存在: userId={}, resourceType={}, resourceId={}, permissionType={}", 
                    userId, resourceType, resourceId, permissionType);
            return;
        }
        
        Permission permission = Permission.builder()
                .userId(userId)
                .resourceType(resourceType)
                .resourceId(resourceId)
                .permissionType(permissionType)
                .grantedBy(grantedBy)
                .createdTime(LocalDateTime.now())
                .build();
        
        permissionMapper.insert(permission);
        log.info("权限授予成功: userId={}, resourceType={}, resourceId={}, permissionType={}", 
                userId, resourceType, resourceId, permissionType);
    }
    
    /**
     * 撤销权限
     */
    @Transactional
    public void revokePermission(Long userId, Permission.ResourceType resourceType, Long resourceId, 
                                Permission.PermissionType permissionType) {
        int count = permissionMapper.deleteSpecificPermission(userId, resourceType, resourceId, permissionType);
        if (count > 0) {
            log.info("权限撤销成功: userId={}, resourceType={}, resourceId={}, permissionType={}", 
                    userId, resourceType, resourceId, permissionType);
        } else {
            log.warn("权限不存在，撤销失败: userId={}, resourceType={}, resourceId={}, permissionType={}", 
                    userId, resourceType, resourceId, permissionType);
        }
    }
    
    /**
     * 检查用户是否有特定权限
     */
    public boolean hasPermission(Long userId, Permission.ResourceType resourceType, Long resourceId, 
                                Permission.PermissionType permissionType) {
        return permissionMapper.hasPermission(userId, resourceType, resourceId, permissionType) > 0;
    }
    
    /**
     * 检查用户是否有资源的任意权限
     */
    public boolean hasAnyPermission(Long userId, Permission.ResourceType resourceType, Long resourceId) {
        return permissionMapper.hasAnyPermission(userId, resourceType, resourceId) > 0;
    }
    
    /**
     * 检查用户是否可以读取资源
     */
    public boolean canRead(Long userId, Permission.ResourceType resourceType, Long resourceId) {
        return hasPermission(userId, resourceType, resourceId, Permission.PermissionType.READ);
    }
    
    /**
     * 检查用户是否可以写入资源
     */
    public boolean canWrite(Long userId, Permission.ResourceType resourceType, Long resourceId) {
        return hasPermission(userId, resourceType, resourceId, Permission.PermissionType.write);
    }
    
    /**
     * 检查用户是否可以删除资源
     */
    public boolean canDelete(Long userId, Permission.ResourceType resourceType, Long resourceId) {
        return hasPermission(userId, resourceType, resourceId, Permission.PermissionType.delete);
    }
    
    /**
     * 检查用户是否可以分享资源
     */
    public boolean canShare(Long userId, Permission.ResourceType resourceType, Long resourceId) {
        return hasPermission(userId, resourceType, resourceId, Permission.PermissionType.share);
    }
    
    /**
     * 获取用户的所有权限
     */
    public List<Permission> getUserPermissions(Long userId) {
        return permissionMapper.findByUserId(userId);
    }
    
    /**
     * 获取用户的文件权限
     */
    public List<Permission> getUserFilePermissions(Long userId) {
        return permissionMapper.findFilePermissionsByUser(userId);
    }
    
    /**
     * 获取用户的文件夹权限
     */
    public List<Permission> getUserFolderPermissions(Long userId) {
        return permissionMapper.findFolderPermissionsByUser(userId);
    }
    
    /**
     * 获取资源的所有权限
     */
    public List<Permission> getResourcePermissions(Permission.ResourceType resourceType, Long resourceId) {
        return permissionMapper.findByResource(resourceType, resourceId);
    }
    
    /**
     * 获取文件的所有权限
     */
    public List<Permission> getFilePermissions(Long fileId) {
        return permissionMapper.findPermissionsByFile(fileId);
    }
    
    /**
     * 获取文件夹的所有权限
     */
    public List<Permission> getFolderPermissions(Long folderId) {
        return permissionMapper.findPermissionsByFolder(folderId);
    }
    
    /**
     * 批量授予权限
     */
    @Transactional
    public void batchGrantPermissions(List<Long> userIds, Permission.ResourceType resourceType, Long resourceId, 
                                     List<Permission.PermissionType> permissionTypes, Long grantedBy) {
        List<Permission> permissions = new java.util.ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (Long userId : userIds) {
            for (Permission.PermissionType permissionType : permissionTypes) {
                // 检查权限是否已存在
                if (!hasPermission(userId, resourceType, resourceId, permissionType)) {
                    Permission permission = Permission.builder()
                            .userId(userId)
                            .resourceType(resourceType)
                            .resourceId(resourceId)
                            .permissionType(permissionType)
                            .grantedBy(grantedBy)
                            .createdTime(now)
                            .build();
                    permissions.add(permission);
                }
            }
        }
        
        if (!permissions.isEmpty()) {
            // 更改为循环调用单个插入方法
            int successCount = 0;
            for (Permission permission : permissions) {
                try {
                    int result = permissionMapper.insert(permission);
                    if (result > 0) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("插入权限失败: userId={}, resourceType={}, resourceId={}, error={}", 
                            permission.getUserId(), permission.getResourceType(), permission.getResourceId(), e.getMessage());
                }
            }
            log.info("批量授予权限完成: 成功{}/{}", successCount, permissions.size());
        }
    }
    
    /**
     * 批量撤销权限
     */
    @Transactional
    public void batchRevokePermissions(List<Long> permissionIds) {
        if (!permissionIds.isEmpty()) {
            // 更改为循环调用单个删除方法
            int successCount = 0;
            for (Long permissionId : permissionIds) {
                try {
                    int result = permissionMapper.deleteById(permissionId);
                    if (result > 0) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("删除权限失败: permissionId={}, error={}", permissionId, e.getMessage());
                }
            }
            log.info("批量撤销权限完成: 成功{}/{}", successCount, permissionIds.size());
        }
    }
    
    /**
     * 删除用户的所有权限
     */
    @Transactional
    public void deleteUserPermissions(Long userId) {
        int count = permissionMapper.deleteByUserId(userId);
        log.info("删除用户所有权限成功: userId={}, 删除数量={}", userId, count);
    }
    
    /**
     * 删除资源的所有权限
     */
    @Transactional
    public void deleteResourcePermissions(Permission.ResourceType resourceType, Long resourceId) {
        int count = permissionMapper.deleteByResource(resourceType, resourceId);
        log.info("删除资源所有权限成功: resourceType={}, resourceId={}, 删除数量={}", 
                resourceType, resourceId, count);
    }
    
    /**
     * 复制权限（从一个资源复制到另一个资源）
     */
    @Transactional
    public void copyPermissions(Permission.ResourceType fromResourceType, Long fromResourceId,
                               Permission.ResourceType toResourceType, Long toResourceId, Long grantedBy) {
        List<Permission> sourcePermissions = permissionMapper.findByResource(fromResourceType, fromResourceId);
        
        if (!sourcePermissions.isEmpty()) {
            List<Permission> newPermissions = new java.util.ArrayList<>();
            LocalDateTime now = LocalDateTime.now();
            
            for (Permission sourcePermission : sourcePermissions) {
                Permission newPermission = Permission.builder()
                        .userId(sourcePermission.getUserId())
                        .resourceType(toResourceType)
                        .resourceId(toResourceId)
                        .permissionType(sourcePermission.getPermissionType())
                        .grantedBy(grantedBy)
                        .createdTime(now)
                        .build();
                newPermissions.add(newPermission);
            }
            
            // 更改为循环调用单个插入方法
            int successCount = 0;
            for (Permission newPermission : newPermissions) {
                try {
                    int result = permissionMapper.insert(newPermission);
                    if (result > 0) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("插入复制权限失败: userId={}, resourceType={}, resourceId={}, error={}", 
                            newPermission.getUserId(), newPermission.getResourceType(), newPermission.getResourceId(), e.getMessage());
                }
            }
            log.info("权限复制完成: 从 {}:{} 复制到 {}:{}, 成功{}/{}",
                    fromResourceType, fromResourceId, toResourceType, toResourceId, successCount, newPermissions.size());
        }
    }
    
    /**
     * 获取权限统计信息
     */
    public Map<String, Object> getPermissionStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总权限数
        long totalPermissions = permissionMapper.countAll();
        stats.put("totalPermissions", totalPermissions);
        
        // 按权限类型统计
        List<Object> permissionTypeStats = permissionMapper.countByPermissionTypeGroup();
        stats.put("permissionTypeStats", permissionTypeStats);
        
        // 按资源类型统计
        List<Object> resourceTypeStats = permissionMapper.countByResourceTypeGroup();
        stats.put("resourceTypeStats", resourceTypeStats);
        
        // 最近授予的权限
        List<Permission> recentPermissions = permissionMapper.findRecent(10);
        stats.put("recentPermissions", recentPermissions);
        
        return stats;
    }
    
    /**
     * 检查权限冲突
     */
    public boolean hasPermissionConflict(Long userId, Permission.ResourceType resourceType, Long resourceId) {
        List<Permission> userPermissions = permissionMapper.findByUserAndResource(userId, resourceType, resourceId);
        
        // 检查是否同时有读写权限但没有删除权限等逻辑冲突
        boolean hasRead = userPermissions.stream().anyMatch(p -> p.getPermissionType() == Permission.PermissionType.READ);
        boolean hasWrite = userPermissions.stream().anyMatch(p -> p.getPermissionType() == Permission.PermissionType.write);
        boolean hasDelete = userPermissions.stream().anyMatch(p -> p.getPermissionType() == Permission.PermissionType.delete);
        
        // 如果有写权限但没有读权限，可能存在逻辑问题
        return hasWrite && !hasRead;
    }
    
    /**
     * 修复权限冲突
     */
    @Transactional
    public void fixPermissionConflicts(Long userId, Permission.ResourceType resourceType, Long resourceId, Long grantedBy) {
        if (hasPermissionConflict(userId, resourceType, resourceId)) {
            // 如果有写权限但没有读权限，自动授予读权限
            if (hasPermission(userId, resourceType, resourceId, Permission.PermissionType.write) &&
                !hasPermission(userId, resourceType, resourceId, Permission.PermissionType.READ)) {
                grantPermission(userId, resourceType, resourceId, Permission.PermissionType.READ, grantedBy);
                log.info("修复权限冲突: 为用户 {} 自动授予读权限", userId);
            }
        }
    }
}
