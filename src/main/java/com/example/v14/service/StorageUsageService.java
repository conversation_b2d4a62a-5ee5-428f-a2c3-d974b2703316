package com.example.v14.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;

/**
 * 存储使用量管理服务
 * 专门处理用户存储使用量的更新，与文件上传流程解耦
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StorageUsageService {

    private final UserService userService;

    /**
     * 异步增加存储使用量
     * 完全独立的异步操作，不影响主业务流程
     */
    @Async("taskExecutor")
    public CompletableFuture<Void> increaseStorageUsageAsync(Long userId, Long size) {
        return CompletableFuture.runAsync(() -> {
            try {
                // 短暂延迟，确保主事务已提交
                Thread.sleep(200);
                
                // 简单直接的更新，不使用复杂的重试机制
                increaseStorageUsageSimple(userId, size);
                
                log.debug("存储使用量异步更新成功: userId={}, size={}", userId, size);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("存储使用量更新被中断: userId={}, size={}", userId, size);
            } catch (Exception e) {
                log.warn("存储使用量异步更新失败，将在后台重试: userId={}, size={}, error={}", 
                        userId, size, e.getMessage());
                // 可以在这里添加重试队列或者定时任务来处理失败的更新
            }
        });
    }

    /**
     * 简单的存储使用量更新
     * 使用独立事务，超时时间短
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, timeout = 5)
    public void increaseStorageUsageSimple(Long userId, Long size) {
        try {
            userService.increaseStorageUsed(userId, size);
        } catch (Exception e) {
            log.error("存储使用量更新失败: userId={}, size={}", userId, size, e);
            throw e;
        }
    }

    /**
     * 异步减少存储使用量
     */
    @Async("taskExecutor")
    public CompletableFuture<Void> decreaseStorageUsageAsync(Long userId, Long size) {
        return CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(200);
                decreaseStorageUsageSimple(userId, size);
                log.debug("存储使用量异步减少成功: userId={}, size={}", userId, size);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("存储使用量减少被中断: userId={}, size={}", userId, size);
            } catch (Exception e) {
                log.warn("存储使用量异步减少失败: userId={}, size={}, error={}", 
                        userId, size, e.getMessage());
            }
        });
    }

    /**
     * 简单的存储使用量减少
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, timeout = 5)
    public void decreaseStorageUsageSimple(Long userId, Long size) {
        try {
            userService.decreaseStorageUsed(userId, size);
        } catch (Exception e) {
            log.error("存储使用量减少失败: userId={}, size={}", userId, size, e);
            throw e;
        }
    }

    /**
     * 批量更新存储使用量
     * 用于批量上传完成后的一次性更新
     */
    @Async("taskExecutor")
    public CompletableFuture<Void> batchUpdateStorageUsageAsync(Long userId, Long totalSize) {
        return CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(300); // 更长的延迟，确保所有文件都已保存
                increaseStorageUsageSimple(userId, totalSize);
                log.info("批量存储使用量更新成功: userId={}, totalSize={}", userId, totalSize);
            } catch (Exception e) {
                log.error("批量存储使用量更新失败: userId={}, totalSize={}", userId, totalSize, e);
            }
        });
    }
}
