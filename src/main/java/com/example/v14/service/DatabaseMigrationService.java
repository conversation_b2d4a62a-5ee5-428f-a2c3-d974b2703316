package com.example.v14.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

/**
 * 数据库迁移服务
 * 用于处理数据库结构的升级和修复
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Order(1) // 确保在应用启动时优先执行
public class DatabaseMigrationService implements CommandLineRunner {
    
    private final JdbcTemplate jdbcTemplate;
    
    @Override
    public void run(String... args) throws Exception {
        log.info("开始执行数据库迁移检查...");
        
        try {
            // 检查并修复下载统计表的外键约束
            fixDownloadStatsConstraint();
            
            log.info("数据库迁移检查完成");
        } catch (Exception e) {
            log.error("数据库迁移执行失败", e);
            // 不抛出异常，避免影响应用启动
        }
    }
    
    /**
     * 修复下载统计表的外键约束
     */
    private void fixDownloadStatsConstraint() {
        try {
            // 检查当前外键约束
            String checkConstraintSql = """
                SELECT CONSTRAINT_NAME, DELETE_RULE 
                FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS 
                WHERE CONSTRAINT_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'download_stats' 
                AND REFERENCED_TABLE_NAME = 'files'
                """;
            
            var constraints = jdbcTemplate.queryForList(checkConstraintSql);
            
            boolean needsFix = false;
            String constraintName = null;
            
            for (var constraint : constraints) {
                String deleteRule = (String) constraint.get("DELETE_RULE");
                constraintName = (String) constraint.get("CONSTRAINT_NAME");
                
                if ("CASCADE".equals(deleteRule)) {
                    needsFix = true;
                    log.info("发现需要修复的外键约束: {}, DELETE_RULE: {}", constraintName, deleteRule);
                    break;
                }
            }
            
            if (needsFix && constraintName != null) {
                log.info("开始修复下载统计表外键约束...");
                
                // 1. 删除现有约束
                String dropConstraintSql = "ALTER TABLE download_stats DROP FOREIGN KEY " + constraintName;
                jdbcTemplate.execute(dropConstraintSql);
                log.info("已删除旧的外键约束: {}", constraintName);
                
                // 2. 修改列定义，允许NULL
                String modifyColumnSql = "ALTER TABLE download_stats MODIFY COLUMN file_id BIGINT NULL COMMENT '文件ID（文件删除后为NULL）'";
                jdbcTemplate.execute(modifyColumnSql);
                log.info("已修改file_id列定义，允许NULL值");
                
                // 3. 添加新的外键约束
                String addConstraintSql = """
                    ALTER TABLE download_stats 
                    ADD CONSTRAINT fk_download_stats_file_id 
                    FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE SET NULL
                    """;
                jdbcTemplate.execute(addConstraintSql);
                log.info("已添加新的外键约束，使用ON DELETE SET NULL");
                
                log.info("下载统计表外键约束修复完成");
            } else {
                log.info("下载统计表外键约束无需修复");
            }
            
        } catch (Exception e) {
            log.error("修复下载统计表外键约束失败", e);
        }
    }
}
