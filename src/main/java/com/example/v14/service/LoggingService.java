package com.example.v14.service;

import com.example.v14.entity.UserLog;
import com.example.v14.entity.IpAccessLog;
import com.example.v14.mapper.UserLogMapper;
import com.example.v14.mapper.IpAccessLogMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 日志记录服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LoggingService {
    
    private final UserLogMapper userLogMapper;
    private final IpAccessLogMapper ipAccessLogMapper;
    private final IpLocationService ipLocationService;
    
    /**
     * 记录用户行为日志
     */
    @Transactional
    public void logUserAction(Long userId, String action, String resourceType, Long resourceId, 
                             String ipAddress, String userAgent, String details) {
        try {
            UserLog userLog = UserLog.builder()
                    .userId(userId)
                    .action(action)
                    .resourceType(resourceType)
                    .resourceId(resourceId)
                    .ipAddress(ipAddress)
                    .userAgent(userAgent)
                    .details(details)
                    .createdTime(LocalDateTime.now())
                    .build();
            
            userLogMapper.insert(userLog);
            log.debug("用户行为日志记录成功: userId={}, action={}", userId, action);
        } catch (Exception e) {
            log.error("记录用户行为日志失败", e);
        }
    }
    
    /**
     * 记录用户行为日志（从HttpServletRequest获取信息）
     */
    @Transactional
    public void logUserAction(Long userId, String action, String resourceType, Long resourceId, 
                             HttpServletRequest request, String details) {
        String ipAddress = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        logUserAction(userId, action, resourceType, resourceId, ipAddress, userAgent, details);
    }
    
    /**
     * 记录IP访问日志
     */
    @Transactional
    public void logIpAccess(String ipAddress, Long userId, String requestUrl, String requestMethod, 
                           String userAgent) {
        try {
            long startTime = System.currentTimeMillis();
            
            IpAccessLog ipAccessLog = IpAccessLog.builder()
                    .ipAddress(ipAddress)
                    .userId(userId)
                    .requestUrl(requestUrl)
                    .requestMethod(requestMethod)
                    .userAgent(userAgent)
                    .accessTime(LocalDateTime.now())
                    .accessStatus("ALLOWED")
                    .accessResult("SUCCESS")
                    .processingTime(System.currentTimeMillis() - startTime)
                    .build();
            
            ipAccessLogMapper.insert(ipAccessLog);
            log.debug("IP访问日志记录成功: ip={}, url={}", ipAddress, requestUrl);
        } catch (Exception e) {
            log.error("记录IP访问日志失败", e);
        }
    }
    
    /**
     * 记录IP访问日志（从HttpServletRequest获取信息）
     */
    @Transactional
    public void logIpAccess(HttpServletRequest request, Long userId) {
        String ipAddress = getClientIpAddress(request);
        String requestUrl = request.getRequestURI();
        String requestMethod = request.getMethod();
        String userAgent = request.getHeader("User-Agent");
        String referer = request.getHeader("Referer");
        
        logIpAccessWithReferer(ipAddress, userId, requestUrl, requestMethod, userAgent, referer, "ALLOWED", "SUCCESS");
    }
    
    /**
     * 记录IP访问被拒绝日志
     */
    @Transactional
    public void logIpAccessDenied(HttpServletRequest request, String reason) {
        String ipAddress = getClientIpAddress(request);
        String requestUrl = request.getRequestURI();
        String requestMethod = request.getMethod();
        String userAgent = request.getHeader("User-Agent");
        String referer = request.getHeader("Referer");
        
        logIpAccessWithReferer(ipAddress, null, requestUrl, requestMethod, userAgent, referer, "BLOCKED", reason);
    }
    
    /**
     * 记录完整的IP访问日志（包含地理位置）
     */
    @Transactional
    public void logIpAccessWithReferer(String ipAddress, Long userId, String requestUrl, String requestMethod, 
                                     String userAgent, String referer, String accessStatus, String accessResult) {
        try {
            long startTime = System.currentTimeMillis();
            
            // 异步获取地理位置信息
            String location = "未知";
            try {
                location = ipLocationService.getIpLocation(ipAddress);
            } catch (Exception e) {
                log.debug("获取IP地理位置失败: {} - {}", ipAddress, e.getMessage());
            }
            
            IpAccessLog ipAccessLog = IpAccessLog.builder()
                    .ipAddress(ipAddress)
                    .userId(userId)
                    .requestUrl(requestUrl)
                    .requestMethod(requestMethod)
                    .userAgent(userAgent)
                    .referer(referer)
                    .accessTime(LocalDateTime.now())
                    .accessStatus(accessStatus)
                    .accessResult(accessResult)
                    .location(location)
                    .processingTime(System.currentTimeMillis() - startTime)
                    .build();
            
            ipAccessLogMapper.insert(ipAccessLog);
            log.debug("IP访问日志记录成功: ip={}, url={}, status={}, location={}", 
                     ipAddress, requestUrl, accessStatus, location);
        } catch (Exception e) {
            log.error("记录IP访问日志失败", e);
        }
    }
    
    /**
     * 记录登录日志
     */
    @Transactional
    public void logLogin(Long userId, HttpServletRequest request, boolean success) {
        String action = success ? UserLog.Actions.LOGIN : "LOGIN_FAILED";
        String details = success ? "登录成功" : "登录失败";
        logUserAction(userId, action, "USER", userId, request, details);
    }
    
    /**
     * 记录登出日志
     */
    @Transactional
    public void logLogout(Long userId, HttpServletRequest request) {
        logUserAction(userId, UserLog.Actions.LOGOUT, "USER", userId, request, "用户登出");
    }
    
    /**
     * 记录文件上传日志
     */
    @Transactional
    public void logFileUpload(Long userId, Long fileId, String fileName, Long fileSize, HttpServletRequest request) {
        String details = String.format("上传文件: %s, 大小: %d bytes", fileName, fileSize);
        logUserAction(userId, UserLog.Actions.UPLOAD, "FILE", fileId, request, details);
    }
    
    /**
     * 记录文件下载日志
     */
    @Transactional
    public void logFileDownload(Long userId, Long fileId, String fileName, HttpServletRequest request) {
        String details = String.format("下载文件: %s", fileName);
        logUserAction(userId, UserLog.Actions.DOWNLOAD, "FILE", fileId, request, details);
    }
    
    /**
     * 记录文件删除日志
     */
    @Transactional
    public void logFileDelete(Long userId, Long fileId, String fileName, HttpServletRequest request) {
        String details = String.format("删除文件: %s", fileName);
        logUserAction(userId, UserLog.Actions.DELETE, "FILE", fileId, request, details);
    }
    
    /**
     * 记录文件分享日志
     */
    @Transactional
    public void logFileShare(Long userId, Long fileId, String fileName, String shareCode, HttpServletRequest request) {
        String details = String.format("分享文件: %s, 分享码: %s", fileName, shareCode);
        logUserAction(userId, UserLog.Actions.SHARE, "FILE", fileId, request, details);
    }
    
    /**
     * 记录文件夹创建日志
     */
    @Transactional
    public void logFolderCreate(Long userId, Long folderId, String folderName, HttpServletRequest request) {
        String details = String.format("创建文件夹: %s", folderName);
        logUserAction(userId, UserLog.Actions.CREATE_FOLDER, "FOLDER", folderId, request, details);
    }
    
    /**
     * 获取用户行为日志
     */
    public List<UserLog> getUserLogs(Long userId, int page, int size) {
        int offset = (page - 1) * size;
        return userLogMapper.findByUserId(userId, offset, size);
    }
    
    /**
     * 获取IP访问日志
     */
    public List<IpAccessLog> getIpAccessLogs(String ipAddress, int page, int size) {
        int offset = (page - 1) * size;
        return ipAccessLogMapper.findByIpAddress(ipAddress, offset, size);
    }
    
    /**
     * 获取用户行为统计
     */
    public Map<String, Object> getUserActionStats(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> stats = new HashMap<>();
        
        // 总操作次数
        long totalActions = userLogMapper.countByUserAndTimeRange(userId, startTime, endTime);
        stats.put("totalActions", totalActions);
        
        // 最近操作记录
        List<UserLog> recentActions = userLogMapper.findRecentByUser(userId, 10);
        stats.put("recentActions", recentActions);
        
        return stats;
    }
    
    /**
     * 获取IP访问统计
     */
    public Map<String, Object> getIpAccessStats(String ipAddress, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> stats = new HashMap<>();
        
        // 总访问次数
        long totalAccess = ipAccessLogMapper.countByIpAndTimeRange(ipAddress, startTime, endTime);
        stats.put("totalAccess", totalAccess);
        
        // 最近访问记录
        List<IpAccessLog> recentAccess = ipAccessLogMapper.findByIpAddress(ipAddress, 0, 10);
        stats.put("recentAccess", recentAccess);
        
        return stats;
    }
    
    /**
     * 获取系统行为统计
     */
    public Map<String, Object> getSystemActionStats(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> stats = new HashMap<>();
        
        // 按操作类型统计
        List<Object> actionStats = userLogMapper.countByActionGroup();
        stats.put("actionStats", actionStats);
        
        // 按日期统计
        List<Object> dailyStats = userLogMapper.countByDate(startTime, endTime);
        stats.put("dailyStats", dailyStats);
        
        // 按小时统计
        List<Object> hourlyStats = userLogMapper.countByHour(startTime, endTime);
        stats.put("hourlyStats", hourlyStats);
        
        // 最活跃用户
        List<Object> activeUsers = userLogMapper.findMostActiveUsers(startTime, endTime, 10);
        stats.put("activeUsers", activeUsers);
        
        return stats;
    }
    
    /**
     * 清理过期日志
     */
    @Transactional
    public void cleanupOldLogs(int days) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(days);
        
        int userLogCount = userLogMapper.deleteOldLogs(cutoffTime);
        int ipLogCount = ipAccessLogMapper.deleteOldRecords(cutoffTime);
        
        log.info("清理过期日志完成: 用户日志 {} 条, IP访问日志 {} 条", userLogCount, ipLogCount);
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
