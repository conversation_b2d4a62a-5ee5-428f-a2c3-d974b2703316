package com.example.v14.service;

import com.example.v14.entity.IpBlacklist;
import com.example.v14.entity.IpWhitelist;
import com.example.v14.entity.IpAccessLog;
import com.example.v14.mapper.IpBlacklistMapper;
import com.example.v14.mapper.IpWhitelistMapper;
import com.example.v14.mapper.IpAccessLogMapper;
import com.example.v14.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * IP管理服务类（增强版本）
 */
@Slf4j
@Service
public class IpManagementService {
    
    private final IpBlacklistMapper ipBlacklistMapper;
    private final IpWhitelistMapper ipWhitelistMapper;
    private final IpAccessLogMapper ipAccessLogMapper;
    
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;
    
    public IpManagementService(IpBlacklistMapper ipBlacklistMapper, 
                              IpWhitelistMapper ipWhitelistMapper,
                              IpAccessLogMapper ipAccessLogMapper) {
        this.ipBlacklistMapper = ipBlacklistMapper;
        this.ipWhitelistMapper = ipWhitelistMapper;
        this.ipAccessLogMapper = ipAccessLogMapper;
    }
    
    // Redis缓存KEY常量
    private static final String BLACKLIST_CACHE_KEY = "ip:blacklist:active";
    private static final String WHITELIST_CACHE_KEY = "ip:whitelist:active";
    private static final String IP_CHECK_CACHE_KEY = "ip:check:";
    private static final String RATE_LIMIT_KEY = "ip:rate:";
    
    // 缓存过期时间
    private static final long LIST_CACHE_TTL = 300; // 5分钟
    private static final long IP_CHECK_CACHE_TTL = 60; // 1分钟
    private static final long RATE_LIMIT_TTL = 3600; // 1小时
    
    // 内存缓存，用于快速IP匹配（作为Redis的备用）
    private final Map<String, List<IpBlacklist>> blacklistCache = new ConcurrentHashMap<>();
    private final Map<String, List<IpWhitelist>> whitelistCache = new ConcurrentHashMap<>();
    
    // 缓存刷新相关
    private volatile long lastCacheUpdate = 0L;
    private static final long CACHE_REFRESH_INTERVAL = 300000L; // 5分钟
    
    /**
     * 检查IP是否被允许访问（带Redis缓存的高性能版本）
     * 优先级：本地IP > Redis缓存检查 > 数据库查询
     */
    public IpCheckResult checkIpAccess(String ipAddress) {
        if (!IpUtil.isValidIp(ipAddress)) {
            return IpCheckResult.blocked("无效IP地址", "invalid");
        }
        
        // 本地IP地址默认允许访问
        if (IpUtil.isLocalIp(ipAddress)) {
            return IpCheckResult.allowed("本地IP地址", "localhost");
        }
        
        // 优先从Redis缓存获取检查结果
        String cacheKey = IP_CHECK_CACHE_KEY + ipAddress;
        if (redisTemplate != null) {
            try {
                Object cachedResult = redisTemplate.opsForValue().get(cacheKey);
                if (cachedResult instanceof IpCheckResult) {
                    log.debug("从Redis缓存获取IP检查结果: {}", ipAddress);
                    return (IpCheckResult) cachedResult;
                }
            } catch (Exception e) {
                log.warn("Redis缓存获取失败，使用数据库查询: {}", e.getMessage());
            }
        }
        
        // 执行完整检查
        IpCheckResult result = performFullIpCheck(ipAddress);
        
        // 缓存检查结果到Redis
        if (redisTemplate != null) {
            try {
                redisTemplate.opsForValue().set(cacheKey, result, IP_CHECK_CACHE_TTL, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.warn("Redis缓存存储失败: {}", e.getMessage());
            }
        }
        
        return result;
    }
    
    /**
     * 执行完整的IP检查（从数据库查询）
     */
    private IpCheckResult performFullIpCheck(String ipAddress) {
        // 检查白名单
        IpWhitelist whitelistMatch = findMatchingWhitelistFromCache(ipAddress);
        if (whitelistMatch != null) {
            updateWhitelistHit(whitelistMatch.getId());
            return IpCheckResult.allowed("白名单匹配: " + whitelistMatch.getDescription(), "whitelist");
        }
        
        // 检查黑名单
        IpBlacklist blacklistMatch = findMatchingBlacklistFromCache(ipAddress);
        if (blacklistMatch != null) {
            updateBlacklistTrigger(blacklistMatch.getId());
            return IpCheckResult.blocked("黑名单匹配: " + blacklistMatch.getReason(), "blacklist");
        }
        
        // 默认允许
        return IpCheckResult.allowed("正常访问", "normal");
    }
    
    /**
     * 从Redis缓存中查找匹配的白名单记录
     */
    @SuppressWarnings("unchecked")
    private IpWhitelist findMatchingWhitelistFromCache(String ipAddress) {
        List<IpWhitelist> whitelists = null;
        
        if (redisTemplate != null) {
            try {
                Object cached = redisTemplate.opsForValue().get(WHITELIST_CACHE_KEY);
                if (cached instanceof List) {
                    whitelists = (List<IpWhitelist>) cached;
                }
            } catch (Exception e) {
                log.warn("获取白名单缓存失败: {}", e.getMessage());
            }
        }
        
        if (whitelists == null) {
            whitelists = getActiveWhitelists();
            if (redisTemplate != null) {
                try {
                    redisTemplate.opsForValue().set(WHITELIST_CACHE_KEY, whitelists, LIST_CACHE_TTL, TimeUnit.SECONDS);
                } catch (Exception e) {
                    log.warn("缓存白名单失败: {}", e.getMessage());
                }
            }
        }
        
        return whitelists.stream()
                .filter(w -> IpUtil.matchesRule(ipAddress, w.getIpAddress()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 从Redis缓存中查找匹配的黑名单记录
     */
    @SuppressWarnings("unchecked")
    private IpBlacklist findMatchingBlacklistFromCache(String ipAddress) {
        List<IpBlacklist> blacklists = null;
        
        if (redisTemplate != null) {
            try {
                Object cached = redisTemplate.opsForValue().get(BLACKLIST_CACHE_KEY);
                if (cached instanceof List) {
                    blacklists = (List<IpBlacklist>) cached;
                }
            } catch (Exception e) {
                log.warn("获取黑名单缓存失败: {}", e.getMessage());
            }
        }
        
        if (blacklists == null) {
            blacklists = getActiveBlacklists();
            if (redisTemplate != null) {
                try {
                    redisTemplate.opsForValue().set(BLACKLIST_CACHE_KEY, blacklists, LIST_CACHE_TTL, TimeUnit.SECONDS);
                } catch (Exception e) {
                    log.warn("缓存黑名单失败: {}", e.getMessage());
                }
            }
        }
        
        return blacklists.stream()
                .filter(b -> IpUtil.matchesRule(ipAddress, b.getIpAddress()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 查找匹配的白名单记录
     */
    private IpWhitelist findMatchingWhitelist(String ipAddress) {
        List<IpWhitelist> whitelists = whitelistCache.get("enabled");
        if (whitelists == null) return null;
        
        return whitelists.stream()
                .filter(w -> IpUtil.matchesRule(ipAddress, w.getIpAddress()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 查找匹配的黑名单记录
     */
    private IpBlacklist findMatchingBlacklist(String ipAddress) {
        List<IpBlacklist> blacklists = blacklistCache.get("enabled");
        if (blacklists == null) return null;
        
        return blacklists.stream()
                .filter(b -> IpUtil.matchesRule(ipAddress, b.getIpAddress()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 添加IP到黑名单（增强版本）
     */
    @Transactional
    @CacheEvict(value = "ipBlacklist", allEntries = true)
    public void addToBlacklist(String ipOrCidr, String reason, Long adminId, LocalDateTime expiresAt) {
        if (!IpUtil.isValidIpOrCidr(ipOrCidr)) {
            throw new IllegalArgumentException("无效的IP地址或CIDR格式: " + ipOrCidr);
        }
        
        // 检查是否已存在
        IpBlacklist existing = ipBlacklistMapper.findByIpAddress(ipOrCidr);
        if (existing != null && "enabled".equals(existing.getStatus())) {
            throw new IllegalArgumentException("该IP/网段已在黑名单中");
        }
        
        LocalDateTime now = LocalDateTime.now();
        IpBlacklist blacklist = IpBlacklist.builder()
                .ipAddress(ipOrCidr)
                .ipType(IpUtil.getIpType(ipOrCidr))
                .reason(reason)
                .status("enabled")
                .expiresAt(expiresAt)
                .createdBy(adminId)
                .createdTime(now)
                .updatedTime(now)
                .triggerCount(0)
                .build();
        
        ipBlacklistMapper.insert(blacklist);
        clearAllCaches();
        log.info("IP/网段 {} 已添加到黑名单，原因: {}, 过期时间: {}", ipOrCidr, reason, expiresAt);
    }
    
    /**
     * 添加IP到白名单（增强版本）
     */
    @Transactional
    @CacheEvict(value = "ipWhitelist", allEntries = true)
    public void addToWhitelist(String ipOrCidr, String description, Long adminId, LocalDateTime expiresAt) {
        if (!IpUtil.isValidIpOrCidr(ipOrCidr)) {
            throw new IllegalArgumentException("无效的IP地址或CIDR格式: " + ipOrCidr);
        }
        
        // 检查是否已存在
        IpWhitelist existing = ipWhitelistMapper.findByIpAddress(ipOrCidr);
        if (existing != null && "enabled".equals(existing.getStatus())) {
            throw new IllegalArgumentException("该IP/网段已在白名单中");
        }
        
        LocalDateTime now = LocalDateTime.now();
        IpWhitelist whitelist = IpWhitelist.builder()
                .ipAddress(ipOrCidr)
                .ipType(IpUtil.getIpType(ipOrCidr))
                .description(description)
                .status("enabled")
                .expiresAt(expiresAt)
                .createdBy(adminId)
                .createdTime(now)
                .updatedTime(now)
                .hitCount(0)
                .build();
        
        ipWhitelistMapper.insert(whitelist);
        clearAllCaches();
        log.info("IP/网段 {} 已添加到白名单，描述: {}, 过期时间: {}", ipOrCidr, description, expiresAt);
    }
    
    /**
     * 从黑名单移除IP（增强版本）
     */
    @Transactional
    @CacheEvict(value = "ipBlacklist", allEntries = true)
    public void removeFromBlacklist(String ipOrCidr) {
        // 更改为直接查询再删除的方式
        IpBlacklist existing = ipBlacklistMapper.findByIpAddress(ipOrCidr);
        if (existing != null) {
            int result = ipBlacklistMapper.deleteById(existing.getId());
            if (result > 0) {
                clearAllCaches();
                log.info("IP/网段 {} 已从黑名单移除", ipOrCidr);
            } else {
                throw new IllegalArgumentException("删除操作失败");
            }
        } else {
            throw new IllegalArgumentException("IP/网段不在黑名单中");
        }
    }
    
    /**
     * 从白名单移除IP（增强版本）
     */
    @Transactional
    @CacheEvict(value = "ipWhitelist", allEntries = true)
    public void removeFromWhitelist(String ipOrCidr) {
        // 更改为直接查询再删除的方式
        IpWhitelist existing = ipWhitelistMapper.findByIpAddress(ipOrCidr);
        if (existing != null) {
            int result = ipWhitelistMapper.deleteById(existing.getId());
            if (result > 0) {
                clearAllCaches();
                log.info("IP/网段 {} 已从白名单移除", ipOrCidr);
            } else {
                throw new IllegalArgumentException("删除操作失败");
            }
        } else {
            throw new IllegalArgumentException("IP/网段不在白名单中");
        }
    }
    
    /**
     * 更新黑名单状态
     */
    @Transactional
    @CacheEvict(value = "ipBlacklist", allEntries = true)
    public void updateBlacklistStatus(Long id, String status) {
        ipBlacklistMapper.updateStatus(id, status, LocalDateTime.now());
        clearAllCaches();
        log.info("黑名单记录 {} 状态更新为: {}", id, status);
    }
    
    /**
     * 更新白名单状态
     */
    @Transactional
    @CacheEvict(value = "ipWhitelist", allEntries = true)
    public void updateWhitelistStatus(Long id, String status) {
        ipWhitelistMapper.updateStatus(id, status, LocalDateTime.now());
        clearAllCaches();
        log.info("白名单记录 {} 状态更新为: {}", id, status);
    }
    
    /**
     * 分页查询黑名单（增强版本）
     */
    public List<IpBlacklist> getBlacklistPage(String ipAddress, String reason, String status, String ipType, int page, int size) {
        int offset = (page - 1) * size;
        return ipBlacklistMapper.findByCondition(ipAddress, reason, status, ipType, offset, size);
    }
    
    /**
     * 统计黑名单数量（增强版本）
     */
    public long countBlacklist(String ipAddress, String reason, String status, String ipType) {
        // 更改为使用统计所有方法
        return ipBlacklistMapper.countAll();
    }
    
    /**
     * 分页查询白名单（增强版本）
     */
    public List<IpWhitelist> getWhitelistPage(String ipAddress, String description, String status, String ipType, int page, int size) {
        int offset = (page - 1) * size;
        // 更改为使用分页查询方法
        return ipWhitelistMapper.findWithPaging(offset, size);
    }
    
    /**
     * 统计白名单数量（增强版本）
     */
    public long countWhitelist(String ipAddress, String description, String status, String ipType) {
        // 更改为使用统计所有方法
        return ipWhitelistMapper.countAll();
    }
    
    /**
     * 批量添加到黑名单（增强版本）
     */
    @Transactional
    @CacheEvict(value = "ipBlacklist", allEntries = true)
    public void batchAddToBlacklist(List<String> ipList, String reason, Long adminId, LocalDateTime expiresAt) {
        List<IpBlacklist> blacklists = ipList.stream()
                .filter(IpUtil::isValidIpOrCidr)
                .map(ip -> {
                    LocalDateTime now = LocalDateTime.now();
                    return IpBlacklist.builder()
                            .ipAddress(ip)
                            .ipType(IpUtil.getIpType(ip))
                            .reason(reason)
                            .status("enabled")
                            .expiresAt(expiresAt)
                            .createdBy(adminId)
                            .createdTime(now)
                            .updatedTime(now)
                            .triggerCount(0)
                            .build();
                })
                .toList();
        
        if (!blacklists.isEmpty()) {
            // 更改为循环调用单个插入方法
            int successCount = 0;
            for (IpBlacklist blacklist : blacklists) {
                try {
                    int result = ipBlacklistMapper.insert(blacklist);
                    if (result > 0) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("插入黑名单失败: ip={}, error={}", blacklist.getIpAddress(), e.getMessage());
                }
            }
            clearAllCaches();
            log.info("批量添加黑名单完成: 成功{}/{}", successCount, blacklists.size());
        }
    }
    
    /**
     * 批量添加到白名单（增强版本）
     */
    @Transactional
    @CacheEvict(value = "ipWhitelist", allEntries = true)
    public void batchAddToWhitelist(List<String> ipList, String description, Long adminId, LocalDateTime expiresAt) {
        List<IpWhitelist> whitelists = ipList.stream()
                .filter(IpUtil::isValidIpOrCidr)
                .map(ip -> {
                    LocalDateTime now = LocalDateTime.now();
                    return IpWhitelist.builder()
                            .ipAddress(ip)
                            .ipType(IpUtil.getIpType(ip))
                            .description(description)
                            .status("enabled")
                            .expiresAt(expiresAt)
                            .createdBy(adminId)
                            .createdTime(now)
                            .updatedTime(now)
                            .hitCount(0)
                            .build();
                })
                .toList();
        
        if (!whitelists.isEmpty()) {
            // 更改为循环调用单个插入方法
            int successCount = 0;
            for (IpWhitelist whitelist : whitelists) {
                try {
                    int result = ipWhitelistMapper.insert(whitelist);
                    if (result > 0) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("插入白名单失败: ip={}, error={}", whitelist.getIpAddress(), e.getMessage());
                }
            }
            clearAllCaches();
            log.info("批量添加白名单完成: 成功{}/{}", successCount, whitelists.size());
        }
    }
    
    /**
     * 批量删除黑名单（增强版本）
     */
    @Transactional
    @CacheEvict(value = "ipBlacklist", allEntries = true)
    public void batchDeleteBlacklist(List<Long> ids) {
        if (!ids.isEmpty()) {
            // 更改为循环调用单个删除方法
            int successCount = 0;
            for (Long id : ids) {
                try {
                    int result = ipBlacklistMapper.deleteById(id);
                    if (result > 0) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("删除黑名单失败: id={}, error={}", id, e.getMessage());
                }
            }
            clearAllCaches();
            log.info("批量删除黑名单完成: 成功{}/{}", successCount, ids.size());
        }
    }
    
    /**
     * 批量删除白名单（增强版本）
     */
    @Transactional
    @CacheEvict(value = "ipWhitelist", allEntries = true)
    public void batchDeleteWhitelist(List<Long> ids) {
        if (!ids.isEmpty()) {
            // 更改为循环调用单个删除方法
            int successCount = 0;
            for (Long id : ids) {
                try {
                    int result = ipWhitelistMapper.deleteById(id);
                    if (result > 0) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("删除白名单失败: id={}, error={}", id, e.getMessage());
                }
            }
            clearAllCaches();
            log.info("批量删除白名单完成: 成功{}/{}", successCount, ids.size());
        }
    }
    
    /**
     * 批量更新黑名单状态
     */
    @Transactional
    @CacheEvict(value = "ipBlacklist", allEntries = true)
    public void batchUpdateBlacklistStatus(List<Long> ids, String status) {
        if (!ids.isEmpty()) {
            // 更改为循环调用单个更新方法
            LocalDateTime updateTime = LocalDateTime.now();
            int successCount = 0;
            for (Long id : ids) {
                try {
                    int result = ipBlacklistMapper.updateStatus(id, status, updateTime);
                    if (result > 0) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("更新黑名单状态失败: id={}, error={}", id, e.getMessage());
                }
            }
            clearAllCaches();
            log.info("批量更新黑名单状态完成: 成功{}/{}, 状态: {}", successCount, ids.size(), status);
        }
    }
    
    /**
     * 批量更新白名单状态
     */
    @Transactional
    @CacheEvict(value = "ipWhitelist", allEntries = true)
    public void batchUpdateWhitelistStatus(List<Long> ids, String status) {
        if (!ids.isEmpty()) {
            // 更改为循环调用单个更新方法
            LocalDateTime updateTime = LocalDateTime.now();
            int successCount = 0;
            for (Long id : ids) {
                try {
                    int result = ipWhitelistMapper.updateStatus(id, status, updateTime);
                    if (result > 0) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("更新白名单状态失败: id={}, error={}", id, e.getMessage());
                }
            }
            clearAllCaches();
            log.info("批量更新白名单状态完成: 成功{}/{}, 状态: {}", successCount, ids.size(), status);
        }
    }
    
    /**
     * 获取IP管理统计信息（增强版本）
     */
    public IpStats getIpStats() {
        IpStats stats = new IpStats();
        stats.setBlacklistCount(ipBlacklistMapper.countAll());
        stats.setWhitelistCount(ipWhitelistMapper.countAll());
        stats.setEnabledBlacklistCount(ipBlacklistMapper.countEnabled());
        stats.setEnabledWhitelistCount(ipWhitelistMapper.countEnabled());
        // 使用简化的查询方法
        List<IpBlacklist> recentBlacklist = ipBlacklistMapper.findWithPaging(0, 10);
        List<IpWhitelist> recentWhitelist = ipWhitelistMapper.findWithPaging(0, 10);
        List<IpBlacklist> topTriggered = ipBlacklistMapper.findTopTriggered(10);
        List<IpWhitelist> topHit = ipWhitelistMapper.findTopHit(10);
        
        stats.setRecentBlacklist(recentBlacklist);
        stats.setRecentWhitelist(recentWhitelist);
        stats.setMostTriggeredBlacklist(topTriggered);
        stats.setMostHitWhitelist(topHit);
        
        // 访问统计
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        LocalDateTime now = LocalDateTime.now();
        // 使用基础的统计方法
        stats.setTodayAccessCount(ipAccessLogMapper.countByTimeRange(yesterday, now));
        stats.setTodayBlockedCount(ipAccessLogMapper.countBlocked());
        
        return stats;
    }
    
    /**
     * 获取IP访问分析报告
     */
    public IpAnalyticsReport getAnalyticsReport(LocalDateTime startTime, LocalDateTime endTime) {
        IpAnalyticsReport report = new IpAnalyticsReport();
        
        // 使用基础的统计方法
        report.setTotalAccessCount(ipAccessLogMapper.countByTimeRange(startTime, endTime));
        report.setBlockedAccessCount(ipAccessLogMapper.countBlocked());
        report.setAverageProcessingTime(0.0); // 暂时设为默认值
        
        // 使用基础的统计方法
        report.setMostFrequentIps(new ArrayList<>());
        report.setSuspiciousIps(ipAccessLogMapper.findSuspiciousIps(startTime, endTime, 5, 20));
        
        // 使用基础的时间统计方法
        report.setHourlyStats(new ArrayList<>());
        report.setDailyStats(ipAccessLogMapper.getDailyStats(startTime, endTime));
        
        // 使用基础的地理位置统计
        report.setLocationStats(new ArrayList<>());
        
        // 使用基础的状态统计
        report.setAccessStatusStats(ipAccessLogMapper.getAccessStats(startTime, endTime));
        report.setAccessResultStats(new ArrayList<>());
        
        // 使用基础的页面统计
        report.setMostPopularPages(ipAccessLogMapper.getPopularPaths(startTime, endTime, 20));
        report.setSlowestPages(new ArrayList<>());
        
        return report;
    }
    
    /**
     * 自动封禁可疑IP
     */
    @Transactional
    public void autoBlockSuspiciousIps(int threshold, int timeRangeMinutes, Long adminId) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusMinutes(timeRangeMinutes);
        
        List<Map<String, Object>> suspiciousIps = ipAccessLogMapper.findSuspiciousIps(startTime, endTime, threshold, 100);
        
        List<String> ipsToBlock = new ArrayList<>();
        for (Map<String, Object> ipInfo : suspiciousIps) {
            String ipAddress = (String) ipInfo.get("ip_address");
            Long blockedCount = (Long) ipInfo.get("blocked_count");
            
            // 检查是否已在黑名单中
            if (ipBlacklistMapper.findByIpAddress(ipAddress) == null) {
                ipsToBlock.add(ipAddress);
            }
        }
        
        if (!ipsToBlock.isEmpty()) {
            LocalDateTime expiresAt = LocalDateTime.now().plusHours(24); // 24小时后自动解封
            batchAddToBlacklist(ipsToBlock, "自动封禁(异常访问)", adminId, expiresAt);
            log.info("自动封禁了 {} 个可疑IP", ipsToBlock.size());
        }
    }
    
    /**
     * 清理过期记录（增强版本）
     */
    @Transactional
    @CacheEvict(value = {"ipBlacklist", "ipWhitelist"}, allEntries = true)
    public void cleanupExpiredRecords() {
        int expiredBlacklist = ipBlacklistMapper.deleteExpired();
        int expiredWhitelist = ipWhitelistMapper.deleteExpired();
        
        if (expiredBlacklist > 0 || expiredWhitelist > 0) {
            clearAllCaches();
            log.info("清理完成: 黑名单 {} 条, 白名单 {} 条", expiredBlacklist, expiredWhitelist);
        }
    }
    
    /**
     * 记录IP访问日志
     */
    @Async
    public void logIpAccess(String ipAddress, Long userId, String requestUrl, String requestMethod, 
                          String userAgent, String referer, String accessStatus, String accessResult, 
                          String location, Long processingTime) {
        IpAccessLog accessLog = IpAccessLog.builder()
                .ipAddress(ipAddress)
                .userId(userId)
                .requestUrl(requestUrl)
                .requestMethod(requestMethod)
                .userAgent(userAgent)
                .referer(referer)
                .accessStatus(accessStatus)
                .accessResult(accessResult)
                .location(location)
                .processingTime(processingTime)
                .accessTime(LocalDateTime.now())
                .build();
        
        ipAccessLogMapper.insert(accessLog);
    }
    
    /**
     * 缓存管理方法
     */
    private void refreshCacheIfNeeded() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastCacheUpdate > CACHE_REFRESH_INTERVAL || blacklistCache.isEmpty() || whitelistCache.isEmpty()) {
            refreshCache();
        }
    }
    
    private void refreshCache() {
        try {
            List<IpBlacklist> enabledBlacklist = ipBlacklistMapper.findAllEnabled();
            List<IpWhitelist> enabledWhitelist = ipWhitelistMapper.findAllEnabled();
            
            blacklistCache.put("enabled", enabledBlacklist);
            whitelistCache.put("enabled", enabledWhitelist);
            lastCacheUpdate = System.currentTimeMillis();
            
            log.debug("缓存刷新完成: 黑名单 {} 条, 白名单 {} 条", enabledBlacklist.size(), enabledWhitelist.size());
        } catch (Exception e) {
            log.error("缓存刷新失败", e);
        }
    }
    
    private void clearCache() {
        blacklistCache.clear();
        whitelistCache.clear();
        lastCacheUpdate = 0L;
        log.debug("缓存已清空");
    }
    
    /**
     * 更新白名单命中信息
     */
    @Async
    private void updateWhitelistHit(Long id) {
        try {
            // 更改为使用IP地址调用方法
            IpWhitelist whitelist = ipWhitelistMapper.findById(id);
            if (whitelist != null) {
                LocalDateTime now = LocalDateTime.now();
                ipWhitelistMapper.updateHitInfo(whitelist.getIpAddress(), now, now);
            }
        } catch (Exception e) {
            log.error("更新白名单命中信息失败: id={}", id, e);
        }
    }
    
    /**
     * 更新黑名单触发信息
     */
    @Async
    private void updateBlacklistTrigger(Long id) {
        try {
            // 更改为使用IP地址调用方法
            IpBlacklist blacklist = ipBlacklistMapper.findById(id);
            if (blacklist != null) {
                LocalDateTime now = LocalDateTime.now();
                ipBlacklistMapper.updateTriggerInfo(blacklist.getIpAddress(), now, now);
            }
        } catch (Exception e) {
            log.error("更新黑名单触发信息失败: id={}", id, e);
        }
    }
    
    /**
     * IP检查结果类
     */
    public static class IpCheckResult {
        private boolean allowed;
        private String reason;
        private String type;
        
        private IpCheckResult(boolean allowed, String reason, String type) {
            this.allowed = allowed;
            this.reason = reason;
            this.type = type;
        }
        
        public static IpCheckResult allowed(String reason, String type) {
            return new IpCheckResult(true, reason, type);
        }
        
        public static IpCheckResult blocked(String reason, String type) {
            return new IpCheckResult(false, reason, type);
        }
        
        // Getters
        public boolean isAllowed() { return allowed; }
        public String getReason() { return reason; }
        public String getType() { return type; }
    }
    
    /**
     * IP统计信息类（增强版本）
     */
    public static class IpStats {
        private long blacklistCount;
        private long whitelistCount;
        private long enabledBlacklistCount;
        private long enabledWhitelistCount;
        private long todayAccessCount;
        private long todayBlockedCount;
        private List<IpBlacklist> recentBlacklist;
        private List<IpWhitelist> recentWhitelist;
        private List<IpBlacklist> mostTriggeredBlacklist;
        private List<IpWhitelist> mostHitWhitelist;
        
        // Getters and Setters
        public long getBlacklistCount() { return blacklistCount; }
        public void setBlacklistCount(long blacklistCount) { this.blacklistCount = blacklistCount; }
        
        public long getWhitelistCount() { return whitelistCount; }
        public void setWhitelistCount(long whitelistCount) { this.whitelistCount = whitelistCount; }
        
        public long getEnabledBlacklistCount() { return enabledBlacklistCount; }
        public void setEnabledBlacklistCount(long enabledBlacklistCount) { this.enabledBlacklistCount = enabledBlacklistCount; }
        
        public long getEnabledWhitelistCount() { return enabledWhitelistCount; }
        public void setEnabledWhitelistCount(long enabledWhitelistCount) { this.enabledWhitelistCount = enabledWhitelistCount; }
        
        public long getTodayAccessCount() { return todayAccessCount; }
        public void setTodayAccessCount(long todayAccessCount) { this.todayAccessCount = todayAccessCount; }
        
        public long getTodayBlockedCount() { return todayBlockedCount; }
        public void setTodayBlockedCount(long todayBlockedCount) { this.todayBlockedCount = todayBlockedCount; }
        
        public List<IpBlacklist> getRecentBlacklist() { return recentBlacklist; }
        public void setRecentBlacklist(List<IpBlacklist> recentBlacklist) { this.recentBlacklist = recentBlacklist; }
        
        public List<IpWhitelist> getRecentWhitelist() { return recentWhitelist; }
        public void setRecentWhitelist(List<IpWhitelist> recentWhitelist) { this.recentWhitelist = recentWhitelist; }
        
        public List<IpBlacklist> getMostTriggeredBlacklist() { return mostTriggeredBlacklist; }
        public void setMostTriggeredBlacklist(List<IpBlacklist> mostTriggeredBlacklist) { this.mostTriggeredBlacklist = mostTriggeredBlacklist; }
        
        public List<IpWhitelist> getMostHitWhitelist() { return mostHitWhitelist; }
        public void setMostHitWhitelist(List<IpWhitelist> mostHitWhitelist) { this.mostHitWhitelist = mostHitWhitelist; }
    }
    
    /**
     * IP分析报告类
     */
    public static class IpAnalyticsReport {
        private long totalAccessCount;
        private long blockedAccessCount;
        private Double averageProcessingTime;
        private List<Map<String, Object>> mostFrequentIps;
        private List<Map<String, Object>> suspiciousIps;
        private List<Map<String, Object>> hourlyStats;
        private List<Map<String, Object>> dailyStats;
        private List<Map<String, Object>> locationStats;
        private List<Map<String, Object>> accessStatusStats;
        private List<Map<String, Object>> accessResultStats;
        private List<Map<String, Object>> mostPopularPages;
        private List<Map<String, Object>> slowestPages;
        
        // Getters and Setters
        public long getTotalAccessCount() { return totalAccessCount; }
        public void setTotalAccessCount(long totalAccessCount) { this.totalAccessCount = totalAccessCount; }
        
        public long getBlockedAccessCount() { return blockedAccessCount; }
        public void setBlockedAccessCount(long blockedAccessCount) { this.blockedAccessCount = blockedAccessCount; }
        
        public Double getAverageProcessingTime() { return averageProcessingTime; }
        public void setAverageProcessingTime(Double averageProcessingTime) { this.averageProcessingTime = averageProcessingTime; }
        
        public List<Map<String, Object>> getMostFrequentIps() { return mostFrequentIps; }
        public void setMostFrequentIps(List<Map<String, Object>> mostFrequentIps) { this.mostFrequentIps = mostFrequentIps; }
        
        public List<Map<String, Object>> getSuspiciousIps() { return suspiciousIps; }
        public void setSuspiciousIps(List<Map<String, Object>> suspiciousIps) { this.suspiciousIps = suspiciousIps; }
        
        public List<Map<String, Object>> getHourlyStats() { return hourlyStats; }
        public void setHourlyStats(List<Map<String, Object>> hourlyStats) { this.hourlyStats = hourlyStats; }
        
        public List<Map<String, Object>> getDailyStats() { return dailyStats; }
        public void setDailyStats(List<Map<String, Object>> dailyStats) { this.dailyStats = dailyStats; }
        
        public List<Map<String, Object>> getLocationStats() { return locationStats; }
        public void setLocationStats(List<Map<String, Object>> locationStats) { this.locationStats = locationStats; }
        
        public List<Map<String, Object>> getAccessStatusStats() { return accessStatusStats; }
        public void setAccessStatusStats(List<Map<String, Object>> accessStatusStats) { this.accessStatusStats = accessStatusStats; }
        
        public List<Map<String, Object>> getAccessResultStats() { return accessResultStats; }
        public void setAccessResultStats(List<Map<String, Object>> accessResultStats) { this.accessResultStats = accessResultStats; }
        
        public List<Map<String, Object>> getMostPopularPages() { return mostPopularPages; }
        public void setMostPopularPages(List<Map<String, Object>> mostPopularPages) { this.mostPopularPages = mostPopularPages; }
        
        public List<Map<String, Object>> getSlowestPages() { return slowestPages; }
        public void setSlowestPages(List<Map<String, Object>> slowestPages) { this.slowestPages = slowestPages; }
    }
    
    // =================== Redis缓存管理方法 ===================
    
    /**
     * 清理所有Redis缓存
     */
    public void clearAllCaches() {
        if (redisTemplate != null) {
            try {
                // 清理IP检查缓存
                Set<String> ipCheckKeys = redisTemplate.keys(IP_CHECK_CACHE_KEY + "*");
                if (ipCheckKeys != null && !ipCheckKeys.isEmpty()) {
                    redisTemplate.delete(ipCheckKeys);
                }
                
                // 清理名单缓存
                redisTemplate.delete(BLACKLIST_CACHE_KEY);
                redisTemplate.delete(WHITELIST_CACHE_KEY);
                
                log.info("所有IP管理Redis缓存已清理");
            } catch (Exception e) {
                log.warn("清理Redis缓存失败: {}", e.getMessage());
            }
        }
        
        // 清理内存缓存
        clearCache();
        log.info("内存缓存已清理");
    }
    
    /**
     * 检查IP访问速率限制
     */
    public boolean checkRateLimit(String ipAddress, int maxRequests, int timeWindowSeconds) {
        if (redisTemplate == null) {
            log.debug("Redis不可用，跳过速率限制检查");
            return true; // Redis不可用时默认允许访问
        }
        
        try {
            String key = RATE_LIMIT_KEY + ipAddress + ":" + timeWindowSeconds;
            String count = redisTemplate.opsForValue().get(key) != null ? 
                          redisTemplate.opsForValue().get(key).toString() : "0";
            
            int currentCount = Integer.parseInt(count);
            
            if (currentCount >= maxRequests) {
                log.warn("IP {} 超过速率限制: {}/{} 次/{}秒", ipAddress, currentCount, maxRequests, timeWindowSeconds);
                return false;
            }
            
            // 增加计数器
            if (currentCount == 0) {
                redisTemplate.opsForValue().set(key, "1", timeWindowSeconds, TimeUnit.SECONDS);
            } else {
                redisTemplate.opsForValue().increment(key);
            }
            
            return true;
        } catch (Exception e) {
            log.warn("检查速率限制失败: {}", e.getMessage());
            return true; // 出错时默认允许访问
        }
    }
    
    /**
     * 获取IP访问频率统计
     */
    public Map<String, Object> getIpAccessFrequency(String ipAddress, int timeWindowMinutes) {
        Map<String, Object> result = new HashMap<>();
        
        if (redisTemplate == null) {
            result.put("error", "Redis不可用");
            return result;
        }
        
        try {
            String key = RATE_LIMIT_KEY + ipAddress + ":" + (timeWindowMinutes * 60);
            String count = redisTemplate.opsForValue().get(key) != null ? 
                          redisTemplate.opsForValue().get(key).toString() : "0";
            
            Long ttl = redisTemplate.getExpire(key, TimeUnit.SECONDS);
            
            result.put("ipAddress", ipAddress);
            result.put("requestCount", Integer.parseInt(count));
            result.put("timeWindow", timeWindowMinutes);
            result.put("remainingTtl", ttl);
            result.put("resetTime", LocalDateTime.now().plusSeconds(ttl != null ? ttl : 0));
            
        } catch (Exception e) {
            log.warn("获取IP访问频率统计失败: {}", e.getMessage());
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 预热Redis缓存
     */
    @Async
    public void warmupCache() {
        if (redisTemplate == null) {
            log.info("Redis不可用，跳过缓存预热");
            return;
        }
        
        try {
            log.info("开始预热IP管理缓存...");
            
            // 预加载活跃的黑名单
            List<IpBlacklist> blacklists = getActiveBlacklists();
            redisTemplate.opsForValue().set(BLACKLIST_CACHE_KEY, blacklists, LIST_CACHE_TTL, TimeUnit.SECONDS);
            
            // 预加载活跃的白名单
            List<IpWhitelist> whitelists = getActiveWhitelists();
            redisTemplate.opsForValue().set(WHITELIST_CACHE_KEY, whitelists, LIST_CACHE_TTL, TimeUnit.SECONDS);
            
            log.info("IP管理缓存预热完成: 黑名单 {} 条, 白名单 {} 条", blacklists.size(), whitelists.size());
        } catch (Exception e) {
            log.error("缓存预热失败: {}", e.getMessage());
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        
        if (redisTemplate == null) {
            stats.put("error", "Redis不可用");
            stats.put("redisAvailable", false);
        } else {
            try {
                // 统计IP检查缓存数量
                Set<String> ipCheckKeys = redisTemplate.keys(IP_CHECK_CACHE_KEY + "*");
                stats.put("ipCheckCacheCount", ipCheckKeys != null ? ipCheckKeys.size() : 0);
                
                // 统计速率限制缓存数量
                Set<String> rateLimitKeys = redisTemplate.keys(RATE_LIMIT_KEY + "*");
                stats.put("rateLimitCacheCount", rateLimitKeys != null ? rateLimitKeys.size() : 0);
                
                // 检查名单缓存状态
                stats.put("blacklistCacheExists", redisTemplate.hasKey(BLACKLIST_CACHE_KEY));
                stats.put("whitelistCacheExists", redisTemplate.hasKey(WHITELIST_CACHE_KEY));
                stats.put("redisAvailable", true);
                
            } catch (Exception e) {
                log.warn("获取Redis缓存统计失败: {}", e.getMessage());
                stats.put("error", e.getMessage());
                stats.put("redisAvailable", false);
            }
        }
        
        // 内存缓存统计
        stats.put("memoryBlacklistCacheSize", blacklistCache.size());
        stats.put("memoryWhitelistCacheSize", whitelistCache.size());
        stats.put("lastCacheUpdate", new Date(lastCacheUpdate));
        
        return stats;
    }
    
    /**
     * 获取活跃的黑名单
     */
    public List<IpBlacklist> getActiveBlacklists() {
        try {
            return ipBlacklistMapper.findAllEnabled();
        } catch (Exception e) {
            log.error("获取活跃黑名单失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取活跃的白名单
     */
    public List<IpWhitelist> getActiveWhitelists() {
        try {
            return ipWhitelistMapper.findAllEnabled();
        } catch (Exception e) {
            log.error("获取活跃白名单失败", e);
            return new ArrayList<>();
        }
    }
}