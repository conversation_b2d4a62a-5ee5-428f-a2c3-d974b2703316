package com.example.v14.service;

import com.example.v14.entity.Share;
import com.example.v14.entity.FileInfo;
import com.example.v14.entity.Folder;
import com.example.v14.dto.ShareWithFileInfo;
import com.example.v14.mapper.ShareMapper;
import com.example.v14.mapper.FileMapper;
import com.example.v14.mapper.FolderMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;

/**
 * 分享服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShareService {
    
    private final ShareMapper shareMapper;
    private final FileMapper fileMapper;
    private final FolderMapper folderMapper;
    
    /**
     * 创建文件分享链接
     */
    @Transactional
    public Share createFileShare(Long fileId, Long userId, Share.ShareType shareType, 
                               String password, LocalDateTime expireTime, Integer downloadLimit) {
        // 验证文件是否存在且属于当前用户
        FileInfo fileInfo = fileMapper.findById(fileId);
        if (fileInfo == null || !fileInfo.getUserId().equals(userId)) {
            throw new RuntimeException("文件不存在或无权限");
        }
        
        // 生成分享码
        String shareCode = generateShareCode();
        
        // 创建分享记录
        Share share = Share.builder()
                .shareCode(shareCode)
                .fileId(fileId)
                .userId(userId)
                .shareType(shareType)
                .password(password)
                .expireTime(expireTime)
                .downloadLimit(downloadLimit)
                .downloadCount(0)
                .isActive(true)
                .createdTime(LocalDateTime.now())
                .build();
        
        shareMapper.insert(share);
        
        log.info("创建文件分享链接成功: {}, 文件: {}, 用户: {}", shareCode, fileInfo.getName(), userId);
        
        return share;
    }
    
    /**
     * 创建文件夹分享链接
     */
    @Transactional
    public Share createFolderShare(Long folderId, Long userId, Share.ShareType shareType, 
                                 String password, LocalDateTime expireTime, Integer downloadLimit) {
        // 验证文件夹是否存在且属于当前用户
        Folder folder = folderMapper.findById(folderId);
        if (folder == null || !folder.getUserId().equals(userId)) {
            throw new RuntimeException("文件夹不存在或无权限");
        }
        
        // 生成分享码
        String shareCode = generateShareCode();
        
        // 创建分享记录
        Share share = Share.builder()
                .shareCode(shareCode)
                .folderId(folderId)
                .userId(userId)
                .shareType(shareType)
                .password(password)
                .expireTime(expireTime)
                .downloadLimit(downloadLimit)
                .downloadCount(0)
                .isActive(true)
                .createdTime(LocalDateTime.now())
                .build();
        
        shareMapper.insert(share);
        
        log.info("创建文件夹分享链接成功: {}, 文件夹: {}, 用户: {}", shareCode, folder.getName(), userId);
        
        return share;
    }
    
    /**
     * 创建分享链接（兼容旧版本）
     */
    @Transactional
    @Deprecated
    public Share createShare(Long fileId, Long userId, Share.ShareType shareType, 
                           String password, LocalDateTime expireTime, Integer downloadLimit) {
        return createFileShare(fileId, userId, shareType, password, expireTime, downloadLimit);
    }
    
    /**
     * 根据分享码获取分享信息
     */
    public Share getShareByCode(String shareCode) {
        return shareMapper.findByShareCode(shareCode);
    }
    
    /**
     * 验证分享访问
     */
    public boolean validateShareAccess(String shareCode, String password) {
        Share share = shareMapper.findByShareCode(shareCode);
        log.info("验证分享访问: shareCode={}, password={}", shareCode, password);

        if (share == null) {
            log.warn("分享不存在: shareCode={}", shareCode);
            return false;
        }

        log.info("分享信息: id={}, password={}, isActive={}, hasPassword={}, isAvailable={}",
                share.getId(), share.getPassword(), share.getIsActive(), share.hasPassword(), share.isAvailable());

        if (!share.isAvailable()) {
            log.warn("分享不可用: shareCode={}", shareCode);
            return false;
        }

        // 检查密码
        if (share.hasPassword()) {
            // 安全的密码比较，处理null值和空白字符
            String sharePassword = share.getPassword();
            String inputPassword = password;
            
            // 标准化密码（去除前后空白）
            if (sharePassword != null) {
                sharePassword = sharePassword.trim();
            }
            if (inputPassword != null) {
                inputPassword = inputPassword.trim();
            }
            
            boolean passwordMatch = java.util.Objects.equals(sharePassword, inputPassword);
            log.info("密码验证: 期望='{}', 实际='{}', 匹配={}", sharePassword, inputPassword, passwordMatch);
            
            if (!passwordMatch) {
                log.warn("分享密码验证失败: shareCode={}", shareCode);
                return false;
            }
        }

        log.info("分享访问验证成功: shareCode={}", shareCode);
        return true;
    }
    
    /**
     * 记录分享下载
     */
    @Transactional
    public void recordShareDownload(String shareCode) {
        Share share = shareMapper.findByShareCode(shareCode);
        if (share == null || !share.isAvailable()) {
            throw new RuntimeException("分享链接无效");
        }
        
        if (!share.allowDownload()) {
            throw new RuntimeException("该分享不允许下载");
        }
        
        // 增加下载次数
        shareMapper.increaseDownloadCount(share.getId());
        
        // 如果是文件分享，同时增加文件的下载次数
        if (share.getFileId() != null) {
            fileMapper.increaseDownloadCount(share.getFileId());
        }
        
        log.info("分享下载记录: {}", shareCode);
    }
    
    /**
     * 更新分享设置
     */
    @Transactional
    public void updateShare(Long shareId, Long userId, String password, LocalDateTime expireTime, Integer downloadLimit) {
        Share share = shareMapper.findById(shareId);
        if (share == null || !share.getUserId().equals(userId)) {
            throw new RuntimeException("分享不存在或无权限操作");
        }

        // 更新分享设置
        if (password != null && !password.trim().isEmpty()) {
            share.setPassword(password.trim());
        } else if (password != null && password.trim().isEmpty()) {
            share.setPassword(null); // 清空密码
        }

        share.setExpireTime(expireTime);
        share.setDownloadLimit(downloadLimit);

        shareMapper.update(share);
        log.info("更新分享设置成功: shareId={}, userId={}", shareId, userId);
    }

    /**
     * 取消分享
     */
    @Transactional
    public void cancelShare(Long shareId, Long userId) {
        Share share = shareMapper.findById(shareId);
        if (share == null || !share.getUserId().equals(userId)) {
            throw new RuntimeException("分享不存在或无权限");
        }

        shareMapper.updateStatus(shareId, false);

        log.info("取消分享成功: {}, 用户: {}", share.getShareCode(), userId);
    }
    
    /**
     * 获取用户的分享列表
     */
    public List<Share> getUserShares(Long userId, int page, int size) {
        int offset = (page - 1) * size;
        return shareMapper.findByUser(userId, offset, size);
    }

    /**
     * 获取用户的分享列表（包含文件信息）
     */
    public List<ShareWithFileInfo> getUserSharesWithFileInfo(Long userId, int page, int size) {
        int offset = (page - 1) * size;
        List<Share> shares = shareMapper.findByUser(userId, offset, size);

        return shares.stream().map(share -> {
            if (share.getFileId() != null) {
                // 文件分享
                FileInfo fileInfo = fileMapper.findById(share.getFileId());
                return new ShareWithFileInfo(share, fileInfo);
            } else if (share.getFolderId() != null) {
                // 文件夹分享
                Folder folder = folderMapper.findById(share.getFolderId());
                return new ShareWithFileInfo(share, folder);
            } else {
                // 异常情况，既不是文件也不是文件夹
                return new ShareWithFileInfo(share, (FileInfo) null);
            }
        }).collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取分享统计信息
     */
    public ShareStats getShareStats(Long userId) {
        long totalShares = shareMapper.countByUser(userId);
        long activeShares = shareMapper.countActiveByUser(userId);
        long totalDownloads = shareMapper.sumDownloadsByUser(userId);
        
        return new ShareStats(totalShares, activeShares, totalDownloads);
    }
    
    /**
     * 清理过期分享
     */
    @Transactional
    public void cleanExpiredShares() {
        int count = shareMapper.deactivateExpiredShares(LocalDateTime.now());
        if (count > 0) {
            log.info("清理过期分享: {} 个", count);
        }
    }
    
    /**
     * 批量取消分享
     */
    @Transactional
    public void batchCancelShares(List<Long> shareIds, Long userId) {
        if (shareIds == null || shareIds.isEmpty()) {
            throw new RuntimeException("分享ID列表不能为空");
        }

        // 更改为循环验证和更新方式
        int validCount = 0;
        int successCount = 0;
        
        // 验证所有分享都属于当前用户
        for (Long shareId : shareIds) {
            Share share = shareMapper.findById(shareId);
            if (share != null && share.getUserId().equals(userId)) {
                validCount++;
            }
        }
        
        if (validCount != shareIds.size()) {
            throw new RuntimeException("部分分享不存在或无权限操作");
        }

        // 循环更新状态为禁用
        for (Long shareId : shareIds) {
            try {
                int result = shareMapper.updateStatus(shareId, false);
                if (result > 0) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("更新分享状态失败: shareId={}, error={}", shareId, e.getMessage());
            }
        }
        
        int updatedCount = successCount;

        log.info("批量取消分享成功: 用户={}, 分享数量={}, 实际更新={}", userId, shareIds.size(), updatedCount);
    }

    /**
     * 批量启用分享
     */
    @Transactional
    public void batchEnableShares(List<Long> shareIds, Long userId) {
        if (shareIds == null || shareIds.isEmpty()) {
            throw new RuntimeException("分享ID列表不能为空");
        }

        // 更改为循环验证和更新方式
        int validCount = 0;
        int successCount = 0;
        
        // 验证所有分享都属于当前用户
        for (Long shareId : shareIds) {
            Share share = shareMapper.findById(shareId);
            if (share != null && share.getUserId().equals(userId)) {
                validCount++;
            }
        }
        
        if (validCount != shareIds.size()) {
            throw new RuntimeException("部分分享不存在或无权限操作");
        }

        // 循环更新状态为启用
        for (Long shareId : shareIds) {
            try {
                int result = shareMapper.updateStatus(shareId, true);
                if (result > 0) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("更新分享状态失败: shareId={}, error={}", shareId, e.getMessage());
            }
        }
        
        int updatedCount = successCount;

        log.info("批量启用分享成功: 用户={}, 分享数量={}, 实际更新={}", userId, shareIds.size(), updatedCount);
    }

    /**
     * 批量删除分享
     */
    @Transactional
    public void batchDeleteShares(List<Long> shareIds, Long userId) {
        if (shareIds == null || shareIds.isEmpty()) {
            throw new RuntimeException("分享ID列表不能为空");
        }

        // 更改为循环验证和删除方式
        int validCount = 0;
        int successCount = 0;
        
        // 验证所有分享都属于当前用户
        for (Long shareId : shareIds) {
            Share share = shareMapper.findById(shareId);
            if (share != null && share.getUserId().equals(userId)) {
                validCount++;
            }
        }
        
        if (validCount != shareIds.size()) {
            throw new RuntimeException("部分分享不存在或无权限操作");
        }

        // 循环删除
        for (Long shareId : shareIds) {
            try {
                int result = shareMapper.deleteById(shareId);
                if (result > 0) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("删除分享失败: shareId={}, error={}", shareId, e.getMessage());
            }
        }
        
        int deletedCount = successCount;

        log.info("批量删除分享成功: 用户={}, 分享数量={}, 实际删除={}", userId, shareIds.size(), deletedCount);
    }

    /**
     * 生成分享码
     */
    private String generateShareCode() {
        String chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < 8; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }

        String shareCode = sb.toString();

        // 检查是否已存在
        if (shareMapper.findByShareCode(shareCode) != null) {
            return generateShareCode(); // 递归生成新的
        }

        return shareCode;
    }
    
    /**
     * 分享统计信息类
     */
    public static class ShareStats {
        private final long totalShares;
        private final long activeShares;
        private final long totalDownloads;
        
        public ShareStats(long totalShares, long activeShares, long totalDownloads) {
            this.totalShares = totalShares;
            this.activeShares = activeShares;
            this.totalDownloads = totalDownloads;
        }
        
        public long getTotalShares() { return totalShares; }
        public long getActiveShares() { return activeShares; }
        public long getTotalDownloads() { return totalDownloads; }
    }
}
