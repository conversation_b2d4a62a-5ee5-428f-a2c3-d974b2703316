package com.example.v14.service;

import com.example.v14.entity.DownloadStats;
import com.example.v14.mapper.DownloadStatsMapper;
import com.example.v14.mapper.UserLogMapper;
import com.example.v14.mapper.IpAccessLogMapper;
import com.example.v14.mapper.FileMapper;
import com.example.v14.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 数据分析服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AnalyticsService {
    
    private final DownloadStatsMapper downloadStatsMapper;
    private final UserLogMapper userLogMapper;
    private final IpAccessLogMapper ipAccessLogMapper;
    private final FileMapper fileMapper;
    private final UserMapper userMapper;
    
    /**
     * 记录下载统计
     */
    @Transactional
    public void recordDownload(Long fileId, Long userId, String ipAddress, Long fileSize) {
        try {
            DownloadStats downloadStats = DownloadStats.builder()
                    .fileId(fileId)
                    .userId(userId)
                    .ipAddress(ipAddress)
                    .downloadTime(LocalDateTime.now())
                    .fileSize(fileSize)
                    .build();
            
            downloadStatsMapper.insert(downloadStats);
            log.debug("下载统计记录成功: fileId={}, userId={}", fileId, userId);
        } catch (Exception e) {
            log.error("记录下载统计失败", e);
        }
    }
    
    /**
     * 获取下载统计概览
     */
    public Map<String, Object> getDownloadOverview(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> overview = new HashMap<>();
        
        // 总下载次数
        long totalDownloads = downloadStatsMapper.countByTimeRange(startTime, endTime);
        overview.put("totalDownloads", totalDownloads);
        
        // 总下载大小
        long totalSize = downloadStatsMapper.sumFileSizeByTimeRange(startTime, endTime);
        overview.put("totalSize", totalSize);
        
        // 今日下载次数
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        long todayDownloads = downloadStatsMapper.countByTimeRange(today, LocalDateTime.now());
        overview.put("todayDownloads", todayDownloads);
        
        // 昨日下载次数
        LocalDateTime yesterday = today.minusDays(1);
        long yesterdayDownloads = downloadStatsMapper.countByTimeRange(yesterday, today);
        overview.put("yesterdayDownloads", yesterdayDownloads);
        
        // 下载增长率
        double growthRate = yesterdayDownloads > 0 ? 
            ((double)(todayDownloads - yesterdayDownloads) / yesterdayDownloads) * 100 : 0;
        overview.put("growthRate", growthRate);
        
        return overview;
    }
    
    /**
     * 获取热门下载文件
     */
    public List<Object> getPopularFiles(int limit) {
        return downloadStatsMapper.findPopularFiles(limit);
    }
    
    /**
     * 获取活跃下载用户
     */
    public List<Object> getActiveDownloadUsers(int limit) {
        return downloadStatsMapper.findActiveUsers(limit);
    }
    
    /**
     * 获取下载趋势数据
     */
    public Map<String, Object> getDownloadTrends(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> trends = new HashMap<>();
        
        // 按日期统计
        List<Object> dailyStats = downloadStatsMapper.countByDate(startTime, endTime);
        trends.put("dailyStats", dailyStats);
        
        // 按小时统计
        List<Object> hourlyStats = downloadStatsMapper.countByHour(startTime, endTime);
        trends.put("hourlyStats", hourlyStats);
        
        return trends;
    }
    
    /**
     * 获取用户下载统计
     */
    public Map<String, Object> getUserDownloadStats(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> stats = new HashMap<>();
        
        // 用户下载次数
        long downloadCount = downloadStatsMapper.countByUserId(userId);
        stats.put("downloadCount", downloadCount);
        
        // 时间范围内下载次数
        long periodDownloads = downloadStatsMapper.countByUserAndTimeRange(userId, startTime, endTime);
        stats.put("periodDownloads", periodDownloads);
        
        // 用户下载记录
        List<DownloadStats> downloadHistory = downloadStatsMapper.findByUserId(userId, 0, 20);
        stats.put("downloadHistory", downloadHistory);
        
        return stats;
    }
    
    /**
     * 获取文件下载统计
     */
    public Map<String, Object> getFileDownloadStats(Long fileId, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> stats = new HashMap<>();
        
        // 文件下载次数
        long downloadCount = downloadStatsMapper.countByFileId(fileId);
        stats.put("downloadCount", downloadCount);
        
        // 时间范围内下载次数
        long periodDownloads = downloadStatsMapper.countByFileAndTimeRange(fileId, startTime, endTime);
        stats.put("periodDownloads", periodDownloads);
        
        // 文件下载记录
        List<DownloadStats> downloadHistory = downloadStatsMapper.findByFileId(fileId, 0, 20);
        stats.put("downloadHistory", downloadHistory);
        
        return stats;
    }
    
    /**
     * 获取系统访问统计
     */
    public Map<String, Object> getAccessStats(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> stats = new HashMap<>();
        
        // 总访问次数
        long totalAccess = ipAccessLogMapper.countByTimeRange(startTime, endTime);
        stats.put("totalAccess", totalAccess);
        
        // 今日访问次数
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        long todayAccess = ipAccessLogMapper.countByTimeRange(today, LocalDateTime.now());
        stats.put("todayAccess", todayAccess);
        
        // 最频繁访问IP
        List<Map<String, Object>> frequentIps = ipAccessLogMapper.findMostFrequentIps(startTime, endTime, 10);
        stats.put("frequentIps", frequentIps);
        
        // 最热门页面
        List<Map<String, Object>> popularPages = ipAccessLogMapper.findMostPopularPages(startTime, endTime, 10);
        stats.put("popularPages", popularPages);
        
        // 按日期统计访问
        List<Map<String, Object>> dailyAccess = ipAccessLogMapper.countByDate(startTime, endTime);
        stats.put("dailyAccess", dailyAccess);
        
        // 按小时统计访问
        List<Map<String, Object>> hourlyAccess = ipAccessLogMapper.countByHour(startTime, endTime);
        stats.put("hourlyAccess", hourlyAccess);
        
        return stats;
    }
    
    /**
     * 获取用户活跃度统计
     */
    public Map<String, Object> getUserActivityStats(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> stats = new HashMap<>();
        
        // 总用户行为次数
        long totalActions = userLogMapper.countByTimeRange(startTime, endTime);
        stats.put("totalActions", totalActions);
        
        // 最活跃用户
        List<Object> activeUsers = userLogMapper.findMostActiveUsers(startTime, endTime, 10);
        stats.put("activeUsers", activeUsers);
        
        // 按操作类型统计
        List<Object> actionStats = userLogMapper.countByActionGroup();
        stats.put("actionStats", actionStats);
        
        // 按日期统计用户行为
        List<Object> dailyActions = userLogMapper.countByDate(startTime, endTime);
        stats.put("dailyActions", dailyActions);
        
        return stats;
    }
    
    /**
     * 获取存储使用统计
     */
    public Map<String, Object> getStorageStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总文件数
        long totalFiles = fileMapper.countAll();
        stats.put("totalFiles", totalFiles);
        
        // 总存储大小
        long totalSize = fileMapper.sumFileSize();
        stats.put("totalSize", totalSize);
        
        // 已删除文件数
        long deletedFiles = fileMapper.countDeleted();
        stats.put("deletedFiles", deletedFiles);
        
        // 按文件类型统计
        Map<String, Long> typeStats = new HashMap<>();
        typeStats.put("images", fileMapper.countByType("image"));
        typeStats.put("documents", fileMapper.countByType("application"));
        typeStats.put("videos", fileMapper.countByType("video"));
        typeStats.put("audios", fileMapper.countByType("audio"));
        stats.put("typeStats", typeStats);
        
        // 按文件大小分布统计
        Map<String, Long> sizeStats = new HashMap<>();
        sizeStats.put("small", fileMapper.countBySizeRange(0L, 1024 * 1024L)); // < 1MB
        sizeStats.put("medium", fileMapper.countBySizeRange(1024 * 1024L, 100 * 1024 * 1024L)); // 1MB-100MB
        sizeStats.put("large", fileMapper.countBySizeRange(100 * 1024 * 1024L, Long.MAX_VALUE)); // > 100MB
        stats.put("sizeStats", sizeStats);
        
        return stats;
    }
    
    /**
     * 获取综合仪表板数据
     */
    public Map<String, Object> getDashboardData(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> dashboard = new HashMap<>();
        
        // 下载统计
        dashboard.put("downloadOverview", getDownloadOverview(startTime, endTime));
        
        // 访问统计
        dashboard.put("accessStats", getAccessStats(startTime, endTime));
        
        // 用户活跃度
        dashboard.put("userActivity", getUserActivityStats(startTime, endTime));
        
        // 存储统计
        dashboard.put("storageStats", getStorageStats());
        
        // 热门文件
        dashboard.put("popularFiles", getPopularFiles(5));
        
        // 活跃用户
        dashboard.put("activeUsers", getActiveDownloadUsers(5));
        
        return dashboard;
    }
    
    /**
     * 生成统计报告
     */
    public Map<String, Object> generateReport(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> report = new HashMap<>();
        
        report.put("reportPeriod", Map.of(
            "startTime", startTime,
            "endTime", endTime
        ));
        
        report.put("summary", Map.of(
            "totalDownloads", downloadStatsMapper.countByTimeRange(startTime, endTime),
            "totalAccess", ipAccessLogMapper.countByTimeRange(startTime, endTime),
            "totalActions", userLogMapper.countByTimeRange(startTime, endTime),
            "totalFiles", fileMapper.countAll(),
            "totalUsers", userMapper.countAll()
        ));
        
        report.put("trends", getDownloadTrends(startTime, endTime));
        report.put("topFiles", getPopularFiles(10));
        report.put("topUsers", getActiveDownloadUsers(10));
        report.put("accessPatterns", getAccessStats(startTime, endTime));
        
        return report;
    }
    
    /**
     * 清理过期统计数据
     */
    @Transactional
    public void cleanupOldStats(int days) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(days);
        
        int downloadStatsCount = downloadStatsMapper.deleteOldRecords(cutoffTime);
        
        log.info("清理过期统计数据完成: 下载统计 {} 条", downloadStatsCount);
    }
}
