package com.example.v14.service;

import com.example.v14.entity.User;
import com.example.v14.entity.FileInfo;
import com.example.v14.mapper.UserMapper;
import com.example.v14.mapper.FileMapper;
import com.example.v14.mapper.ShareMapper;
import com.example.v14.mapper.DownloadStatsMapper;
import com.example.v14.mapper.IpAccessLogMapper;
import com.example.v14.config.FileConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdminService {

    private final UserMapper userMapper;
    private final FileMapper fileMapper;
    private final ShareMapper shareMapper;
    private final DownloadStatsMapper downloadStatsMapper;
    private final IpAccessLogMapper ipAccessLogMapper;
    private final FileConfig fileConfig;
    private final org.springframework.security.crypto.password.PasswordEncoder passwordEncoder;
    
    /**
     * 获取系统统计信息
     */
    public SystemStats getSystemStats() {
        SystemStats stats = new SystemStats();
        
        // 用户统计
        stats.setTotalUsers(userMapper.countAll());
        stats.setActiveUsers(userMapper.countByStatus(User.UserStatus.ACTIVE));
        stats.setDisabledUsers(userMapper.countByStatus(User.UserStatus.DISABLED));
        
        // 文件统计
        stats.setTotalFiles(fileMapper.countAll());
        stats.setTotalSize(fileMapper.sumFileSize());
        stats.setDeletedFiles(fileMapper.countDeleted());
        
        // 今日统计
        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        stats.setTodayUploads(fileMapper.countByDateRange(today, LocalDateTime.now()));
        stats.setTodayRegistrations(userMapper.countByDateRange(today, LocalDateTime.now()));
        
        return stats;
    }
    
    /**
     * 获取用户列表
     */
    public List<User> getUserList(String username, String email, User.UserRole role, 
                                 User.UserStatus status, int page, int size) {
        int offset = (page - 1) * size;
        return userMapper.searchUsers(username, email, role, status, offset, size);
    }
    
    /**
     * 统计用户数量
     */
    public long countUsers(String username, String email, User.UserRole role, User.UserStatus status) {
        return userMapper.countByCondition(username, email, role, status);
    }
    
    /**
     * 更新用户状态
     */
    @Transactional
    public void updateUserStatus(Long userId, User.UserStatus status) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        user.setStatus(status);
        user.setUpdatedTime(LocalDateTime.now());
        userMapper.update(user);
        
        log.info("管理员更新用户状态: userId={}, status={}", userId, status);
    }
    
    /**
     * 更新用户角色
     */
    @Transactional
    public void updateUserRole(Long userId, User.UserRole role) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        user.setRole(role);
        user.setUpdatedTime(LocalDateTime.now());
        userMapper.update(user);
        
        log.info("管理员更新用户角色: userId={}, role={}", userId, role);
    }
    
    /**
     * 更新用户存储限制
     */
    @Transactional
    public void updateUserStorageLimit(Long userId, Long storageLimit, Long singleFileLimit) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        user.setStorageLimit(storageLimit);
        user.setSingleFileLimit(singleFileLimit);
        user.setUpdatedTime(LocalDateTime.now());
        userMapper.update(user);
        
        log.info("管理员更新用户存储限制: userId={}, storageLimit={}, singleFileLimit={}", 
                userId, storageLimit, singleFileLimit);
    }
    
    /**
     * 删除用户
     */
    @Transactional
    public void deleteUser(Long userId) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        if (user.getRole() == User.UserRole.ADMIN) {
            throw new RuntimeException("不能删除管理员用户");
        }
        
        // 删除用户的所有文件
        List<FileInfo> userFiles = fileMapper.findAllByUser(userId);
        for (FileInfo file : userFiles) {
            fileMapper.deleteById(file.getId());
        }
        
        // 删除用户
        userMapper.deleteById(userId);
        
        log.info("管理员删除用户: userId={}", userId);
    }
    
    /**
     * 获取文件统计信息
     */
    public Map<String, Object> getFileStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 文件类型统计
        stats.put("imageFiles", fileMapper.countByType("image"));
        stats.put("videoFiles", fileMapper.countByType("video"));
        stats.put("audioFiles", fileMapper.countByType("audio"));
        stats.put("documentFiles", fileMapper.countByType("document"));
        stats.put("archiveFiles", fileMapper.countByType("archive"));
        stats.put("otherFiles", fileMapper.countByType("other"));
        
        // 文件大小分布
        stats.put("smallFiles", fileMapper.countBySizeRange(0L, 1024 * 1024L)); // < 1MB
        stats.put("mediumFiles", fileMapper.countBySizeRange(1024 * 1024L, 100 * 1024 * 1024L)); // 1MB-100MB
        stats.put("largeFiles", fileMapper.countBySizeRange(100 * 1024 * 1024L, Long.MAX_VALUE)); // > 100MB
        
        return stats;
    }
    
    /**
     * 获取最近上传的文件
     */
    public List<FileInfo> getRecentFiles(int limit) {
        return fileMapper.findRecent(limit);
    }
    
    /**
     * 获取存储使用排行
     */
    public List<User> getStorageUsageRanking(int limit) {
        return userMapper.findTopByStorageUsage(limit);
    }
    
    /**
     * 清理删除的文件
     */
    @Transactional
    public int cleanupDeletedFiles(int days) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(days);
        List<FileInfo> expiredFiles = fileMapper.findDeletedBefore(cutoffTime);

        int count = 0;
        for (FileInfo file : expiredFiles) {
            // 删除物理文件
            try {
                deletePhysicalFile(file);
                log.debug("物理文件删除成功: {}", file.getFilePath());
            } catch (Exception e) {
                log.error("删除物理文件失败: {}, 错误: {}", file.getFilePath(), e.getMessage());
                // 即使物理文件删除失败，也继续删除数据库记录
            }

            // 删除数据库记录
            fileMapper.deleteById(file.getId());
            count++;
        }

        log.info("清理过期删除文件: {} 个", count);
        return count;
    }

    /**
     * 删除物理文件
     */
    private void deletePhysicalFile(FileInfo fileInfo) {
        if (fileInfo.getFilePath() == null || fileInfo.getFilePath().trim().isEmpty()) {
            log.warn("文件路径为空，跳过物理文件删除: fileId={}", fileInfo.getId());
            return;
        }

        try {
            // 构建完整的文件路径
            String uploadPath = fileConfig.getUploadPath();
            if (!uploadPath.endsWith("/") && !uploadPath.endsWith("\\")) {
                uploadPath += File.separator;
            }

            String fullPath = uploadPath + fileInfo.getFilePath();
            File physicalFile = new File(fullPath);

            if (physicalFile.exists()) {
                if (physicalFile.delete()) {
                    log.debug("物理文件删除成功: {}", fullPath);
                } else {
                    log.warn("物理文件删除失败: {}", fullPath);
                }
            } else {
                log.debug("物理文件不存在，可能已被删除: {}", fullPath);
            }
        } catch (Exception e) {
            log.error("删除物理文件时发生异常: {}", fileInfo.getFilePath(), e);
            throw new RuntimeException("删除物理文件失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 根据ID获取用户
     */
    public User getUserById(Long userId) {
        return userMapper.findById(userId);
    }

    /**
     * 创建用户
     */
    @Transactional
    public void createUser(String username, String password, String email, String nickname,
                          User.UserRole role, Long storageLimit) {
        // 检查用户名是否已存在
        if (userMapper.existsByUsername(username, 0L) > 0) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (email != null && !email.isEmpty() && userMapper.existsByEmail(email, 0L) > 0) {
            throw new RuntimeException("邮箱已存在");
        }

        // 设置默认存储限制
        if (storageLimit == null) {
            storageLimit = role == User.UserRole.ADMIN ? -1L : 1610612736L; // 1.5GB
        }

        Long singleFileLimit = role == User.UserRole.ADMIN ? 5368709120L : 1073741824L; // 5GB : 1GB

        User user = User.builder()
                .username(username)
                .password(passwordEncoder.encode(password))
                .email(email)
                .nickname(nickname != null ? nickname : username)
                .role(role)
                .status(User.UserStatus.ACTIVE)
                .storageUsed(0L)
                .storageLimit(storageLimit)
                .singleFileLimit(singleFileLimit)
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();

        userMapper.insert(user);
        log.info("管理员创建用户: {}, 角色: {}", username, role);
    }

    /**
     * 更新用户信息
     */
    @Transactional
    public void updateUserInfo(Long userId, String username, String email, String nickname,
                              User.UserRole role, User.UserStatus status, Long storageLimit) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 更新字段
        if (username != null && !username.equals(user.getUsername())) {
            if (userMapper.existsByUsername(username, userId) > 0) {
                throw new RuntimeException("用户名已存在");
            }
            user.setUsername(username);
        }

        if (email != null && !email.equals(user.getEmail())) {
            if (!email.isEmpty() && userMapper.existsByEmail(email, userId) > 0) {
                throw new RuntimeException("邮箱已存在");
            }
            user.setEmail(email);
        }

        if (nickname != null) {
            user.setNickname(nickname);
        }

        if (role != null) {
            user.setRole(role);
        }

        if (status != null) {
            user.setStatus(status);
        }

        if (storageLimit != null) {
            user.setStorageLimit(storageLimit);
        }

        user.setUpdatedTime(LocalDateTime.now());
        userMapper.update(user);

        log.info("管理员更新用户信息: userId={}", userId);
    }

    /**
     * 获取概览统计
     */
    public Map<String, Object> getOverviewStats(int days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        LocalDateTime endTime = LocalDateTime.now();

        Map<String, Object> stats = new HashMap<>();

        // 总上传量（文件数）
        long totalUploads = fileMapper.countAll();
        long todayUploads = fileMapper.countByDateRange(LocalDateTime.now().withHour(0).withMinute(0).withSecond(0), endTime);

        // 总下载量（使用真实的下载统计）
        long totalDownloads = downloadStatsMapper.countAll();
        long todayDownloads = downloadStatsMapper.countByDateRange(
            LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(),
            System.currentTimeMillis()
        );

        // 总访问量（使用IP访问日志）
        long totalViews = ipAccessLogMapper.countAll();
        long todayViews = ipAccessLogMapper.countByDateRange(
            LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(),
            System.currentTimeMillis()
        );

        // 分享链接数
        long totalShares = shareMapper.countAll();
        long activeShares = shareMapper.countActive();

        stats.put("totalUploads", totalUploads);
        stats.put("uploadsChange", todayUploads);
        stats.put("totalDownloads", totalDownloads);
        stats.put("downloadsChange", todayDownloads);
        stats.put("totalViews", totalViews);
        stats.put("viewsChange", todayViews);
        stats.put("totalShares", totalShares);
        stats.put("activeShares", activeShares);

        return stats;
    }

    /**
     * 获取活跃度统计
     */
    public Map<String, Object> getActivityStats(int days) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime dayStart = now.withHour(0).withMinute(0).withSecond(0);
        LocalDateTime weekStart = now.minusDays(7);
        LocalDateTime monthStart = now.minusDays(30);

        Map<String, Object> stats = new HashMap<>();

        // 活跃用户数（简化处理，实际应该根据登录记录计算）
        long dailyActiveUsers = userMapper.countByDateRange(dayStart, now);
        long weeklyActiveUsers = userMapper.countByDateRange(weekStart, now);
        long monthlyActiveUsers = userMapper.countByDateRange(monthStart, now);

        // 趋势（简化处理）
        stats.put("dailyActiveUsers", dailyActiveUsers);
        stats.put("weeklyActiveUsers", weeklyActiveUsers);
        stats.put("monthlyActiveUsers", monthlyActiveUsers);
        stats.put("avgSessionTime", 25); // 简化处理
        stats.put("dailyActiveTrend", 5);
        stats.put("weeklyActiveTrend", 12);
        stats.put("monthlyActiveTrend", 8);
        stats.put("sessionTimeTrend", -2);

        return stats;
    }

    /**
     * 获取存储统计
     */
    public Map<String, Object> getStorageStats() {
        Map<String, Object> stats = new HashMap<>();

        long totalSize = fileMapper.sumFileSize();
        long usedSize = totalSize; // 简化处理

        stats.put("totalSize", totalSize);
        stats.put("usedSize", usedSize);

        // 文件类型分布（简化处理）
        Map<String, Long> breakdown = new HashMap<>();
        breakdown.put("image", fileMapper.countByType("image"));
        breakdown.put("video", fileMapper.countByType("video"));
        breakdown.put("document", fileMapper.countByType("application"));
        breakdown.put("other", fileMapper.countByType("text"));

        stats.put("breakdown", breakdown);

        return stats;
    }

    /**
     * 获取排行榜数据
     */
    public List<Map<String, Object>> getRankingData(String type, int limit) {
        List<Map<String, Object>> ranking = new ArrayList<>();

        switch (type) {
            case "downloads":
                // 获取下载次数最多的文件
                List<FileInfo> topDownloadFiles = fileMapper.findTopByDownloads(limit);
                for (FileInfo file : topDownloadFiles) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("fileName", file.getName());
                    item.put("fileType", file.getFileType());
                    item.put("fileSize", file.getFileSize());
                    item.put("downloadCount", file.getDownloadCount() != null ? file.getDownloadCount() : 0);
                    ranking.add(item);
                }
                break;
                
            case "views":
                // 获取最近上传的文件作为最受关注（因为没有单独的浏览统计）
                List<FileInfo> recentFiles = fileMapper.findRecent(limit);
                for (FileInfo file : recentFiles) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("fileName", file.getName());
                    item.put("fileType", file.getFileType());
                    item.put("fileSize", file.getFileSize());
                    // 使用下载次数作为关注度的参考
                    item.put("viewCount", file.getDownloadCount() != null ? file.getDownloadCount() * 3 : 0);
                    ranking.add(item);
                }
                break;
                
            case "shares":
                // 获取分享次数最多的文件
                List<Map<String, Object>> topSharedFiles = shareMapper.findTopSharedFiles(limit);
                for (Map<String, Object> fileData : topSharedFiles) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("fileName", fileData.get("fileName"));
                    item.put("fileType", fileData.get("fileType"));
                    item.put("fileSize", fileData.get("fileSize"));
                    item.put("shareCount", fileData.get("shareCount"));
                    ranking.add(item);
                }
                break;
        }

        return ranking;
    }

    /**
     * 获取图表数据
     */
    public Map<String, Object> getChartData(int days) {
        Map<String, Object> chartData = new HashMap<>();

        // 生成时间轴标签
        List<String> labels = new ArrayList<>();
        List<Integer> uploads = new ArrayList<>();
        List<Integer> downloads = new ArrayList<>();

        LocalDateTime now = LocalDateTime.now();
        for (int i = days - 1; i >= 0; i--) {
            LocalDateTime date = now.minusDays(i);
            labels.add(date.format(DateTimeFormatter.ofPattern("MM-dd")));

            // 简化处理：生成模拟数据
            uploads.add((int) (Math.random() * 50) + 10);
            downloads.add((int) (Math.random() * 80) + 20);
        }

        Map<String, Object> timeline = new HashMap<>();
        timeline.put("labels", labels);
        timeline.put("uploads", uploads);
        timeline.put("downloads", downloads);

        chartData.put("timeline", timeline);

        return chartData;
    }

    /**
     * 获取文件类型统计
     */
    public Map<String, Object> getFileTypeStats() {
        Map<String, Object> stats = new HashMap<>();

        stats.put("image", fileMapper.countByType("image"));
        stats.put("video", fileMapper.countByType("video"));
        stats.put("document", fileMapper.countByType("application"));
        stats.put("other", fileMapper.countByType("text"));

        return stats;
    }

    /**
     * 导出统计报告
     */
    public void exportStatsReport(int days, OutputStream outputStream) throws IOException {
        try (PrintWriter writer = new PrintWriter(new OutputStreamWriter(outputStream, StandardCharsets.UTF_8))) {
            // CSV头部
            writer.println("统计项,数值,说明");

            // 获取统计数据
            Map<String, Object> overview = getOverviewStats(days);
            Map<String, Object> storage = getStorageStats();

            // 写入数据
            writer.println("总用户数," + userMapper.countAll() + ",系统注册用户总数");
            writer.println("总文件数," + overview.get("totalUploads") + ",系统文件总数");
            writer.println("总存储空间," + storage.get("totalSize") + ",字节");
            writer.println("活跃用户," + userMapper.countByStatus(User.UserStatus.ACTIVE) + ",状态为活跃的用户");
            writer.println("禁用用户," + userMapper.countByStatus(User.UserStatus.DISABLED) + ",状态为禁用的用户");

            writer.flush();
        }
    }

    /**
     * 获取用户文件列表
     */
    public List<FileInfo> getUserFiles(Long userId) {
        return fileMapper.selectByUserId(userId);
    }

    /**
     * 获取用户文件列表（分页）
     */
    public List<FileInfo> getUserFiles(Long userId, String fileName, int page, int size) {
        int offset = (page - 1) * size;
        
        if (fileName != null && !fileName.trim().isEmpty()) {
            return fileMapper.selectUserFilesWithPagingAndName(userId, fileName, offset, size);
        } else {
            return fileMapper.selectUserFilesWithPaging(userId, offset, size);
        }
    }

    /**
     * 统计用户文件数量
     */
    public long countUserFiles(Long userId, String fileName) {
        if (fileName != null && !fileName.trim().isEmpty()) {
            return fileMapper.countUserFilesWithName(userId, fileName);
        } else {
            return fileMapper.countUserFiles(userId);
        }
    }

    /**
     * 删除用户文件
     */
    @Transactional
    public void deleteUserFile(Long userId, Long fileId) {
        // 验证文件属于指定用户
        FileInfo file = fileMapper.selectById(fileId);
        if (file == null || !file.getUserId().equals(userId)) {
            throw new RuntimeException("文件不存在或无权限操作");
        }

        // 删除物理文件
        try {
            File physicalFile = new File(fileConfig.getUploadPath(), file.getFilePath());
            if (physicalFile.exists()) {
                physicalFile.delete();
            }
        } catch (Exception e) {
            log.warn("删除物理文件失败: {}", e.getMessage());
        }

        // 删除数据库记录
        fileMapper.deleteById(fileId);
    }

    /**
     * 系统统计信息类
     */
    public static class SystemStats {
        private long totalUsers;
        private long activeUsers;
        private long disabledUsers;
        private long totalFiles;
        private long totalSize;
        private long deletedFiles;
        private long todayUploads;
        private long todayRegistrations;

        // Getters and Setters
        public long getTotalUsers() { return totalUsers; }
        public void setTotalUsers(long totalUsers) { this.totalUsers = totalUsers; }

        public long getActiveUsers() { return activeUsers; }
        public void setActiveUsers(long activeUsers) { this.activeUsers = activeUsers; }

        public long getDisabledUsers() { return disabledUsers; }
        public void setDisabledUsers(long disabledUsers) { this.disabledUsers = disabledUsers; }

        public long getTotalFiles() { return totalFiles; }
        public void setTotalFiles(long totalFiles) { this.totalFiles = totalFiles; }

        public long getTotalSize() { return totalSize; }
        public void setTotalSize(long totalSize) { this.totalSize = totalSize; }

        public long getDeletedFiles() { return deletedFiles; }
        public void setDeletedFiles(long deletedFiles) { this.deletedFiles = deletedFiles; }

        public long getTodayUploads() { return todayUploads; }
        public void setTodayUploads(long todayUploads) { this.todayUploads = todayUploads; }

        public long getTodayRegistrations() { return todayRegistrations; }
        public void setTodayRegistrations(long todayRegistrations) { this.todayRegistrations = todayRegistrations; }
    }
}
