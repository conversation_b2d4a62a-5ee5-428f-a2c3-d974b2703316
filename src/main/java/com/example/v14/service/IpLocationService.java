package com.example.v14.service;

import com.example.v14.util.IpUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * IP地理位置查询服务
 */
@Slf4j
@Service
public class IpLocationService {
    
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;
    
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    
    public IpLocationService(RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }
    
    private static final String LOCATION_CACHE_KEY = "ip:location:";
    private static final long LOCATION_CACHE_TTL = 24 * 60 * 60; // 24小时
    private static final String IP_API_URL = "http://ip-api.com/json/";
    private static final String IPINFO_API_URL = "http://ipinfo.io/";
    
    /**
     * 同步获取IP地理位置
     */
    public String getIpLocation(String ipAddress) {
        if (!IpUtil.isValidIp(ipAddress)) {
            return "无效IP";
        }
        
        // 本地IP和内网IP直接返回
        if (IpUtil.isLocalIp(ipAddress)) {
            return "本地";
        }
        
        if (IpUtil.isPrivateIp(ipAddress)) {
            return "内网";
        }
        
        // 尝试从缓存获取
        String cacheKey = LOCATION_CACHE_KEY + ipAddress;
        if (redisTemplate != null) {
            try {
                Object cached = redisTemplate.opsForValue().get(cacheKey);
                if (cached != null) {
                    log.debug("从缓存获取IP地理位置: {} -> {}", ipAddress, cached);
                    return cached.toString();
                }
            } catch (Exception e) {
                log.warn("获取地理位置缓存失败: {}", e.getMessage());
            }
        }
        
        // 查询地理位置
        String location = queryLocationFromApi(ipAddress);
        
        // 缓存结果
        if (redisTemplate != null) {
            try {
                redisTemplate.opsForValue().set(cacheKey, location, LOCATION_CACHE_TTL, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.warn("缓存地理位置失败: {}", e.getMessage());
            }
        }
        
        return location;
    }
    
    /**
     * 异步获取IP地理位置
     */
    @Async
    public CompletableFuture<String> getIpLocationAsync(String ipAddress) {
        return CompletableFuture.completedFuture(getIpLocation(ipAddress));
    }
    
    /**
     * 从API查询地理位置
     */
    private String queryLocationFromApi(String ipAddress) {
        // 优先使用ip-api.com
        String location = queryFromIpApi(ipAddress);
        if (location != null && !location.equals("未知")) {
            return location;
        }
        
        // 备用ipinfo.io
        location = queryFromIpInfo(ipAddress);
        if (location != null && !location.equals("未知")) {
            return location;
        }
        
        return "未知";
    }
    
    /**
     * 从ip-api.com查询地理位置
     */
    private String queryFromIpApi(String ipAddress) {
        try {
            String url = IP_API_URL + ipAddress + "?lang=zh-CN&fields=status,country,regionName,city";
            log.debug("查询IP地理位置: {}", url);
            
            String response = restTemplate.getForObject(url, String.class);
            if (response == null) {
                log.warn("IP-API返回空响应: {}", ipAddress);
                return "未知";
            }
            
            JsonNode json = objectMapper.readTree(response);
            String status = json.get("status").asText();
            
            if (!"success".equals(status)) {
                log.warn("IP-API查询失败: {} - {}", ipAddress, json.get("message").asText(""));
                return "未知";
            }
            
            String country = json.get("country").asText("");
            String region = json.get("regionName").asText("");
            String city = json.get("city").asText("");
            
            // 格式化地址
            StringBuilder location = new StringBuilder();
            if (!country.isEmpty()) {
                location.append(country);
            }
            if (!region.isEmpty() && !region.equals(country)) {
                if (location.length() > 0) location.append(",");
                location.append(region);
            }
            if (!city.isEmpty() && !city.equals(region)) {
                if (location.length() > 0) location.append(",");
                location.append(city);
            }
            
            String result = location.toString();
            if (result.isEmpty()) {
                result = "未知";
            }
            
            log.debug("IP地理位置查询成功: {} -> {}", ipAddress, result);
            return result;
            
        } catch (Exception e) {
            log.warn("IP-API查询异常: {} - {}", ipAddress, e.getMessage());
            return "未知";
        }
    }
    
    /**
     * 从ipinfo.io查询地理位置
     */
    private String queryFromIpInfo(String ipAddress) {
        try {
            String url = IPINFO_API_URL + ipAddress + "/json";
            log.debug("备用查询IP地理位置: {}", url);
            
            String response = restTemplate.getForObject(url, String.class);
            if (response == null) {
                log.warn("IPInfo返回空响应: {}", ipAddress);
                return "未知";
            }
            
            JsonNode json = objectMapper.readTree(response);
            
            String country = json.has("country") ? json.get("country").asText("") : "";
            String region = json.has("region") ? json.get("region").asText("") : "";
            String city = json.has("city") ? json.get("city").asText("") : "";
            
            // 格式化地址
            StringBuilder location = new StringBuilder();
            if (!country.isEmpty()) {
                location.append(translateCountry(country));
            }
            if (!region.isEmpty()) {
                if (location.length() > 0) location.append(",");
                location.append(region);
            }
            if (!city.isEmpty()) {
                if (location.length() > 0) location.append(",");
                location.append(city);
            }
            
            String result = location.toString();
            if (result.isEmpty()) {
                result = "未知";
            }
            
            log.debug("IPInfo地理位置查询成功: {} -> {}", ipAddress, result);
            return result;
            
        } catch (Exception e) {
            log.warn("IPInfo查询异常: {} - {}", ipAddress, e.getMessage());
            return "未知";
        }
    }
    
    /**
     * 翻译国家代码为中文
     */
    private String translateCountry(String countryCode) {
        switch (countryCode.toUpperCase()) {
            case "CN": return "中国";
            case "US": return "美国";
            case "JP": return "日本";
            case "KR": return "韩国";
            case "GB": return "英国";
            case "DE": return "德国";
            case "FR": return "法国";
            case "CA": return "加拿大";
            case "AU": return "澳大利亚";
            case "RU": return "俄罗斯";
            case "IN": return "印度";
            case "BR": return "巴西";
            case "SG": return "新加坡";
            case "HK": return "香港";
            case "TW": return "台湾";
            default: return countryCode;
        }
    }
    
    /**
     * 批量查询IP地理位置
     */
    @Async
    public CompletableFuture<Void> batchQueryLocations(java.util.List<String> ipAddresses) {
        log.info("开始批量查询IP地理位置，共 {} 个IP", ipAddresses.size());
        
        for (String ip : ipAddresses) {
            try {
                getIpLocation(ip);
                // 避免API频率限制
                Thread.sleep(100);
            } catch (Exception e) {
                log.warn("批量查询IP地理位置失败: {} - {}", ip, e.getMessage());
            }
        }
        
        log.info("批量查询IP地理位置完成");
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 清理地理位置缓存
     */
    public void clearLocationCache() {
        try {
            var keys = redisTemplate.keys(LOCATION_CACHE_KEY + "*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("清理地理位置缓存完成，共 {} 条", keys.size());
            }
        } catch (Exception e) {
            log.warn("清理地理位置缓存失败: {}", e.getMessage());
        }
    }
    
    /**
     * 获取地理位置缓存统计
     */
    public java.util.Map<String, Object> getLocationCacheStats() {
        java.util.Map<String, Object> stats = new java.util.HashMap<>();
        
        try {
            var keys = redisTemplate.keys(LOCATION_CACHE_KEY + "*");
            stats.put("cachedLocationCount", keys != null ? keys.size() : 0);
            
            // 统计不同地理位置的IP数量
            java.util.Map<String, Integer> locationStats = new java.util.HashMap<>();
            if (keys != null) {
                for (String key : keys) {
                    try {
                        Object location = redisTemplate.opsForValue().get(key);
                        if (location != null) {
                            String locationStr = location.toString();
                            locationStats.put(locationStr, locationStats.getOrDefault(locationStr, 0) + 1);
                        }
                    } catch (Exception e) {
                        log.debug("获取缓存位置信息失败: {}", key);
                    }
                }
            }
            
            stats.put("locationDistribution", locationStats);
            
        } catch (Exception e) {
            log.warn("获取地理位置缓存统计失败: {}", e.getMessage());
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }
}