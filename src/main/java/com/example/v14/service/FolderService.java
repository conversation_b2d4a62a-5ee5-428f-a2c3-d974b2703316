package com.example.v14.service;

import com.example.v14.entity.Folder;
import com.example.v14.entity.FileInfo;
import com.example.v14.mapper.FolderMapper;
import com.example.v14.mapper.FileMapper;
import com.example.v14.config.FileConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文件夹服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FolderService {

    private final FolderMapper folderMapper;
    private final FileMapper fileMapper;
    private final FileService fileService;
    private final FileConfig fileConfig;
    
    /**
     * 根据ID查询文件夹
     */
    public Folder findById(Long id) {
        return folderMapper.findById(id);
    }
    
    /**
     * 创建文件夹
     */
    @Transactional
    public Folder createFolder(String name, Long parentId, Long userId) {
        // 安全处理parentId - 避免空指针异常
        Long actualParentId = (parentId != null && !parentId.equals(0L)) ? parentId : null;

        // 检查文件夹名称是否已存在
        boolean nameExists;
        if (actualParentId == null) {
            nameExists = folderMapper.existsByNameInRoot(userId, name, 0L) > 0;
        } else {
            nameExists = folderMapper.existsByName(userId, actualParentId, name, 0L) > 0;
        }
        
        if (nameExists) {
            throw new RuntimeException("文件夹名称已存在");
        }

        // 构建路径
        String path = buildFolderPath(actualParentId, name, userId);

        Folder folder = Folder.builder()
                .name(name)
                .parentId(actualParentId)
                .userId(userId)
                .path(path)
                .isDeleted(false)
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();

        folderMapper.insert(folder);
        return folder;
    }
    
    /**
     * 重命名文件夹
     */
    @Transactional
    public void renameFolder(Long folderId, String newName, Long userId) {
        Folder folder = folderMapper.findById(folderId);
        if (folder == null || !folder.getUserId().equals(userId)) {
            throw new RuntimeException("文件夹不存在或无权限");
        }
        
        // 检查新名称是否已存在
        if (folderMapper.existsByName(userId, folder.getParentId(), newName, folderId) > 0) {
            throw new RuntimeException("文件夹名称已存在");
        }
        
        // 更新文件夹信息
        String newPath = buildFolderPath(folder.getParentId(), newName, userId);
        folder.setName(newName);
        folder.setPath(newPath);
        folder.setUpdatedTime(LocalDateTime.now());
        
        folderMapper.update(folder);
        
        // 更新子文件夹路径
        updateSubFolderPaths(folder, userId);
    }
    
    /**
     * 移动文件夹
     */
    @Transactional
    public void moveFolder(Long folderId, Long newParentId, Long userId) {
        log.info("FolderService.moveFolder 开始: folderId={}, newParentId={}, userId={}", folderId, newParentId, userId);

        Folder folder = folderMapper.findById(folderId);
        if (folder == null || !folder.getUserId().equals(userId)) {
            log.error("文件夹不存在或无权限: folderId={}, userId={}, folder={}", folderId, userId, folder);
            throw new RuntimeException("文件夹不存在或无权限");
        }

        // 安全处理newParentId - 避免空指针异常
        Long actualParentId = (newParentId != null && !newParentId.equals(0L)) ? newParentId : null;
        log.info("转换后的父文件夹ID: {} -> {}", newParentId, actualParentId);

        // 检查是否移动到自己的子文件夹
        if (isSubFolder(folderId, actualParentId, userId)) {
            log.error("不能移动到自己的子文件夹: folderId={}, actualParentId={}", folderId, actualParentId);
            throw new RuntimeException("不能移动到自己的子文件夹");
        }

        // 检查目标位置是否已有同名文件夹
        int existingCount = folderMapper.existsByName(userId, actualParentId, folder.getName(), folderId);
        if (existingCount > 0) {
            log.error("目标位置已存在同名文件夹: userId={}, actualParentId={}, name={}, existingCount={}",
                     userId, actualParentId, folder.getName(), existingCount);
            throw new RuntimeException("目标位置已存在同名文件夹");
        }

        // 更新文件夹路径
        String newPath = buildFolderPath(actualParentId, folder.getName(), userId);
        log.info("构建新路径: {}", newPath);

        int updateResult = folderMapper.move(folderId, actualParentId, newPath, LocalDateTime.now());
        log.info("数据库更新结果: {}", updateResult);

        if (updateResult <= 0) {
            throw new RuntimeException("文件夹移动失败：数据库更新失败");
        }

        // 暂时跳过子文件夹路径更新，先确保基本移动功能正常
        // TODO: 后续优化子文件夹路径更新逻辑
        log.info("FolderService.moveFolder 完成: folderId={}", folderId);
    }
    
    /**
     * 删除文件夹（软删除）
     */
    @Transactional
    public void deleteFolder(Long folderId, Long userId) {
        log.info("=== 开始删除文件夹 ===");
        log.info("文件夹ID: {}, 用户ID: {}", folderId, userId);

        Folder folder = folderMapper.findById(folderId);
        if (folder == null || !folder.getUserId().equals(userId)) {
            log.error("文件夹不存在或无权限: folderId={}, userId={}", folderId, userId);
            throw new RuntimeException("文件夹不存在或无权限");
        }

        log.info("找到文件夹: ID={}, 名称={}, 路径={}", folder.getId(), folder.getName(), folder.getPath());

        // 递归删除文件夹及其内容
        deleteFolderRecursively(folder, userId);

        log.info("文件夹删除成功: {}, 用户: {}", folder.getName(), userId);
        log.info("=== 删除文件夹完成 ===");
    }

    /**
     * 递归删除文件夹及其所有内容
     */
    private void deleteFolderRecursively(Folder folder, Long userId) {
        log.info("递归删除文件夹: ID={}, 名称={}, 路径={}", folder.getId(), folder.getName(), folder.getPath());

        // 1. 删除文件夹内的所有文件
        List<FileInfo> filesInFolder = fileMapper.findByUserAndFolder(userId, folder.getId());
        if (!filesInFolder.isEmpty()) {
            log.info("删除文件夹内的 {} 个文件", filesInFolder.size());
            List<Long> fileIds = filesInFolder.stream().map(FileInfo::getId).collect(Collectors.toList());
            fileService.batchDeleteFiles(fileIds, userId);
        }

        // 2. 递归删除所有子文件夹
        List<Folder> subFolders = folderMapper.findByUserAndParent(userId, folder.getId());
        for (Folder subFolder : subFolders) {
            deleteFolderRecursively(subFolder, userId);
        }

        // 3. 软删除当前文件夹
        LocalDateTime deleteTime = LocalDateTime.now();
        folderMapper.softDelete(folder.getId(), deleteTime);
        log.info("文件夹软删除完成: ID={}, 名称={}, 删除时间={}", folder.getId(), folder.getName(), deleteTime);
    }
    
    /**
     * 获取用户的文件夹列表
     */
    public List<Folder> getUserFolders(Long userId, Long parentId) {
        if (parentId == null || parentId.equals(0L)) {
            return folderMapper.findRootFoldersByUser(userId);
        }
        return folderMapper.findByUserAndParent(userId, parentId);
    }
    
    /**
     * 获取用户所有文件夹
     */
    public List<Folder> getAllUserFolders(Long userId) {
        return folderMapper.findByUser(userId);
    }

    /**
     * 获取回收站文件夹
     */
    public List<Folder> getDeletedFolders(Long userId, int page, int size) {
        int offset = (page - 1) * size;
        return folderMapper.findDeletedFolders(userId, offset, size);
    }

    /**
     * 恢复删除的文件夹
     */
    @Transactional
    public void restoreFolder(Long folderId, Long userId) {
        log.info("=== 开始恢复文件夹 ===");
        log.info("文件夹ID: {}, 用户ID: {}", folderId, userId);

        Folder folder = folderMapper.findByIdIncludeDeleted(folderId);
        if (folder == null || !folder.getUserId().equals(userId) || !folder.isDeleted()) {
            log.error("文件夹不存在或无权限或未删除: folderId={}, userId={}", folderId, userId);
            throw new RuntimeException("文件夹不存在或无权限");
        }

        log.info("找到已删除文件夹: ID={}, 名称={}, 路径={}", folder.getId(), folder.getName(), folder.getPath());

        // 递归恢复文件夹及其内容
        restoreFolderRecursively(folder, userId);

        log.info("文件夹恢复成功: {}, 用户: {}", folder.getName(), userId);
        log.info("=== 恢复文件夹完成 ===");
    }

    /**
     * 递归恢复文件夹及其所有内容
     */
    private void restoreFolderRecursively(Folder folder, Long userId) {
        log.info("递归恢复文件夹: ID={}, 名称={}, 路径={}", folder.getId(), folder.getName(), folder.getPath());

        // 1. 恢复当前文件夹
        folderMapper.restore(folder.getId());
        log.info("文件夹恢复完成: ID={}, 名称={}", folder.getId(), folder.getName());

        // 2. 恢复文件夹内的所有文件
        List<FileInfo> deletedFilesInFolder = fileMapper.findDeletedFilesByFolder(userId, folder.getId());
        for (FileInfo file : deletedFilesInFolder) {
            try {
                fileService.restoreFile(file.getId(), userId);
            } catch (Exception e) {
                log.error("恢复文件失败: fileId={}, fileName={}", file.getId(), file.getName(), e);
            }
        }

        // 3. 递归恢复所有子文件夹
        List<Folder> deletedSubFolders = folderMapper.findDeletedSubFolders(userId, folder.getId());
        for (Folder subFolder : deletedSubFolders) {
            restoreFolderRecursively(subFolder, userId);
        }
    }

    /**
     * 永久删除文件夹
     */
    @Transactional
    public void permanentDeleteFolder(Long folderId, Long userId) {
        log.info("=== 开始永久删除文件夹 ===");
        log.info("文件夹ID: {}, 用户ID: {}", folderId, userId);

        Folder folder = folderMapper.findByIdIncludeDeleted(folderId);
        if (folder == null) {
            log.warn("尝试删除不存在的文件夹: folderId={}, userId={}", folderId, userId);
            return; // 文件夹不存在时直接返回，不抛异常
        }

        if (!folder.getUserId().equals(userId)) {
            log.error("权限检查失败: 文件夹所有者={}, 当前用户={}", folder.getUserId(), userId);
            throw new RuntimeException("无权限删除此文件夹");
        }

        // 检查文件夹是否在回收站状态
        if (!folder.isDeleted()) {
            log.warn("文件夹不在回收站状态，无法永久删除: folderId={}", folderId);
            throw new RuntimeException("文件夹不在回收站中，无法永久删除");
        }

        log.info("找到已删除文件夹: ID={}, 名称={}, 路径={}", folder.getId(), folder.getName(), folder.getPath());

        // 递归永久删除文件夹及其内容
        permanentDeleteFolderRecursively(folder, userId);

        log.info("文件夹永久删除成功: {}, 用户: {}", folder.getName(), userId);
        log.info("=== 永久删除文件夹完成 ===");
    }

    /**
     * 递归永久删除文件夹及其所有内容
     */
    private void permanentDeleteFolderRecursively(Folder folder, Long userId) {
        log.info("递归永久删除文件夹: ID={}, 名称={}, 路径={}", folder.getId(), folder.getName(), folder.getPath());

        // 1. 永久删除文件夹内的所有已删除文件
        List<FileInfo> deletedFilesInFolder = fileMapper.findDeletedFilesByFolder(userId, folder.getId());
        for (FileInfo file : deletedFilesInFolder) {
            try {
                fileService.permanentDeleteFile(file.getId(), userId);
            } catch (Exception e) {
                log.error("永久删除文件失败: fileId={}, fileName={}", file.getId(), file.getName(), e);
            }
        }

        // 2. 递归永久删除所有已删除的子文件夹
        List<Folder> deletedSubFolders = folderMapper.findDeletedSubFolders(userId, folder.getId());
        for (Folder subFolder : deletedSubFolders) {
            permanentDeleteFolderRecursively(subFolder, userId);
        }

        // 3. 物理删除当前文件夹
        folderMapper.deleteById(folder.getId());
        log.info("文件夹物理删除完成: ID={}, 名称={}", folder.getId(), folder.getName());
    }
    
    /**
     * 构建文件夹路径
     */
    private String buildFolderPath(Long parentId, String name, Long userId) {
        if (parentId == null || parentId == 0) {
            return "/" + name;
        }
        
        Folder parent = folderMapper.findById(parentId);
        if (parent == null || !parent.getUserId().equals(userId)) {
            throw new RuntimeException("父文件夹不存在");
        }
        
        return parent.getPath() + "/" + name;
    }
    
    /**
     * 更新子文件夹路径
     */
    private void updateSubFolderPaths(Folder parentFolder, Long userId) {
        String oldPathPrefix = parentFolder.getPath().replaceAll("/[^/]+$", "");
        List<Folder> subFolders = folderMapper.findSubFolders(userId, oldPathPrefix);

        log.info("需要更新路径的子文件夹数量: {}", subFolders.size());

        for (Folder subFolder : subFolders) {
            String newPath = subFolder.getPath().replaceFirst("^" + oldPathPrefix, parentFolder.getPath());
            log.info("更新子文件夹路径: {} -> {}", subFolder.getPath(), newPath);

            // 使用单个更新而不是批量更新，避免SQL语法错误
            folderMapper.updatePath(subFolder.getId(), newPath, LocalDateTime.now());
        }
    }
    
    /**
     * 检查是否为子文件夹
     */
    private boolean isSubFolder(Long folderId, Long targetParentId, Long userId) {
        if (targetParentId == null || targetParentId == 0) {
            return false;
        }
        
        Folder current = folderMapper.findById(targetParentId);
        while (current != null && current.getUserId().equals(userId)) {
            if (current.getId().equals(folderId)) {
                return true;
            }
            if (current.getParentId() == null || current.getParentId() == 0) {
                break;
            }
            current = folderMapper.findById(current.getParentId());
        }
        
        return false;
    }
    
    /**
     * 获取文件夹路径链
     */
    public List<Folder> getFolderPath(Long folderId, Long userId) {
        List<Folder> path = new ArrayList<>();

        if (folderId == null || Long.valueOf(0).equals(folderId)) {
            return path; // 根目录，返回空路径
        }

        Folder current = folderMapper.findById(folderId);

        // 从当前文件夹向上遍历到根目录
        while (current != null && current.getUserId().equals(userId)) {
            path.add(0, current); // 添加到列表开头，保持正确顺序

            if (current.getParentId() == null || current.getParentId() == 0) {
                break; // 到达根目录
            }

            current = folderMapper.findById(current.getParentId());
        }

        return path;
    }
    
    /**
     * 计算文件夹总大小（包含所有子文件夹）
     */
    public long calculateFolderSize(Long folderId, Long userId) {
        long totalSize = 0;
        
        // 获取文件夹内的所有文件大小
        List<FileInfo> files = fileMapper.findByUserAndFolder(userId, folderId);
        for (FileInfo file : files) {
            if (!Boolean.TRUE.equals(file.getIsDeleted())) {
                totalSize += file.getFileSize();
            }
        }
        
        // 递归计算子文件夹大小
        List<Folder> subFolders = folderMapper.findByUserAndParent(userId, folderId);
        for (Folder subFolder : subFolders) {
            if (!subFolder.isDeleted()) {
                totalSize += calculateFolderSize(subFolder.getId(), userId);
            }
        }
        
        return totalSize;
    }
    
    /**
     * 下载文件夹（打包成ZIP）
     */
    public File downloadFolder(Long folderId, Long userId) throws IOException {
        // 验证文件夹权限
        Folder folder = folderMapper.findById(folderId);
        if (folder == null || !folder.getUserId().equals(userId)) {
            throw new RuntimeException("文件夹不存在或无权限");
        }
        
        // 创建临时ZIP文件
        String zipFileName = "folder_" + folder.getName() + "_" + System.currentTimeMillis() + ".zip";
        String zipFilePath = fileConfig.getTempPath() + zipFileName;
        File zipFile = new File(zipFilePath);
        
        // 确保临时目录存在
        File tempDir = new File(fileConfig.getTempPath());
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }
        
        // 创建ZIP文件
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
            addFolderToZip(zos, folderId, userId, "");
        }
        
        log.info("文件夹打包下载成功: folderId={}, userId={}, zipFile={}", folderId, userId, zipFile.getName());
        return zipFile;
    }
    
    /**
     * 递归将文件夹内容添加到ZIP
     */
    private void addFolderToZip(ZipOutputStream zos, Long folderId, Long userId, String parentPath) throws IOException {
        // 获取文件夹中的所有文件
        List<FileInfo> files = fileMapper.findByUserAndFolder(userId, folderId);
        for (FileInfo fileInfo : files) {
            String filePath = fileConfig.getUploadPath() + fileInfo.getFilePath();
            File file = new File(filePath);
            
            if (file.exists()) {
                // 构建ZIP中的文件路径
                String zipEntryName = parentPath + fileInfo.getOriginalName();
                ZipEntry zipEntry = new ZipEntry(zipEntryName);
                zos.putNextEntry(zipEntry);
                
                try (FileInputStream fis = new FileInputStream(file)) {
                    byte[] buffer = new byte[8192];
                    int length;
                    while ((length = fis.read(buffer)) > 0) {
                        zos.write(buffer, 0, length);
                    }
                }
                
                zos.closeEntry();
            }
        }
        
        // 获取子文件夹并递归处理
        List<Folder> subFolders = folderMapper.findByUserAndParent(userId, folderId);
        for (Folder subFolder : subFolders) {
            String subFolderPath = parentPath + subFolder.getName() + "/";
            // 创建文件夹条目
            ZipEntry folderEntry = new ZipEntry(subFolderPath);
            zos.putNextEntry(folderEntry);
            zos.closeEntry();
            
            // 递归处理子文件夹
            addFolderToZip(zos, subFolder.getId(), userId, subFolderPath);
        }
    }
}
