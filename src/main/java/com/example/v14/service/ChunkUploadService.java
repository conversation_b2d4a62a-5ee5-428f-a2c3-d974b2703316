package com.example.v14.service;

import com.example.v14.entity.ChunkUpload;
import com.example.v14.entity.FileInfo;
import com.example.v14.config.FileConfig;
import com.example.v14.mapper.FileMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 分片上传服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChunkUploadService {

    private final FileConfig fileConfig;
    private final FileService fileService;
    private final UserService userService;
    private final StorageUsageService storageUsageService;
    private final FileMapper fileMapper;
    
    // 内存中存储上传状态，实际项目中可以使用Redis
    private final Map<String, ChunkUpload> uploadCache = new ConcurrentHashMap<>();
    
    /**
     * 初始化分片上传
     */
    public ChunkUpload initChunkUpload(String fileName, Long fileSize, String fileMd5, 
                                      Long chunkSize, Long userId, Long folderId) {
        // 检查用户权限
        if (!userService.canUploadFile(userId, fileSize)) {
            throw new RuntimeException("文件大小超出限制或存储空间不足");
        }
        
        // 计算分片数
        int totalChunks = (int) Math.ceil((double) fileSize / chunkSize);
        
        // 生成上传ID
        String uploadId = generateUploadId();
        
        // 创建临时目录
        String tempPath = createTempDirectory(uploadId);
        
        ChunkUpload chunkUpload = ChunkUpload.builder()
                .uploadId(uploadId)
                .fileName(fileName)
                .fileSize(fileSize)
                .fileMd5(fileMd5)
                .chunkSize(chunkSize)
                .totalChunks(totalChunks)
                .uploadedChunks(new HashSet<>())
                .userId(userId)
                .folderId(folderId)
                .tempPath(tempPath)
                .status(ChunkUpload.UploadStatus.UPLOADING)
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .expireTime(LocalDateTime.now().plusHours(24)) // 24小时过期
                .build();
        
        uploadCache.put(uploadId, chunkUpload);
        
        log.info("初始化分片上传: uploadId={}, fileName={}, totalChunks={}", 
                uploadId, fileName, totalChunks);
        
        return chunkUpload;
    }
    
    /**
     * 上传分片
     */
    public ChunkUpload uploadChunk(String uploadId, Integer chunkNumber, MultipartFile chunkFile) throws IOException {
        ChunkUpload chunkUpload = uploadCache.get(uploadId);
        if (chunkUpload == null) {
            throw new RuntimeException("上传会话不存在或已过期");
        }
        
        if (chunkUpload.isExpired()) {
            throw new RuntimeException("上传会话已过期");
        }
        
        // 保存分片文件
        String chunkFileName = String.format("chunk_%d", chunkNumber);
        Path chunkPath = Paths.get(chunkUpload.getTempPath(), chunkFileName);
        
        try (InputStream inputStream = chunkFile.getInputStream()) {
            Files.copy(inputStream, chunkPath, java.nio.file.StandardCopyOption.REPLACE_EXISTING);
        }
        
        // 更新上传状态
        chunkUpload.getUploadedChunks().add(chunkNumber);
        chunkUpload.setUpdatedTime(LocalDateTime.now());
        
        log.debug("分片上传成功: uploadId={}, chunk={}/{}", 
                uploadId, chunkNumber, chunkUpload.getTotalChunks());
        
        // 检查是否完成
        if (chunkUpload.isCompleted()) {
            return completeUpload(chunkUpload);
        }
        
        return chunkUpload;
    }
    
    /**
     * 完成上传
     */
    private ChunkUpload completeUpload(ChunkUpload chunkUpload) throws IOException {
        log.info("开始合并分片: uploadId={}", chunkUpload.getUploadId());
        
        // 合并分片
        String finalFilePath = mergeChunks(chunkUpload);
        
        // 验证文件MD5
        if (!verifyFileMd5(finalFilePath, chunkUpload.getFileMd5())) {
            throw new RuntimeException("文件MD5校验失败");
        }
        
        // 创建文件记录
        FileInfo fileInfo = createFileRecord(chunkUpload, finalFilePath);
        
        // 使用专门的存储使用量服务
        storageUsageService.increaseStorageUsageAsync(chunkUpload.getUserId(), chunkUpload.getFileSize());
        
        // 清理临时文件
        cleanupTempFiles(chunkUpload);
        
        chunkUpload.setStatus(ChunkUpload.UploadStatus.COMPLETED);
        chunkUpload.setUpdatedTime(LocalDateTime.now());
        
        log.info("分片上传完成: uploadId={}, fileId={}", 
                chunkUpload.getUploadId(), fileInfo.getId());
        
        return chunkUpload;
    }
    
    /**
     * 获取上传状态
     */
    public ChunkUpload getUploadStatus(String uploadId) {
        return uploadCache.get(uploadId);
    }
    
    /**
     * 取消上传
     */
    public void cancelUpload(String uploadId) {
        ChunkUpload chunkUpload = uploadCache.get(uploadId);
        if (chunkUpload != null) {
            cleanupTempFiles(chunkUpload);
            uploadCache.remove(uploadId);
            log.info("取消分片上传: uploadId={}", uploadId);
        }
    }
    
    /**
     * 生成上传ID
     */
    private String generateUploadId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 创建临时目录
     */
    private String createTempDirectory(String uploadId) {
        try {
            String tempPath = fileConfig.getTempPath() + File.separator + uploadId;
            Path path = Paths.get(tempPath);
            Files.createDirectories(path);
            return tempPath;
        } catch (IOException e) {
            throw new RuntimeException("创建临时目录失败", e);
        }
    }
    
    /**
     * 合并分片
     */
    private String mergeChunks(ChunkUpload chunkUpload) throws IOException {
        String fileName = chunkUpload.getFileName();
        String storagePath = generateStoragePath(fileName);
        String fullPath = fileConfig.getUploadPath() + storagePath;
        
        // 创建目标目录
        Path targetPath = Paths.get(fullPath);
        Files.createDirectories(targetPath.getParent());
        
        // 合并分片
        try (FileOutputStream fos = new FileOutputStream(fullPath)) {
            for (int i = 0; i < chunkUpload.getTotalChunks(); i++) {
                String chunkFileName = String.format("chunk_%d", i);
                Path chunkPath = Paths.get(chunkUpload.getTempPath(), chunkFileName);
                
                if (!Files.exists(chunkPath)) {
                    throw new IOException("分片文件不存在: " + chunkFileName);
                }
                
                Files.copy(chunkPath, fos);
            }
        }
        
        return storagePath;
    }
    
    /**
     * 验证文件MD5
     */
    private boolean verifyFileMd5(String filePath, String expectedMd5) {
        try {
            String actualMd5 = calculateFileMd5(fileConfig.getUploadPath() + filePath);
            return expectedMd5.equalsIgnoreCase(actualMd5);
        } catch (Exception e) {
            log.error("MD5校验失败", e);
            return false;
        }
    }
    
    /**
     * 计算文件MD5
     */
    private String calculateFileMd5(String filePath) throws Exception {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        try (FileInputStream fis = new FileInputStream(filePath)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                md5.update(buffer, 0, bytesRead);
            }
        }
        
        byte[] digest = md5.digest();
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }
    
    /**
     * 创建文件记录
     */
    private FileInfo createFileRecord(ChunkUpload chunkUpload, String storagePath) {
        try {
            // 安全处理folderId - 避免空指针异常
            Long folderId = chunkUpload.getFolderId();
            Long targetFolderId = (folderId != null && !folderId.equals(0L)) ? folderId : null;
            
            FileInfo fileInfo = FileInfo.builder()
                    .name(chunkUpload.getFileName())
                    .originalName(chunkUpload.getFileName())
                    .fileType(getFileType(chunkUpload.getFileName()))
                    .fileSize(chunkUpload.getFileSize())
                    .filePath(storagePath)
                    .folderId(targetFolderId) // 使用安全处理的folderId
                    .userId(chunkUpload.getUserId())
                    .md5Hash(chunkUpload.getFileMd5())
                    .downloadCount(0)
                    .isDeleted(false)
                    .createdTime(LocalDateTime.now())
                    .updatedTime(LocalDateTime.now())
                    .build();

            // 保存文件记录到数据库
            int result = fileMapper.insert(fileInfo);
            if (result <= 0) {
                throw new RuntimeException("保存文件记录失败");
            }

            log.info("文件记录创建成功: fileId={}, fileName={}, filePath={}, folderId={}",
                    fileInfo.getId(), fileInfo.getName(), fileInfo.getFilePath(), targetFolderId);

            return fileInfo;
        } catch (Exception e) {
            log.error("创建文件记录失败", e);
            throw new RuntimeException("创建文件记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取文件类型
     */
    private String getFileType(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();

        // 根据扩展名返回MIME类型
        switch (extension) {
            case ".jpg":
            case ".jpeg":
                return "image/jpeg";
            case ".png":
                return "image/png";
            case ".gif":
                return "image/gif";
            case ".pdf":
                return "application/pdf";
            case ".txt":
                return "text/plain";
            case ".doc":
                return "application/msword";
            case ".docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case ".mp4":
                return "video/mp4";
            case ".mp3":
                return "audio/mpeg";
            default:
                return "application/octet-stream";
        }
    }
    
    /**
     * 生成存储路径
     */
    private String generateStoragePath(String fileName) {
        // 使用日期和UUID生成存储路径
        String date = LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String extension = getFileExtension(fileName);
        return date + "/" + uuid + extension;
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex) : "";
    }
    
    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(ChunkUpload chunkUpload) {
        try {
            Path tempDir = Paths.get(chunkUpload.getTempPath());
            if (Files.exists(tempDir)) {
                Files.walk(tempDir)
                        .sorted(Comparator.reverseOrder())
                        .map(Path::toFile)
                        .forEach(File::delete);
            }
        } catch (IOException e) {
            log.error("清理临时文件失败: {}", chunkUpload.getTempPath(), e);
        }
    }
    
    /**
     * 清理过期的上传会话
     */
    public void cleanupExpiredUploads() {
        uploadCache.entrySet().removeIf(entry -> {
            ChunkUpload upload = entry.getValue();
            if (upload.isExpired()) {
                cleanupTempFiles(upload);
                log.info("清理过期上传会话: uploadId={}", upload.getUploadId());
                return true;
            }
            return false;
        });
    }


}
