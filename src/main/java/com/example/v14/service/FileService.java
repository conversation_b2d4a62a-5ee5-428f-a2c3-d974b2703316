package com.example.v14.service;

import com.example.v14.config.FileConfig;
import com.example.v14.controller.FileController;
import com.example.v14.entity.FileInfo;
import com.example.v14.mapper.FileMapper;
import com.example.v14.service.LoggingService;
import com.example.v14.service.AnalyticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文件服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileService {
    
    private final FileMapper fileMapper;
    private final FileConfig fileConfig;
    private final UserService userService;
    private final LoggingService loggingService;
    private final AnalyticsService analyticsService;
    private final StorageUsageService storageUsageService;
    
    /**
     * 上传文件
     */
    @Transactional
    public FileInfo uploadFile(MultipartFile file, Long folderId, Long userId) throws IOException {
        return uploadFile(file, folderId, userId, true);
    }

    /**
     * 上传文件（可选择是否立即更新存储使用量）
     */
    @Transactional
    public FileInfo uploadFile(MultipartFile file, Long folderId, Long userId, boolean updateStorageImmediately) throws IOException {
        log.info("=== 开始文件上传流程 ===");
        log.info("文件名: {}, 大小: {}, 用户ID: {}, 文件夹ID: {}, 立即更新存储: {}",
                file.getOriginalFilename(), file.getSize(), userId, folderId, updateStorageImmediately);

        // 验证文件
        validateFile(file, userId);

        // 生成文件存储路径
        String storagePath = generateStoragePath(file.getOriginalFilename());
        String fullPath = fileConfig.getUploadPath() + storagePath;
        log.info("生成存储路径: {}", fullPath);

        // 创建目录
        Path path = Paths.get(fullPath);
        Files.createDirectories(path.getParent());

        // 一次性保存文件并计算MD5
        log.info("开始保存文件和计算MD5...");
        String md5Hash = saveFileAndCalculateMD5(file, fullPath);

        // 检查是否已存在相同文件，如果存在则生成新的文件名
        // 注意：对于文件夹上传，我们允许相同内容的文件保持原始文件名
        String finalFileName = file.getOriginalFilename();

        // 检查是否在同一文件夹下已存在同名文件
        FileInfo existingFileByName = fileMapper.findByNameAndFolderAndUser(finalFileName, folderId, userId);
        if (existingFileByName != null) {
            // 同一文件夹下有同名文件，生成新的文件名
            finalFileName = generateDuplicateFileName(file.getOriginalFilename(), folderId, userId);
            log.info("检测到同名文件，生成新文件名: {} -> {}", file.getOriginalFilename(), finalFileName);
        } else {
            // 检查是否已存在相同MD5的文件（仅在不同文件夹或不同名称时提示）
            // 使用 findFirstByMd5AndUser 避免多个结果的问题
            List<FileInfo> existingFilesByMd5 = fileMapper.findByMd5AndUserList(md5Hash, userId);
            if (!existingFilesByMd5.isEmpty()) {
                FileInfo firstExistingFile = existingFilesByMd5.get(0);
                // 安全比较folderId，处理null值情况
                if (!Objects.equals(firstExistingFile.getFolderId(), folderId)) {
                    log.info("检测到相同内容的文件已存在于其他位置: {} (文件夹ID: {})",
                            firstExistingFile.getName(), firstExistingFile.getFolderId());
                    // 不重命名，允许在不同文件夹中存在相同内容的文件
                }
            }
        }

        // 创建文件记录
        FileInfo fileInfo = FileInfo.builder()
                .name(finalFileName)
                .originalName(file.getOriginalFilename())
                .fileType(getFileType(finalFileName))
                .fileSize(file.getSize())
                .filePath(storagePath)
                .folderId((folderId != null && !Long.valueOf(0).equals(folderId)) ? folderId : null) // 根目录使用NULL而不是0
                .userId(userId)
                .md5Hash(md5Hash)
                .downloadCount(0)
                .isDeleted(false)
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();

        fileMapper.insert(fileInfo);

        // 根据参数决定是否更新用户存储使用量
        if (updateStorageImmediately) {
            // 使用专门的存储使用量服务，完全异步处理
            storageUsageService.increaseStorageUsageAsync(userId, file.getSize());
        }

        log.info("文件上传成功: {}, 用户: {}, 大小: {}", file.getOriginalFilename(), userId, file.getSize());

        return fileInfo;
    }

    /**
     * 异步更新存储使用量
     */
    @Async
    public void updateStorageUsedAsync(Long userId, Long size) {
        try {
            // 短暂延迟，让主事务先完成
            Thread.sleep(100);
            // 直接调用UserService的新事务方法，避免重试逻辑导致的超时
            userService.increaseStorageUsedInNewTransaction(userId, size);
            log.debug("异步存储使用量更新成功: userId={}, size={}", userId, size);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("异步更新存储使用量被中断: userId={}, size={}", userId, size);
        } catch (Exception e) {
            log.warn("异步更新存储使用量失败，将忽略此错误: userId={}, size={}, error={}", userId, size, e.getMessage());
            // 异步更新失败不影响主流程，只记录警告日志
        }
    }

    /**
     * 带重试机制的存储使用量更新（公共方法）
     * 使用新事务避免事务回滚问题
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateStorageUsedWithRetry(Long userId, Long size) {
        int maxRetries = 3;
        int retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                userService.increaseStorageUsedInNewTransaction(userId, size);
                log.debug("存储使用量更新成功: userId={}, size={}", userId, size);
                return; // 成功则退出
            } catch (Exception e) {
                retryCount++;
                String errorMessage = e.getMessage();

                // 检查是否是死锁异常或事务回滚异常
                if (errorMessage != null && (errorMessage.contains("Deadlock found") ||
                    errorMessage.contains("rollback-only") ||
                    errorMessage.contains("Transaction rolled back"))) {
                    log.warn("存储使用量更新遇到事务问题，第{}次重试: userId={}, size={}, error={}",
                            retryCount, userId, size, errorMessage);

                    if (retryCount < maxRetries) {
                        try {
                            // 随机等待100-300ms后重试，给数据库更多时间
                            Thread.sleep(100 + (long)(Math.random() * 200));
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    } else {
                        log.error("存储使用量更新重试{}次后仍失败: userId={}, size={}", maxRetries, userId, size, e);
                        // 最后一次重试失败，记录错误但不抛异常，避免影响文件上传
                    }
                } else {
                    // 非事务相关异常，直接抛出
                    log.error("存储使用量更新失败: userId={}, size={}", userId, size, e);
                    throw e;
                }
            }
        }
    }

    /**
     * 上传文件（包含日志记录）
     */
    @Transactional
    public FileInfo uploadFile(MultipartFile file, Long folderId, Long userId, jakarta.servlet.http.HttpServletRequest request) throws IOException {
        FileInfo fileInfo = uploadFile(file, folderId, userId);

        // 记录上传日志
        try {
            if (request != null) {
                loggingService.logFileUpload(userId, fileInfo.getId(), fileInfo.getName(), fileInfo.getFileSize(), request);
            }
        } catch (Exception e) {
            log.error("记录文件上传日志失败", e);
        }

        return fileInfo;
    }
    
    /**
     * 删除文件（软删除）
     */
    @Transactional
    public void deleteFile(Long fileId, Long userId) {
        FileInfo fileInfo = fileMapper.findById(fileId);
        if (fileInfo == null || !fileInfo.getUserId().equals(userId)) {
            throw new RuntimeException("文件不存在或无权限");
        }
        
        fileMapper.softDelete(fileId, LocalDateTime.now());
        
        // 更新用户存储使用量
        userService.decreaseStorageUsed(userId, fileInfo.getFileSize());
        
        log.info("文件删除成功: {}, 用户: {}", fileInfo.getName(), userId);
    }
    
    /**
     * 恢复删除的文件
     */
    @Transactional
    public void restoreFile(Long fileId, Long userId) {
        FileInfo fileInfo = fileMapper.findByIdIncludeDeleted(fileId);
        if (fileInfo == null || !fileInfo.getUserId().equals(userId) || !Boolean.TRUE.equals(fileInfo.getIsDeleted())) {
            throw new RuntimeException("文件不存在或无权限");
        }
        
        // 检查用户存储空间
        if (!userService.canUploadFile(userId, fileInfo.getFileSize())) {
            throw new RuntimeException("存储空间不足");
        }
        
        fileMapper.restore(fileId);
        
        // 使用专门的存储使用量服务
        storageUsageService.increaseStorageUsageAsync(userId, fileInfo.getFileSize());
        
        log.info("文件恢复成功: {}, 用户: {}", fileInfo.getName(), userId);
    }
    
    /**
     * 物理删除文件
     */
    @Transactional
    public void permanentDeleteFile(Long fileId, Long userId) {
        log.info("=== 开始永久删除文件 ===");
        log.info("文件ID: {}, 用户ID: {}", fileId, userId);

        FileInfo fileInfo = fileMapper.findByIdIncludeDeleted(fileId);
        if (fileInfo == null) {
            log.warn("尝试删除不存在的文件: fileId={}, userId={}", fileId, userId);
            return; // 文件不存在时直接返回，不抛异常
        }

        log.info("找到文件: ID={}, 名称={}, 用户={}, 删除状态={}, 文件路径={}",
                fileInfo.getId(), fileInfo.getName(), fileInfo.getUserId(),
                fileInfo.getIsDeleted(), fileInfo.getFilePath());

        if (!fileInfo.getUserId().equals(userId)) {
            log.error("权限检查失败: 文件所有者={}, 当前用户={}", fileInfo.getUserId(), userId);
            throw new RuntimeException("无权限删除此文件");
        }

        // 检查文件是否在回收站状态
        if (!Boolean.TRUE.equals(fileInfo.getIsDeleted())) {
            log.warn("文件不在回收站状态，无法永久删除: fileId={}", fileId);
            throw new RuntimeException("文件不在回收站中，无法永久删除");
        }

        // 删除物理文件
        try {
            String fullPath = fileConfig.getUploadPath() + fileInfo.getFilePath();
            log.info("尝试删除物理文件: {}", fullPath);
            boolean fileExists = Files.exists(Paths.get(fullPath));
            log.info("物理文件是否存在: {}", fileExists);

            Files.deleteIfExists(Paths.get(fullPath));
            log.info("物理文件删除完成: {}", fileInfo.getFilePath());
        } catch (IOException e) {
            log.error("删除物理文件失败: {}", fileInfo.getFilePath(), e);
            // 即使物理文件删除失败，也继续删除数据库记录
        }

        // 删除数据库记录
        log.info("开始删除数据库记录: fileId={}", fileId);
        int deletedRows = fileMapper.deleteById(fileId);
        log.info("数据库删除结果: 影响行数={}", deletedRows);

        if (deletedRows > 0) {
            log.info("文件永久删除成功: {}, 用户: {}", fileInfo.getName(), userId);
        } else {
            log.error("数据库记录删除失败: fileId={}, 影响行数={}", fileId, deletedRows);
            throw new RuntimeException("数据库记录删除失败");
        }

        log.info("=== 永久删除文件完成 ===");
    }
    
    /**
     * 重命名文件
     */
    @Transactional
    public void renameFile(Long fileId, String newName, Long userId) {
        FileInfo fileInfo = fileMapper.findById(fileId);
        if (fileInfo == null || !fileInfo.getUserId().equals(userId)) {
            throw new RuntimeException("文件不存在或无权限");
        }

        // 保留原文件扩展名
        String finalNewName = preserveFileExtension(fileInfo.getName(), newName);

        // 检查新名称是否已存在（排除当前文件）
        if (fileMapper.existsByNameAndFolderExcludeId(userId, fileInfo.getFolderId(), finalNewName, fileId) > 0) {
            throw new RuntimeException("文件名称已存在");
        }

        fileMapper.rename(fileId, finalNewName, LocalDateTime.now());

        log.info("文件重命名成功: {} -> {}, 用户: {}", fileInfo.getName(), finalNewName, userId);
    }
    
    /**
     * 移动文件
     */
    @Transactional
    public void moveFile(Long fileId, Long targetFolderId, Long userId) {
        FileInfo fileInfo = fileMapper.findById(fileId);
        if (fileInfo == null || !fileInfo.getUserId().equals(userId)) {
            throw new RuntimeException("文件不存在或无权限");
        }

        // 安全处理folderId - 避免空指针异常
        Long actualFolderId = (targetFolderId != null && !targetFolderId.equals(0L)) ? targetFolderId : null;
        fileMapper.moveToFolder(fileId, actualFolderId, LocalDateTime.now());

        log.info("文件移动成功: {}, 用户: {}", fileInfo.getName(), userId);
    }
    
    /**
     * 批量操作文件
     */
    @Transactional
    public void batchMoveFiles(List<Long> fileIds, Long targetFolderId, Long userId) {
        // 验证所有文件都属于当前用户
        for (Long fileId : fileIds) {
            FileInfo fileInfo = fileMapper.findById(fileId);
            if (fileInfo == null || !fileInfo.getUserId().equals(userId)) {
                throw new RuntimeException("文件不存在或无权限: " + fileId);
            }
        }

        // 更改为循环调用单个移动方法，保证稳定性
        LocalDateTime moveTime = LocalDateTime.now();
        Long actualFolderId = (targetFolderId != null && !targetFolderId.equals(0L)) ? targetFolderId : null;
        int successCount = 0;
        for (Long fileId : fileIds) {
            try {
                int result = fileMapper.moveToFolder(fileId, actualFolderId, moveTime);
                if (result > 0) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("移动文件失败: fileId={}, error={}", fileId, e.getMessage());
            }
        }
        log.info("批量移动文件完成: 成功{}/{}, 用户: {}", successCount, fileIds.size(), userId);
    }
    
    /**
     * 批量删除文件
     */
    @Transactional
    public void batchDeleteFiles(List<Long> fileIds, Long userId) {
        log.info("=== 开始批量删除文件 ===");
        log.info("用户ID: {}, 文件ID列表: {}", userId, fileIds);

        if (fileIds == null || fileIds.isEmpty()) {
            log.warn("文件ID列表为空，跳过批量删除");
            return;
        }

        long totalSize = 0;
        List<FileInfo> validFiles = new ArrayList<>();

        // 验证所有文件都属于当前用户并计算总大小
        for (Long fileId : fileIds) {
            log.info("验证文件权限: fileId={}", fileId);
            FileInfo fileInfo = fileMapper.findById(fileId);
            if (fileInfo == null) {
                log.error("文件不存在: fileId={}", fileId);
                throw new RuntimeException("文件不存在: " + fileId);
            }
            if (!fileInfo.getUserId().equals(userId)) {
                log.error("无权限删除文件: fileId={}, 文件所有者={}, 当前用户={}",
                         fileId, fileInfo.getUserId(), userId);
                throw new RuntimeException("文件不存在或无权限: " + fileId);
            }

            validFiles.add(fileInfo);
            totalSize += fileInfo.getFileSize();
            log.info("验证文件成功: ID={}, 名称={}, 大小={}, 当前删除状态={}",
                    fileId, fileInfo.getName(), fileInfo.getFileSize(), fileInfo.getIsDeleted());
        }

        // 更改为循环调用单个软删除方法，保证稳定性
        LocalDateTime deleteTime = LocalDateTime.now();
        log.info("删除时间: {}", deleteTime);
        
        int successCount = 0;
        for (Long fileId : fileIds) {
            try {
                int result = fileMapper.softDelete(fileId, deleteTime);
                if (result > 0) {
                    successCount++;
                    log.debug("文件软删除成功: ID={}", fileId);
                } else {
                    log.warn("文件软删除失败: ID={} (可能已删除或不存在)", fileId);
                }
            } catch (Exception e) {
                log.error("文件软删除异常: fileId={}, error={}", fileId, e.getMessage());
            }
        }
        
        log.info("批量软删除完成: 成功{}/{}", successCount, fileIds.size());
        
        if (successCount != fileIds.size()) {
            log.warn("批量删除部分失败: 期望影响{}(个), 实际成功{}(个)", fileIds.size(), successCount);
        }

        // 验证删除结果
        log.info("验证删除结果:");
        for (Long fileId : fileIds) {
            FileInfo deletedFile = fileMapper.findByIdIncludeDeleted(fileId);
            if (deletedFile != null) {
                log.info("删除后文件状态: ID={}, 删除状态={}, 删除时间={}",
                        fileId, deletedFile.getIsDeleted(), deletedFile.getDeletedTime());
            } else {
                log.error("删除后文件不存在: fileId={}", fileId);
            }
        }

        // 更新用户存储使用量
        log.info("更新用户存储使用量: userId={}, 减少大小={}", userId, totalSize);
        userService.decreaseStorageUsed(userId, totalSize);

        log.info("批量删除文件成功: 文件数量={}, 用户={}", fileIds.size(), userId);
        log.info("=== 批量删除文件完成 ===");
    }

    /**
     * 复制文件
     */
    @Transactional
    public FileInfo copyFile(Long fileId, Long targetFolderId, Long userId) throws IOException {
        FileInfo originalFile = fileMapper.findById(fileId);
        if (originalFile == null || !originalFile.getUserId().equals(userId)) {
            throw new RuntimeException("文件不存在或无权限");
        }

        // 检查用户存储空间
        if (!userService.canUploadFile(userId, originalFile.getFileSize())) {
            throw new RuntimeException("存储空间不足");
        }

        // 生成新的文件名（如果同名则添加副本标识）
        String newFileName = generateCopyFileName(originalFile.getName(), targetFolderId, userId);

        // 复制物理文件
        String newStoragePath = generateStoragePath(newFileName);
        String originalFullPath = fileConfig.getUploadPath() + originalFile.getFilePath();
        String newFullPath = fileConfig.getUploadPath() + newStoragePath;

        // 创建目录
        Path newPath = Paths.get(newFullPath);
        Files.createDirectories(newPath.getParent());

        // 复制文件
        Files.copy(Paths.get(originalFullPath), newPath, StandardCopyOption.REPLACE_EXISTING);

        // 创建新的文件记录
        FileInfo newFile = FileInfo.builder()
                .name(newFileName)
                .originalName(newFileName)
                .fileType(originalFile.getFileType())
                .fileSize(originalFile.getFileSize())
                .filePath(newStoragePath)
                .folderId((targetFolderId != null && !targetFolderId.equals(0L)) ? targetFolderId : null) // 安全处理folderId
                .userId(userId)
                .md5Hash(originalFile.getMd5Hash()) // 复制文件MD5相同
                .downloadCount(0)
                .isDeleted(false)
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();

        fileMapper.insert(newFile);

        // 使用专门的存储使用量服务
        storageUsageService.increaseStorageUsageAsync(userId, originalFile.getFileSize());

        log.info("文件复制成功: {} -> {}, 用户: {}", originalFile.getName(), newFileName, userId);

        return newFile;
    }

    /**
     * 批量复制文件
     */
    @Transactional
    public void batchCopyFiles(List<Long> fileIds, Long targetFolderId, Long userId) throws IOException {
        // 验证所有文件都属于当前用户并计算总大小
        long totalSize = 0;
        for (Long fileId : fileIds) {
            FileInfo fileInfo = fileMapper.findById(fileId);
            if (fileInfo == null || !fileInfo.getUserId().equals(userId)) {
                throw new RuntimeException("文件不存在或无权限: " + fileId);
            }
            totalSize += fileInfo.getFileSize();
        }

        // 检查用户存储空间
        if (!userService.canUploadFile(userId, totalSize)) {
            throw new RuntimeException("存储空间不足");
        }

        // 逐个复制文件
        for (Long fileId : fileIds) {
            copyFile(fileId, targetFolderId, userId);
        }

        log.info("批量复制文件成功: {}, 用户: {}", fileIds.size(), userId);
    }

    /**
     * 批量下载文件（打包成ZIP）
     */
    public File batchDownloadFiles(List<Long> fileIds, Long userId) throws IOException {
        // 验证所有文件都属于当前用户
        List<FileInfo> files = new ArrayList<>();
        for (Long fileId : fileIds) {
            FileInfo fileInfo = fileMapper.findById(fileId);
            if (fileInfo == null || !fileInfo.getUserId().equals(userId)) {
                throw new RuntimeException("文件不存在或无权限: " + fileId);
            }
            files.add(fileInfo);
        }

        // 创建临时ZIP文件
        String zipFileName = "batch_download_" + System.currentTimeMillis() + ".zip";
        String zipFilePath = fileConfig.getTempPath() + zipFileName;
        File zipFile = new File(zipFilePath);

        // 创建ZIP文件
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
            for (FileInfo fileInfo : files) {
                String filePath = fileConfig.getUploadPath() + fileInfo.getFilePath();
                File file = new File(filePath);

                if (file.exists()) {
                    // 添加文件到ZIP
                    ZipEntry zipEntry = new ZipEntry(fileInfo.getOriginalName());
                    zos.putNextEntry(zipEntry);

                    try (FileInputStream fis = new FileInputStream(file)) {
                        byte[] buffer = new byte[8192];
                        int length;
                        while ((length = fis.read(buffer)) > 0) {
                            zos.write(buffer, 0, length);
                        }
                    }

                    zos.closeEntry();
                }
            }
        }

        log.info("批量下载文件打包成功: {} 个文件, 用户: {}", files.size(), userId);

        return zipFile;
    }

    /**
     * 生成重复文件名（用于处理相同MD5的文件）
     */
    private String generateDuplicateFileName(String originalName, Long folderId, Long userId) {
        String baseName = originalName;
        String extension = "";

        // 分离文件名和扩展名
        int lastDotIndex = originalName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            baseName = originalName.substring(0, lastDotIndex);
            extension = originalName.substring(lastDotIndex);
        }

        // 检查是否存在同名文件，生成新的文件名
        String newName = originalName;
        int duplicateNumber = 1;

        while (fileMapper.existsByNameAndFolder(userId, folderId, newName) > 0) {
            newName = baseName + "_副本" + (duplicateNumber > 1 ? duplicateNumber : "") + extension;
            duplicateNumber++;
        }

        return newName;
    }

    /**
     * 生成复制文件名
     */
    private String generateCopyFileName(String originalName, Long folderId, Long userId) {
        String baseName = originalName;
        String extension = "";

        // 分离文件名和扩展名
        int lastDotIndex = originalName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            baseName = originalName.substring(0, lastDotIndex);
            extension = originalName.substring(lastDotIndex);
        }

        // 检查是否存在同名文件
        String newName = originalName;
        int copyNumber = 1;

        while (fileMapper.existsByNameAndFolder(userId, folderId, newName) > 0) {
            newName = baseName + "_副本" + (copyNumber > 1 ? copyNumber : "") + extension;
            copyNumber++;
        }

        return newName;
    }
    
    /**
     * 获取文件列表
     */
    public List<FileInfo> getFileList(Long userId, Long folderId) {
        if (folderId == null || folderId == 0) {
            return fileMapper.findByUserInRoot(userId);
        }
        return fileMapper.findByUserAndFolder(userId, folderId);
    }

    /**
     * 获取文件信息
     */
    public FileInfo getFileInfo(Long fileId, Long userId) {
        FileInfo fileInfo = fileMapper.findById(fileId);
        if (fileInfo == null || !fileInfo.getUserId().equals(userId)) {
            return null;
        }
        return fileInfo;
    }
    
    /**
     * 搜索文件
     */
    public List<FileInfo> searchFiles(Long userId, String keyword, String fileType, 
                                     Long minSize, Long maxSize, 
                                     LocalDateTime startTime, LocalDateTime endTime,
                                     int page, int size) {
        int offset = (page - 1) * size;
        return fileMapper.searchFiles(userId, keyword, fileType, minSize, maxSize, 
                                    startTime, endTime, offset, size);
    }
    
    /**
     * 获取回收站文件
     */
    public List<FileInfo> getDeletedFiles(Long userId, int page, int size) {
        int offset = (page - 1) * size;
        return fileMapper.findDeletedFiles(userId, offset, size);
    }

    /**
     * 获取用户最近文件
     */
    public List<FileInfo> getRecentFilesByUser(Long userId, int limit) {
        return fileMapper.findRecentByUser(userId, limit);
    }
    
    /**
     * 增加下载次数并记录统计
     */
    @Transactional
    public void increaseDownloadCount(Long fileId) {
        fileMapper.increaseDownloadCount(fileId);
    }

    /**
     * 记录文件下载（包含统计和日志）
     */
    @Transactional
    public void recordFileDownload(Long fileId, Long userId, String ipAddress, jakarta.servlet.http.HttpServletRequest request) {
        try {
            // 增加文件下载次数
            fileMapper.increaseDownloadCount(fileId);

            // 获取文件信息
            FileInfo fileInfo = fileMapper.findById(fileId);
            if (fileInfo != null) {
                // 记录下载统计
                analyticsService.recordDownload(fileId, userId, ipAddress, fileInfo.getFileSize());

                // 记录用户行为日志
                if (userId != null && request != null) {
                    loggingService.logFileDownload(userId, fileId, fileInfo.getName(), request);
                }
            }
        } catch (Exception e) {
            log.error("记录文件下载失败: fileId={}, userId={}", fileId, userId, e);
        }
    }
    
    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file, Long userId) {
        log.info("开始验证文件: {}, 大小: {}", file.getOriginalFilename(), file.getSize());

        if (file.isEmpty()) {
            log.warn("文件为空: {}", file.getOriginalFilename());
            throw new RuntimeException("文件不能为空");
        }

        // 检查文件大小限制
        if (!userService.canUploadFile(userId, file.getSize())) {
            log.warn("文件大小超限: {}, 大小: {}", file.getOriginalFilename(), file.getSize());
            throw new RuntimeException("文件大小超出限制或存储空间不足");
        }

        // 检查文件类型（这里可以添加黑名单检查）
        String filename = file.getOriginalFilename();
        if (filename == null || filename.trim().isEmpty()) {
            log.warn("文件名为空");
            throw new RuntimeException("文件名不能为空");
        }

        log.info("文件验证通过: {}", filename);
    }
    
    /**
     * 保存文件并计算MD5（只读取文件一次）
     */
    private String saveFileAndCalculateMD5(MultipartFile file, String fullPath) throws IOException {
        log.info("开始保存文件并计算MD5: {}", file.getOriginalFilename());

        try (InputStream inputStream = file.getInputStream();
             FileOutputStream outputStream = new FileOutputStream(fullPath)) {

            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[8192];
            int bytesRead;
            long totalBytesRead = 0;

            // 边读边写，同时计算MD5
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
                md5.update(buffer, 0, bytesRead);
                totalBytesRead += bytesRead;
            }

            log.info("文件保存完成: {}, 总字节数: {}", file.getOriginalFilename(), totalBytesRead);

            // 转换MD5为十六进制字符串
            byte[] digest = md5.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }

            String md5Hash = sb.toString();
            log.info("MD5计算完成: {}, MD5: {}", file.getOriginalFilename(), md5Hash);

            return md5Hash;

        } catch (Exception e) {
            log.error("保存文件并计算MD5失败: {}", file.getOriginalFilename(), e);
            throw new IOException("保存文件并计算MD5失败", e);
        }
    }

    /**
     * 计算文件MD5（保留用于其他地方使用）
     */
    private String calculateMD5(byte[] data) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(data);
            StringBuilder sb = new StringBuilder();
            for (byte b : hash) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException("计算文件MD5失败", e);
        }
    }
    
    /**
     * 生成存储路径
     */
    private String generateStoragePath(String originalFilename) {
        String date = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String extension = getFileExtension(originalFilename);
        return date + "/" + uuid + (extension.isEmpty() ? "" : "." + extension);
    }
    
    /**
     * 获取文件类型
     */
    private String getFileType(String filename) {
        String extension = getFileExtension(filename);
        if (extension.isEmpty()) {
            return "unknown";
        }
        
        // 根据扩展名判断文件类型
        if (extension.matches("(?i)(jpg|jpeg|png|gif|bmp|webp|svg)")) {
            return "image";
        } else if (extension.matches("(?i)(mp4|avi|mkv|mov|wmv|flv|webm|m4v)")) {
            return "video";
        } else if (extension.matches("(?i)(mp3|wav|flac|aac|ogg|wma|m4a)")) {
            return "audio";
        } else if (extension.matches("(?i)(pdf|doc|docx|xls|xlsx|ppt|pptx|txt|rtf)")) {
            return "document";
        } else if (extension.matches("(?i)(zip|rar|7z|tar|gz)")) {
            return "archive";
        } else {
            return "other";
        }
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
    }

    /**
     * 保留原文件扩展名（如果用户没有指定新扩展名）
     */
    private String preserveFileExtension(String originalName, String newName) {
        if (originalName == null || newName == null) {
            return newName;
        }

        // 获取原文件的扩展名
        int originalDotIndex = originalName.lastIndexOf('.');
        if (originalDotIndex <= 0) {
            // 原文件没有扩展名，直接返回新名称
            return newName;
        }

        String originalExtension = originalName.substring(originalDotIndex);

        // 检查新名称是否包含扩展名
        int newDotIndex = newName.lastIndexOf('.');
        if (newDotIndex > 0) {
            // 用户指定了扩展名，使用用户指定的扩展名
            return newName;
        } else {
            // 用户没有指定扩展名，保留原文件的扩展名
            return newName + originalExtension;
        }
    }

    /**
     * 刷新文件状态
     * 检查数据库中的文件是否在文件系统中存在，直接删除丢失文件的数据库记录
     */
    @Transactional
    public FileController.FileRefreshResult refreshFiles(Long userId, Long folderId) {
        log.info("=== 开始刷新文件状态 ===");
        log.info("用户ID: {}, 文件夹ID: {}", userId, folderId);

        FileController.FileRefreshResult result = new FileController.FileRefreshResult();

        try {
            // 获取指定文件夹下的所有文件
            List<FileInfo> dbFiles = fileMapper.findByUserAndFolder(userId, (folderId != null && !Long.valueOf(0).equals(folderId)) ? folderId : null);
            result.setTotalFiles(dbFiles.size());

            int checkedFiles = 0;
            int removedCount = 0;

            // 检查每个文件是否在文件系统中存在
            for (FileInfo fileInfo : dbFiles) {
                checkedFiles++;
                String fullPath = fileConfig.getUploadPath() + fileInfo.getFilePath();
                Path filePath = Paths.get(fullPath);

                if (!Files.exists(filePath)) {
                    // 文件不存在，直接从数据库中删除记录
                    log.warn("发现丢失文件: {} (ID: {})", fileInfo.getName(), fileInfo.getId());

                    // 直接物理删除数据库记录，不进入回收站
                    fileMapper.deleteById(fileInfo.getId());

                    // 异步减少用户存储使用量
                    storageUsageService.decreaseStorageUsageAsync(userId, fileInfo.getFileSize());

                    result.getRemovedFiles().add(fileInfo.getName());
                    removedCount++;

                    log.info("已从数据库中删除丢失文件记录: {}", fileInfo.getName());
                }
            }

            result.setCheckedFiles(checkedFiles);
            result.setRemovedCount(removedCount);
            result.setAddedCount(0); // 暂时不实现添加新文件的功能

            log.info("文件状态检查完成: 总计{}个文件，检查{}个，移除{}个丢失文件",
                    result.getTotalFiles(), result.getCheckedFiles(), result.getRemovedCount());

            return result;

        } catch (Exception e) {
            log.error("刷新文件状态失败", e);
            throw new RuntimeException("刷新文件状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据文件ID获取文件信息（管理员专用）
     */
    public FileInfo getFileById(Long fileId) {
        return fileMapper.findById(fileId);
    }
}
