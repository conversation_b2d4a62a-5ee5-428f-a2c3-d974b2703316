package com.example.v14.service;

import com.example.v14.entity.User;
import com.example.v14.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

    private final UserMapper userMapper;
    private final BCryptPasswordEncoder passwordEncoder;
    
    /**
     * 根据ID查询用户
     */
    public User findById(Long id) {
        return userMapper.findById(id);
    }
    
    /**
     * 根据用户名查询用户
     */
    public User findByUsername(String username) {
        return userMapper.findByUsername(username);
    }
    
    /**
     * 根据邮箱查询用户
     */
    public User findByEmail(String email) {
        return userMapper.findByEmail(email);
    }
    
    /**
     * 用户注册
     */
    @Transactional
    public User register(String username, String password, String email, String nickname) {
        // 检查用户名是否已存在
        if (userMapper.existsByUsername(username, 0L) > 0) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (email != null && !email.isEmpty() && userMapper.existsByEmail(email, 0L) > 0) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 创建用户
        User user = User.builder()
                .username(username)
                .password(passwordEncoder.encode(password))
                .email(email)
                .nickname(nickname != null ? nickname : username)
                .role(User.UserRole.USER)
                .status(User.UserStatus.ACTIVE)
                .storageUsed(0L)
                .storageLimit(1610612736L) // 1.5GB
                .singleFileLimit(1073741824L) // 1GB
                .createdTime(LocalDateTime.now())
                .updatedTime(LocalDateTime.now())
                .build();
        
        userMapper.insert(user);
        return user;
    }
    
    /**
     * 用户登录验证
     */
    public User login(String username, String password) {
        User user = userMapper.findByUsername(username);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        if (!user.isActive()) {
            throw new RuntimeException("用户已被禁用或锁定");
        }

        if (!passwordEncoder.matches(password, user.getPassword())) {
            throw new RuntimeException("密码错误");
        }

        return user;
    }
    
    /**
     * 更新最后登录信息
     */
    @Transactional
    public void updateLastLogin(Long userId, String ip) {
        userMapper.updateLastLogin(userId, LocalDateTime.now(), ip);
    }
    
    /**
     * 更新用户信息
     */
    @Transactional
    public void updateUser(User user) {
        user.setUpdatedTime(LocalDateTime.now());
        userMapper.update(user);
    }
    
    /**
     * 修改密码
     */
    @Transactional
    public void changePassword(Long userId, String oldPassword, String newPassword) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new RuntimeException("原密码错误");
        }
        
        String encodedPassword = passwordEncoder.encode(newPassword);
        userMapper.updatePassword(userId, encodedPassword, LocalDateTime.now());
    }
    
    /**
     * 管理员重置密码
     */
    @Transactional
    public void resetPassword(Long userId, String newPassword) {
        String encodedPassword = passwordEncoder.encode(newPassword);
        userMapper.updatePassword(userId, encodedPassword, LocalDateTime.now());
    }
    
    /**
     * 更新用户存储使用量
     */
    @Transactional
    public void updateStorageUsed(Long userId) {
        // 这里可以通过文件服务计算实际使用量
        // 暂时先用简单的更新方式
        userMapper.updateStorageUsed(userId, 0L, LocalDateTime.now());
    }

    /**
     * 更新用户存储使用量（指定大小）
     */
    @Transactional
    public void updateStorageUsed(Long userId, Long storageUsed) {
        userMapper.updateStorageUsed(userId, storageUsed, LocalDateTime.now());
    }
    
    /**
     * 增加存储使用量
     */
    @Transactional
    public void increaseStorageUsed(Long userId, Long size) {
        userMapper.increaseStorageUsed(userId, size, LocalDateTime.now());
    }

    /**
     * 增加存储使用量（新事务）
     * 使用新事务避免事务回滚问题
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void increaseStorageUsedInNewTransaction(Long userId, Long size) {
        userMapper.increaseStorageUsed(userId, size, LocalDateTime.now());
    }

    /**
     * 减少存储使用量
     */
    @Transactional
    public void decreaseStorageUsed(Long userId, Long size) {
        userMapper.decreaseStorageUsed(userId, size, LocalDateTime.now());
    }
    
    /**
     * 检查用户是否可以上传文件
     */
    public boolean canUploadFile(Long userId, Long fileSize) {
        User user = userMapper.findById(userId);
        return user != null && user.canUploadFile(fileSize);
    }
    
    /**
     * 查询所有用户（分页）
     */
    public List<User> findAll(int page, int size) {
        int offset = (page - 1) * size;
        return userMapper.findAll(offset, size);
    }
    
    /**
     * 搜索用户
     */
    public List<User> searchUsers(String username, String email, User.UserRole role, User.UserStatus status, int page, int size) {
        int offset = (page - 1) * size;
        return userMapper.searchUsers(username, email, role, status, offset, size);
    }
    
    /**
     * 统计用户总数
     */
    public long countAll() {
        return userMapper.countAll();
    }
    
    /**
     * 根据条件统计用户数
     */
    public long countByCondition(String username, String email, User.UserRole role, User.UserStatus status) {
        return userMapper.countByCondition(username, email, role, status);
    }
    
    /**
     * 删除用户
     */
    @Transactional
    public void deleteUser(Long userId) {
        userMapper.deleteById(userId);
    }


}
