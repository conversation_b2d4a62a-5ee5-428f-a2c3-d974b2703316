package com.example.v14.mapper;

import com.example.v14.entity.DownloadStats;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 下载统计Mapper接口
 */
@Mapper
public interface DownloadStatsMapper {
    
    /**
     * 根据ID查询下载统计
     */
    @Select("SELECT * FROM download_stats WHERE id = #{id}")
    DownloadStats findById(Long id);
    
    /**
     * 插入下载统计记录
     */
    @Insert("INSERT INTO download_stats (file_id, user_id, ip_address, download_time, file_size) " +
            "VALUES (#{fileId}, #{userId}, #{ipAddress}, #{downloadTime}, #{fileSize})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(DownloadStats downloadStats);
    
    /**
     * 根据文件ID查询下载统计
     */
    @Select("SELECT * FROM download_stats WHERE file_id = #{fileId} ORDER BY download_time DESC LIMIT #{offset}, #{limit}")
    List<DownloadStats> findByFileId(@Param("fileId") Long fileId, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 根据用户ID查询下载统计
     */
    @Select("SELECT * FROM download_stats WHERE user_id = #{userId} ORDER BY download_time DESC LIMIT #{offset}, #{limit}")
    List<DownloadStats> findByUserId(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 根据IP地址查询下载统计
     */
    @Select("SELECT * FROM download_stats WHERE ip_address = #{ipAddress} ORDER BY download_time DESC LIMIT #{offset}, #{limit}")
    List<DownloadStats> findByIpAddress(@Param("ipAddress") String ipAddress, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 统计文件下载次数
     */
    @Select("SELECT COUNT(*) FROM download_stats WHERE file_id = #{fileId}")
    long countByFileId(Long fileId);
    
    /**
     * 统计用户下载次数
     */
    @Select("SELECT COUNT(*) FROM download_stats WHERE user_id = #{userId}")
    long countByUserId(Long userId);
    
    /**
     * 统计IP下载次数
     */
    @Select("SELECT COUNT(*) FROM download_stats WHERE ip_address = #{ipAddress}")
    long countByIpAddress(String ipAddress);
    
    /**
     * 统计总下载次数
     */
    @Select("SELECT COUNT(*) FROM download_stats")
    long countAll();
    
    /**
     * 根据时间范围统计下载次数
     */
    @Select("SELECT COUNT(*) FROM download_stats WHERE download_time BETWEEN #{startTime} AND #{endTime}")
    long countByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计时间范围内的下载总大小
     */
    @Select("SELECT COALESCE(SUM(file_size), 0) FROM download_stats WHERE download_time BETWEEN #{startTime} AND #{endTime}")
    long sumFileSizeByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取热门下载文件（按下载次数排序）
     * 排除已删除的文件（file_id为NULL的记录）
     */
    @Select("SELECT file_id, COUNT(*) as download_count FROM download_stats " +
            "WHERE file_id IS NOT NULL GROUP BY file_id ORDER BY download_count DESC LIMIT #{limit}")
    List<Object> findPopularFiles(int limit);
    
    /**
     * 获取活跃下载用户（按下载次数排序）
     */
    @Select("SELECT user_id, COUNT(*) as download_count FROM download_stats " +
            "WHERE user_id IS NOT NULL GROUP BY user_id ORDER BY download_count DESC LIMIT #{limit}")
    List<Object> findActiveUsers(int limit);
    
    /**
     * 按日期统计下载次数
     */
    @Select("SELECT DATE(download_time) as date, COUNT(*) as count " +
            "FROM download_stats WHERE download_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY DATE(download_time) ORDER BY date")
    List<Object> countByDate(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 按小时统计下载次数
     */
    @Select("SELECT HOUR(download_time) as hour, COUNT(*) as count " +
            "FROM download_stats WHERE download_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY HOUR(download_time) ORDER BY hour")
    List<Object> countByHour(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 删除指定时间之前的统计记录
     */
    @Delete("DELETE FROM download_stats WHERE download_time < #{cutoffTime}")
    int deleteOldRecords(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    /**
     * 根据文件ID删除统计记录
     */
    @Delete("DELETE FROM download_stats WHERE file_id = #{fileId}")
    int deleteByFileId(Long fileId);
    
    /**
     * 根据用户ID删除统计记录
     */
    @Delete("DELETE FROM download_stats WHERE user_id = #{userId}")
    int deleteByUserId(Long userId);
    
    /**
     * 获取最近下载记录
     */
    @Select("SELECT * FROM download_stats ORDER BY download_time DESC LIMIT #{limit}")
    List<DownloadStats> findRecent(int limit);
    
    /**
     * 统计指定用户在时间范围内的下载次数
     */
    @Select("SELECT COUNT(*) FROM download_stats WHERE user_id = #{userId} AND download_time BETWEEN #{startTime} AND #{endTime}")
    long countByUserAndTimeRange(@Param("userId") Long userId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计指定文件在时间范围内的下载次数
     */
    @Select("SELECT COUNT(*) FROM download_stats WHERE file_id = #{fileId} AND download_time BETWEEN #{startTime} AND #{endTime}")
    long countByFileAndTimeRange(@Param("fileId") Long fileId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取下载统计详情（包含文件和用户信息）
     */
    @Select("SELECT ds.*, f.name as file_name, f.original_name, u.username " +
            "FROM download_stats ds " +
            "LEFT JOIN files f ON ds.file_id = f.id " +
            "LEFT JOIN users u ON ds.user_id = u.id " +
            "ORDER BY ds.download_time DESC LIMIT #{offset}, #{limit}")
    List<Object> findDetailedStats(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 根据时间戳范围统计下载次数
     */
    @Select("SELECT COUNT(*) FROM download_stats WHERE download_time >= FROM_UNIXTIME(#{startTime}/1000) AND download_time <= FROM_UNIXTIME(#{endTime}/1000)")
    long countByDateRange(@Param("startTime") long startTime, @Param("endTime") long endTime);
}
