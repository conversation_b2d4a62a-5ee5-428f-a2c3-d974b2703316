package com.example.v14.mapper;

import com.example.v14.entity.IpWhitelist;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * IP白名单Mapper接口 - 简化版本
 */
@Mapper
public interface IpWhitelistMapper {
    
    /**
     * 根据ID查询白名单
     */
    @Select("SELECT * FROM ip_whitelist WHERE id = #{id}")
    IpWhitelist findById(Long id);
    
    /**
     * 插入白名单记录
     */
    @Insert("INSERT INTO ip_whitelist (ip_address, ip_type, status, reason, expires_at, created_by, created_time, updated_time, hit_count, last_hit_at) " +
            "VALUES (#{ipAddress}, #{ipType}, #{status}, #{reason}, #{expiresAt}, #{createdBy}, #{createdTime}, #{updatedTime}, #{hitCount}, #{lastHitAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(IpWhitelist ipWhitelist);
    
    /**
     * 更新白名单记录
     */
    @Update("UPDATE ip_whitelist SET ip_address = #{ipAddress}, ip_type = #{ipType}, status = #{status}, " +
            "reason = #{reason}, expires_at = #{expiresAt}, updated_time = #{updatedTime}, " +
            "hit_count = #{hitCount}, last_hit_at = #{lastHitAt} WHERE id = #{id}")
    int update(IpWhitelist ipWhitelist);
    
    /**
     * 删除白名单记录
     */
    @Delete("DELETE FROM ip_whitelist WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 查询所有白名单
     */
    @Select("SELECT * FROM ip_whitelist ORDER BY created_time DESC")
    List<IpWhitelist> findAll();
    
    /**
     * 分页查询白名单
     */
    @Select("SELECT * FROM ip_whitelist ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<IpWhitelist> findWithPaging(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 查询所有启用的白名单
     */
    @Select("SELECT * FROM ip_whitelist WHERE status = 'ENABLED' " +
            "AND (expires_at IS NULL OR expires_at > NOW()) " +
            "ORDER BY created_time DESC")
    List<IpWhitelist> findAllEnabled();
    
    /**
     * 根据IP地址查询白名单
     */
    @Select("SELECT * FROM ip_whitelist WHERE ip_address = #{ipAddress}")
    IpWhitelist findByIpAddress(String ipAddress);
    
    /**
     * 根据IP地址和状态查询白名单
     */
    @Select("SELECT * FROM ip_whitelist WHERE ip_address = #{ipAddress} AND status = #{status}")
    IpWhitelist findByIpAddressAndStatus(@Param("ipAddress") String ipAddress, @Param("status") String status);
    
    /**
     * 检查IP是否在白名单中
     */
    @Select("SELECT COUNT(*) FROM ip_whitelist WHERE ip_address = #{ipAddress} " +
            "AND status = 'ENABLED' " +
            "AND (expires_at IS NULL OR expires_at > NOW())")
    int countActiveByIpAddress(String ipAddress);
    
    /**
     * 统计白名单总数
     */
    @Select("SELECT COUNT(*) FROM ip_whitelist")
    long countAll();
    
    /**
     * 统计启用的白名单数量
     */
    @Select("SELECT COUNT(*) FROM ip_whitelist WHERE status = 'ENABLED' " +
            "AND (expires_at IS NULL OR expires_at > NOW())")
    long countEnabled();
    
    /**
     * 统计已过期的白名单数量
     */
    @Select("SELECT COUNT(*) FROM ip_whitelist WHERE status = 'ENABLED' " +
            "AND expires_at IS NOT NULL AND expires_at <= NOW()")
    long countExpired();
    
    /**
     * 更新命中信息
     */
    @Update("UPDATE ip_whitelist SET hit_count = hit_count + 1, " +
            "last_hit_at = #{hitAt}, updated_time = #{updatedTime} " +
            "WHERE ip_address = #{ipAddress}")
    int updateHitInfo(@Param("ipAddress") String ipAddress, 
                     @Param("hitAt") LocalDateTime hitAt,
                     @Param("updatedTime") LocalDateTime updatedTime);
    
    /**
     * 更新状态
     */
    @Update("UPDATE ip_whitelist SET status = #{status}, updated_time = #{updatedTime} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") String status, @Param("updatedTime") LocalDateTime updatedTime);
    
    // 批量操作已移除，Service层改用循环调用保证稳定性
    
    /**
     * 删除过期的白名单记录
     */
    @Delete("DELETE FROM ip_whitelist WHERE status = 'ENABLED' " +
            "AND expires_at IS NOT NULL AND expires_at <= NOW()")
    int deleteExpired();
    
    /**
     * 根据创建者查询白名单
     */
    @Select("SELECT * FROM ip_whitelist WHERE created_by = #{createdBy} ORDER BY created_time DESC")
    List<IpWhitelist> findByCreatedBy(Long createdBy);
    
    /**
     * 根据状态查询白名单
     */
    @Select("SELECT * FROM ip_whitelist WHERE status = #{status} ORDER BY created_time DESC")
    List<IpWhitelist> findByStatus(String status);
    
    /**
     * 根据IP类型查询白名单
     */
    @Select("SELECT * FROM ip_whitelist WHERE ip_type = #{ipType} ORDER BY created_time DESC")
    List<IpWhitelist> findByIpType(String ipType);
    
    /**
     * 查询即将过期的白名单（用于提醒）
     */
    @Select("SELECT * FROM ip_whitelist WHERE status = 'ENABLED' " +
            "AND expires_at IS NOT NULL " +
            "AND expires_at BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{days} DAY) " +
            "ORDER BY expires_at ASC")
    List<IpWhitelist> findExpiringWithinDays(@Param("days") int days);
    
    /**
     * 获取命中次数最多的IP
     */
    @Select("SELECT * FROM ip_whitelist WHERE hit_count > 0 " +
            "ORDER BY hit_count DESC LIMIT #{limit}")
    List<IpWhitelist> findTopHit(@Param("limit") int limit);
}