package com.example.v14.mapper;

import com.example.v14.entity.Folder;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件夹Mapper接口
 */
@Mapper
public interface FolderMapper {
    
    /**
     * 根据ID查询文件夹（仅查询未删除的文件夹）
     */
    @Select("SELECT * FROM folders WHERE id = #{id} AND is_deleted = false")
    Folder findById(Long id);

    /**
     * 根据ID查询文件夹（包括已删除的文件夹）
     */
    @Select("SELECT * FROM folders WHERE id = #{id}")
    Folder findByIdIncludeDeleted(Long id);
    
    /**
     * 插入文件夹
     */
    @Insert("INSERT INTO folders (name, parent_id, user_id, path, created_time, updated_time, is_deleted) " +
            "VALUES (#{name}, #{parentId}, #{userId}, #{path}, #{createdTime}, #{updatedTime}, #{isDeleted})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Folder folder);
    
    /**
     * 更新文件夹
     */
    @Update("UPDATE folders SET name = #{name}, path = #{path}, updated_time = #{updatedTime} WHERE id = #{id}")
    int update(Folder folder);
    
    /**
     * 软删除文件夹
     */
    @Update("UPDATE folders SET is_deleted = true, deleted_time = #{deletedTime} WHERE id = #{id}")
    int softDelete(@Param("id") Long id, @Param("deletedTime") LocalDateTime deletedTime);

    /**
     * 恢复删除的文件夹
     */
    @Update("UPDATE folders SET is_deleted = false, deleted_time = NULL WHERE id = #{id}")
    int restore(Long id);

    /**
     * 物理删除文件夹
     */
    @Delete("DELETE FROM folders WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 根据用户ID查询根目录文件夹（仅未删除）
     */
    @Select("SELECT * FROM folders WHERE user_id = #{userId} AND is_deleted = false " +
            "AND parent_id IS NULL ORDER BY created_time DESC")
    List<Folder> findRootFoldersByUser(@Param("userId") Long userId);
    
    /**
     * 根据用户ID和父文件夹ID查询子文件夹（仅未删除）
     */
    @Select("SELECT * FROM folders WHERE user_id = #{userId} AND is_deleted = false " +
            "AND parent_id = #{parentId} ORDER BY created_time DESC")
    List<Folder> findByUserAndParent(@Param("userId") Long userId, @Param("parentId") Long parentId);
    
    /**
     * 根据用户ID查询所有文件夹（仅未删除）
     */
    @Select("SELECT * FROM folders WHERE user_id = #{userId} AND is_deleted = false ORDER BY path")
    List<Folder> findByUser(Long userId);

    /**
     * 查询回收站文件夹
     */
    @Select("SELECT * FROM folders WHERE user_id = #{userId} AND is_deleted = true ORDER BY deleted_time DESC LIMIT #{offset}, #{limit}")
    List<Folder> findDeletedFolders(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 统计回收站文件夹数量
     */
    @Select("SELECT COUNT(*) FROM folders WHERE user_id = #{userId} AND is_deleted = true")
    long countDeletedFolders(Long userId);
    
    /**
     * 检查文件夹名称是否存在（排除已删除的文件夹）
     */
    /**
     * 检查根目录中文件夹名称是否存在（排除已删除的文件夹）
     */
    @Select("SELECT COUNT(*) FROM folders WHERE user_id = #{userId} AND is_deleted = false " +
            "AND parent_id IS NULL AND name = #{name} AND id != #{excludeId}" )
    int existsByNameInRoot(@Param("userId") Long userId, @Param("name") String name, @Param("excludeId") Long excludeId);
    
    /**
     * 检查指定文件夹中子文件夹名称是否存在（排除已删除的文件夹）
     */
    @Select("SELECT COUNT(*) FROM folders WHERE user_id = #{userId} AND is_deleted = false " +
            "AND parent_id = #{parentId} AND name = #{name} AND id != #{excludeId}")
    int existsByName(@Param("userId") Long userId, @Param("parentId") Long parentId, @Param("name") String name, @Param("excludeId") Long excludeId);
    
    /**
     * 根据路径查询文件夹
     */
    @Select("SELECT * FROM folders WHERE user_id = #{userId} AND path = #{path}")
    Folder findByUserAndPath(@Param("userId") Long userId, @Param("path") String path);
    
    /**
     * 查询子文件夹（递归）
     */
    @Select("SELECT * FROM folders WHERE user_id = #{userId} AND path LIKE CONCAT(#{pathPrefix}, '%')")
    List<Folder> findSubFolders(@Param("userId") Long userId, @Param("pathPrefix") String pathPrefix);
    
    /**
     * 移动文件夹
     */
    @Update("UPDATE folders SET parent_id = #{parentId}, path = #{path}, updated_time = #{updatedTime} WHERE id = #{id}")
    int move(@Param("id") Long id, @Param("parentId") Long parentId, @Param("path") String path, @Param("updatedTime") LocalDateTime updatedTime);
    
    // 批量更新路径功能由Service层处理，逐个调用update方法

    /**
     * 单个更新路径（作为批量更新的替代方案）
     */
    @Update("UPDATE folders SET path = #{path}, updated_time = #{updatedTime} WHERE id = #{id}")
    int updatePath(@Param("id") Long id, @Param("path") String path, @Param("updatedTime") LocalDateTime updatedTime);
    
    /**
     * 统计文件夹数量（仅未删除）
     */
    @Select("SELECT COUNT(*) FROM folders WHERE user_id = #{userId} AND is_deleted = false")
    long countByUser(Long userId);

    /**
     * 统计子文件夹数量（仅未删除）
     */
    @Select("SELECT COUNT(*) FROM folders WHERE parent_id = #{parentId} AND is_deleted = false")
    long countByParent(Long parentId);

    /**
     * 查询文件夹的所有子文件夹（包括递归，仅未删除）
     */
    @Select("SELECT * FROM folders WHERE user_id = #{userId} AND is_deleted = false AND path LIKE CONCAT(#{pathPrefix}, '%')")
    List<Folder> findSubFoldersRecursive(@Param("userId") Long userId, @Param("pathPrefix") String pathPrefix);

    // 批量软删除文件夹 - 已移除，Service层改用循环调用softDelete方法保证稳定性

    /**
     * 查询指定父文件夹下的已删除子文件夹
     */
    @Select("SELECT * FROM folders WHERE user_id = #{userId} AND parent_id = #{parentId} AND is_deleted = true")
    List<Folder> findDeletedSubFolders(@Param("userId") Long userId, @Param("parentId") Long parentId);
}
