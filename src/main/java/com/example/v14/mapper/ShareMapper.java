package com.example.v14.mapper;

import com.example.v14.entity.Share;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 分享Mapper接口
 */
@Mapper
public interface ShareMapper {
    
    /**
     * 根据ID查询分享
     */
    @Select("SELECT * FROM shares WHERE id = #{id}")
    Share findById(Long id);
    
    /**
     * 根据分享码查询分享
     */
    @Select("SELECT * FROM shares WHERE share_code = #{shareCode}")
    Share findByShareCode(String shareCode);
    
    /**
     * 插入分享
     */
    @Insert("INSERT INTO shares (share_code, file_id, folder_id, user_id, share_type, password, expire_time, download_limit, download_count, is_active, created_time) " +
            "VALUES (#{shareCode}, #{fileId}, #{folderId}, #{userId}, #{shareType}, #{password}, #{expireTime}, #{downloadLimit}, #{downloadCount}, #{isActive}, #{createdTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Share share);
    
    /**
     * 更新分享
     */
    @Update("UPDATE shares SET password = #{password}, expire_time = #{expireTime}, download_limit = #{downloadLimit} WHERE id = #{id}")
    int update(Share share);

    /**
     * 更新分享状态
     */
    @Update("UPDATE shares SET is_active = #{isActive} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("isActive") Boolean isActive);
    
    /**
     * 增加下载次数
     */
    @Update("UPDATE shares SET download_count = download_count + 1 WHERE id = #{id}")
    int increaseDownloadCount(Long id);
    
    /**
     * 根据用户ID查询分享列表
     */
    @Select("SELECT * FROM shares WHERE user_id = #{userId} ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<Share> findByUser(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 统计用户分享总数
     */
    @Select("SELECT COUNT(*) FROM shares WHERE user_id = #{userId}")
    long countByUser(Long userId);
    
    /**
     * 统计用户活跃分享数
     */
    @Select("SELECT COUNT(*) FROM shares WHERE user_id = #{userId} AND is_active = true AND (expire_time IS NULL OR expire_time > NOW())")
    long countActiveByUser(Long userId);
    
    /**
     * 统计用户分享总下载次数
     */
    @Select("SELECT COALESCE(SUM(download_count), 0) FROM shares WHERE user_id = #{userId}")
    long sumDownloadsByUser(Long userId);
    
    /**
     * 停用过期分享
     */
    @Update("UPDATE shares SET is_active = false WHERE expire_time IS NOT NULL AND expire_time <= #{now} AND is_active = true")
    int deactivateExpiredShares(@Param("now") LocalDateTime now);
    
    /**
     * 根据文件ID查询分享
     */
    @Select("SELECT * FROM shares WHERE file_id = #{fileId} AND is_active = true ORDER BY created_time DESC")
    List<Share> findByFileId(Long fileId);
    
    /**
     * 根据文件夹ID查询分享
     */
    @Select("SELECT * FROM shares WHERE folder_id = #{folderId} AND is_active = true ORDER BY created_time DESC")
    List<Share> findByFolderId(Long folderId);
    
    /**
     * 删除分享
     */
    @Delete("DELETE FROM shares WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 统计所有分享数量
     */
    @Select("SELECT COUNT(*) FROM shares")
    long countAll();

    /**
     * 统计活跃分享数量
     */
    @Select("SELECT COUNT(*) FROM shares WHERE expire_time > NOW() OR expire_time IS NULL")
    long countActive();

    // 批量更新分享状态 - 已移除，Service层改用循环调用updateStatus方法保证稳定性

    // 批量删除分享 - 已移除，Service层改用循环调用deleteById方法保证稳定性

    // 根据ID列表查询分享（用于权限验证）- 已移除，Service层改用循环调用findById方法保证稳定性
    
    /**
     * 获取分享次数最多的文件（排行榜用）
     */
    @Select("SELECT f.name as fileName, f.file_type as fileType, f.file_size as fileSize, " +
            "COUNT(s.id) as shareCount " +
            "FROM shares s " +
            "LEFT JOIN files f ON s.file_id = f.id " +
            "WHERE f.is_deleted = false " +
            "GROUP BY s.file_id, f.name, f.file_type, f.file_size " +
            "ORDER BY shareCount DESC " +
            "LIMIT #{limit}")
    List<java.util.Map<String, Object>> findTopSharedFiles(int limit);
}
