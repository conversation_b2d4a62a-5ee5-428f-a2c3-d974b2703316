package com.example.v14.mapper;

import com.example.v14.entity.FileInfo;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件Mapper接口
 */
@Mapper
public interface FileMapper {
    
    /**
     * 根据ID查询文件（仅查询未删除的文件）
     */
    @Select("SELECT * FROM files WHERE id = #{id} AND is_deleted = false")
    FileInfo findById(Long id);

    /**
     * 根据ID查询文件（包括已删除的文件）
     */
    @Select("SELECT * FROM files WHERE id = #{id}")
    FileInfo findByIdIncludeDeleted(Long id);
    
    /**
     * 根据MD5查询文件
     */
    @Select("SELECT * FROM files WHERE md5_hash = #{md5Hash} AND user_id = #{userId} AND is_deleted = false")
    FileInfo findByMd5AndUser(@Param("md5Hash") String md5Hash, @Param("userId") Long userId);

    /**
     * 根据MD5和用户查询文件列表
     */
    @Select("SELECT * FROM files WHERE md5_hash = #{md5Hash} AND user_id = #{userId} AND is_deleted = false")
    List<FileInfo> findByMd5AndUserList(@Param("md5Hash") String md5Hash, @Param("userId") Long userId);
    
    /**
     * 插入文件
     */
    @Insert("INSERT INTO files (name, original_name, file_type, file_size, file_path, folder_id, user_id, md5_hash, download_count, is_deleted, created_time, updated_time) " +
            "VALUES (#{name}, #{originalName}, #{fileType}, #{fileSize}, #{filePath}, #{folderId}, #{userId}, #{md5Hash}, #{downloadCount}, #{isDeleted}, #{createdTime}, #{updatedTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(FileInfo fileInfo);
    
    /**
     * 更新文件信息
     */
    @Update("UPDATE files SET name = #{name}, folder_id = #{folderId}, updated_time = #{updatedTime} WHERE id = #{id}")
    int update(FileInfo fileInfo);
    
    /**
     * 根据用户ID查询文件列表
     */
    @Select("SELECT * FROM files WHERE user_id = #{userId} AND is_deleted = false ORDER BY created_time DESC")
    List<FileInfo> selectByUserId(Long userId);
    
    /**
     * 分页查询用户文件
     */
    @Select("SELECT f.*, u.username FROM files f LEFT JOIN users u ON f.user_id = u.id " +
            "WHERE f.user_id = #{userId} AND f.is_deleted = false " +
            "ORDER BY f.created_time DESC LIMIT #{offset}, #{limit}")
    List<FileInfo> selectUserFilesWithPaging(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 分页查询用户文件（带文件名过滤）
     */
    @Select("SELECT f.*, u.username FROM files f LEFT JOIN users u ON f.user_id = u.id " +
            "WHERE f.user_id = #{userId} AND f.is_deleted = false " +
            "AND f.name LIKE CONCAT('%', #{fileName}, '%') " +
            "ORDER BY f.created_time DESC LIMIT #{offset}, #{limit}")
    List<FileInfo> selectUserFilesWithPagingAndName(@Param("userId") Long userId, @Param("fileName") String fileName, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 统计用户文件数量
     */
    @Select("SELECT COUNT(*) FROM files WHERE user_id = #{userId} AND is_deleted = false")
    long countUserFiles(@Param("userId") Long userId);
    
    /**
     * 统计用户文件数量（带文件名过滤）
     */
    @Select("SELECT COUNT(*) FROM files WHERE user_id = #{userId} AND is_deleted = false " +
            "AND name LIKE CONCAT('%', #{fileName}, '%')")
    long countUserFilesWithName(@Param("userId") Long userId, @Param("fileName") String fileName);
    
    /**
     * 根据ID查询文件（兼容方法）
     */
    @Select("SELECT * FROM files WHERE id = #{id}")
    FileInfo selectById(Long id);
    
    /**
     * 根据ID删除文件
     */
    @Delete("DELETE FROM files WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 移动文件到文件夹
     */
    @Update("UPDATE files SET folder_id = #{folderId}, updated_time = #{updatedTime} WHERE id = #{id}")
    int moveToFolder(@Param("id") Long id, @Param("folderId") Long folderId, @Param("updatedTime") LocalDateTime updatedTime);
    
    /**
     * 重命名文件
     */
    @Update("UPDATE files SET name = #{name}, updated_time = #{updatedTime} WHERE id = #{id}")
    int rename(@Param("id") Long id, @Param("name") String name, @Param("updatedTime") LocalDateTime updatedTime);
    
    /**
     * 软删除文件
     */
    @Update("UPDATE files SET is_deleted = true, deleted_time = #{deletedTime} WHERE id = #{id}")
    int softDelete(@Param("id") Long id, @Param("deletedTime") LocalDateTime deletedTime);
    
    /**
     * 恢复删除的文件
     */
    @Update("UPDATE files SET is_deleted = false, deleted_time = NULL WHERE id = #{id}")
    int restore(Long id);
    
    // deleteById方法重复定义已删除，使用第91行的定义
    
    /**
     * 增加下载次数
     */
    @Update("UPDATE files SET download_count = download_count + 1 WHERE id = #{id}")
    int increaseDownloadCount(Long id);
    
    /**
     * 根据用户ID查询根目录文件列表
     */
    @Select("SELECT * FROM files WHERE user_id = #{userId} AND is_deleted = false " +
            "AND folder_id IS NULL ORDER BY created_time DESC")
    List<FileInfo> findByUserInRoot(@Param("userId") Long userId);
    
    /**
     * 根据用户ID和文件夹ID查询文件列表
     */
    @Select("SELECT * FROM files WHERE user_id = #{userId} AND is_deleted = false " +
            "AND folder_id = #{folderId} ORDER BY created_time DESC")
    List<FileInfo> findByUserAndFolder(@Param("userId") Long userId, @Param("folderId") Long folderId);


    
    /**
     * 根据用户ID查询所有文件
     */
    @Select("SELECT * FROM files WHERE user_id = #{userId} AND is_deleted = false ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<FileInfo> findByUser(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 搜索文件 - 基础版本
     */
    @Select("SELECT * FROM files WHERE user_id = #{userId} AND is_deleted = false " +
            "AND (#{keyword} IS NULL OR #{keyword} = '' OR name LIKE CONCAT('%', #{keyword}, '%') OR original_name LIKE CONCAT('%', #{keyword}, '%')) " +
            "AND (#{fileType} IS NULL OR #{fileType} = '' OR file_type = #{fileType}) " +
            "AND (#{minSize} IS NULL OR file_size >= #{minSize}) " +
            "AND (#{maxSize} IS NULL OR file_size <= #{maxSize}) " +
            "AND (#{startTime} IS NULL OR created_time >= #{startTime}) " +
            "AND (#{endTime} IS NULL OR created_time <= #{endTime}) " +
            "ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<FileInfo> searchFiles(@Param("userId") Long userId,
                              @Param("keyword") String keyword,
                              @Param("fileType") String fileType,
                              @Param("minSize") Long minSize,
                              @Param("maxSize") Long maxSize,
                              @Param("startTime") LocalDateTime startTime,
                              @Param("endTime") LocalDateTime endTime,
                              @Param("offset") int offset,
                              @Param("limit") int limit);
    
    /**
     * 统计用户文件数量
     */
    @Select("SELECT COUNT(*) FROM files WHERE user_id = #{userId} AND is_deleted = false")
    long countByUser(Long userId);
    
    /**
     * 统计用户存储使用量
     */
    @Select("SELECT COALESCE(SUM(file_size), 0) FROM files WHERE user_id = #{userId} AND is_deleted = false")
    long sumFileSizeByUser(Long userId);
    
    /**
     * 查询回收站文件
     */
    @Select("SELECT * FROM files WHERE user_id = #{userId} AND is_deleted = true ORDER BY deleted_time DESC LIMIT #{offset}, #{limit}")
    List<FileInfo> findDeletedFiles(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 统计回收站文件数量
     */
    @Select("SELECT COUNT(*) FROM files WHERE user_id = #{userId} AND is_deleted = true")
    long countDeletedFiles(Long userId);

    /**
     * 查询指定文件夹中的已删除文件
     */
    @Select("SELECT * FROM files WHERE user_id = #{userId} AND folder_id = #{folderId} AND is_deleted = true")
    List<FileInfo> findDeletedFilesByFolder(@Param("userId") Long userId, @Param("folderId") Long folderId);
    
    /**
     * 根据根目录查询文件数量
     */
    @Select("SELECT COUNT(*) FROM files WHERE folder_id IS NULL AND is_deleted = false")
    long countByRootFolder();
    
    /**
     * 根据文件夹ID查询文件数量
     */
    @Select("SELECT COUNT(*) FROM files WHERE folder_id = #{folderId} AND is_deleted = false")
    long countByFolder(@Param("folderId") Long folderId);
    
    // 批量移动文件到指定文件夹 - 已移除，Service层改用循环调用moveToFolder方法保证稳定性
    
    // 批量软删除文件 - 已移除，Service层改用循环调用softDelete方法保证稳定性

    /**
     * 统计所有文件数量
     */
    @Select("SELECT COUNT(*) FROM files WHERE is_deleted = false")
    long countAll();

    /**
     * 统计所有文件大小
     */
    @Select("SELECT COALESCE(SUM(file_size), 0) FROM files WHERE is_deleted = false")
    long sumFileSize();

    /**
     * 统计已删除文件数量
     */
    @Select("SELECT COUNT(*) FROM files WHERE is_deleted = true")
    long countDeleted();

    /**
     * 根据日期范围统计文件数量
     */
    @Select("SELECT COUNT(*) FROM files WHERE created_time BETWEEN #{startTime} AND #{endTime} AND is_deleted = false")
    long countByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据文件类型统计数量
     */
    @Select("SELECT COUNT(*) FROM files WHERE file_type LIKE CONCAT(#{type}, '%') AND is_deleted = false")
    long countByType(String type);

    /**
     * 根据文件大小范围统计数量
     */
    @Select("SELECT COUNT(*) FROM files WHERE file_size BETWEEN #{minSize} AND #{maxSize} AND is_deleted = false")
    long countBySizeRange(@Param("minSize") Long minSize, @Param("maxSize") Long maxSize);

    /**
     * 获取最近上传的文件
     */
    @Select("SELECT * FROM files WHERE is_deleted = false ORDER BY created_time DESC LIMIT #{limit}")
    List<FileInfo> findRecent(int limit);

    /**
     * 获取用户最近上传的文件
     */
    @Select("SELECT * FROM files WHERE user_id = #{userId} AND is_deleted = false ORDER BY created_time DESC LIMIT #{limit}")
    List<FileInfo> findRecentByUser(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 根据用户ID查询所有文件
     */
    @Select("SELECT * FROM files WHERE user_id = #{userId} AND is_deleted = false")
    List<FileInfo> findAllByUser(Long userId);

    /**
     * 查询指定时间之前删除的文件
     */
    @Select("SELECT * FROM files WHERE is_deleted = true AND deleted_time < #{cutoffTime}")
    List<FileInfo> findDeletedBefore(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 检查指定文件夹中是否存在同名文件
     */
    @Select("SELECT COUNT(*) FROM files WHERE user_id = #{userId} AND folder_id = #{folderId} AND name = #{fileName} AND is_deleted = false")
    int existsByNameAndFolder(@Param("userId") Long userId, @Param("folderId") Long folderId, @Param("fileName") String fileName);

    /**
     * 根据文件名和根目录查询文件
     */
    @Select("SELECT * FROM files WHERE user_id = #{userId} AND name = #{fileName} AND is_deleted = false " +
            "AND folder_id IS NULL LIMIT 1")
    FileInfo findByNameInRootAndUser(@Param("fileName") String fileName, @Param("userId") Long userId);
    
    /**
     * 根据文件名、文件夹和用户查询文件
     */
    @Select("SELECT * FROM files WHERE user_id = #{userId} AND name = #{fileName} AND is_deleted = false " +
            "AND folder_id = #{folderId} LIMIT 1")
    FileInfo findByNameAndFolderAndUser(@Param("fileName") String fileName, @Param("folderId") Long folderId, @Param("userId") Long userId);

    /**
     * 检查根目录中是否存在同名文件（排除指定文件ID）
     */
    @Select("SELECT COUNT(*) FROM files WHERE user_id = #{userId} AND name = #{fileName} AND is_deleted = false " +
            "AND id != #{excludeId} AND folder_id IS NULL")
    int existsByNameInRootExcludeId(@Param("userId") Long userId, @Param("fileName") String fileName, @Param("excludeId") Long excludeId);
    
    /**
     * 检查指定文件夹中是否存在同名文件（排除指定文件ID）
     */
    @Select("SELECT COUNT(*) FROM files WHERE user_id = #{userId} AND name = #{fileName} AND is_deleted = false " +
            "AND id != #{excludeId} AND folder_id = #{folderId}")
    int existsByNameAndFolderExcludeId(@Param("userId") Long userId, @Param("folderId") Long folderId,
                                      @Param("fileName") String fileName, @Param("excludeId") Long excludeId);
    
    /**
     * 根据下载次数获取热门文件
     */
    @Select("SELECT * FROM files WHERE is_deleted = false AND download_count > 0 ORDER BY download_count DESC LIMIT #{limit}")
    List<FileInfo> findTopByDownloads(int limit);
}
