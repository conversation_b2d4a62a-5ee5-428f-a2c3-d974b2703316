package com.example.v14.mapper;

import com.example.v14.entity.UserLog;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户行为日志Mapper接口
 */
@Mapper
public interface UserLogMapper {
    
    /**
     * 根据ID查询用户日志
     */
    @Select("SELECT * FROM user_logs WHERE id = #{id}")
    UserLog findById(Long id);
    
    /**
     * 插入用户日志
     */
    @Insert("INSERT INTO user_logs (user_id, action, resource_type, resource_id, ip_address, user_agent, details, created_time) " +
            "VALUES (#{userId}, #{action}, #{resourceType}, #{resourceId}, #{ipAddress}, #{userAgent}, #{details}, #{createdTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(UserLog userLog);
    
    /**
     * 根据用户ID查询日志
     */
    @Select("SELECT * FROM user_logs WHERE user_id = #{userId} ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<UserLog> findByUserId(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 根据操作类型查询日志
     */
    @Select("SELECT * FROM user_logs WHERE action = #{action} ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<UserLog> findByAction(@Param("action") String action, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 根据IP地址查询日志
     */
    @Select("SELECT * FROM user_logs WHERE ip_address = #{ipAddress} ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<UserLog> findByIpAddress(@Param("ipAddress") String ipAddress, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 根据资源类型和ID查询日志
     */
    @Select("SELECT * FROM user_logs WHERE resource_type = #{resourceType} AND resource_id = #{resourceId} ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<UserLog> findByResource(@Param("resourceType") String resourceType, @Param("resourceId") Long resourceId, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 根据时间范围查询日志
     */
    @Select("SELECT * FROM user_logs WHERE created_time BETWEEN #{startTime} AND #{endTime} ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<UserLog> findByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 复合条件查询日志 - 简化版本
     */
    @Select("SELECT * FROM user_logs WHERE 1=1 " +
            "AND (#{userId} IS NULL OR user_id = #{userId}) " +
            "AND (#{action} IS NULL OR #{action} = '' OR action = #{action}) " +
            "AND (#{ipAddress} IS NULL OR #{ipAddress} = '' OR ip_address = #{ipAddress}) " +
            "AND (#{startTime} IS NULL OR created_time >= #{startTime}) " +
            "AND (#{endTime} IS NULL OR created_time <= #{endTime}) " +
            "ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<UserLog> findByConditions(@Param("userId") Long userId,
                                  @Param("action") String action,
                                  @Param("ipAddress") String ipAddress,
                                  @Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime,
                                  @Param("offset") int offset,
                                  @Param("limit") int limit);
    
    /**
     * 统计用户日志总数
     */
    @Select("SELECT COUNT(*) FROM user_logs WHERE user_id = #{userId}")
    long countByUserId(Long userId);
    
    /**
     * 统计操作类型日志数
     */
    @Select("SELECT COUNT(*) FROM user_logs WHERE action = #{action}")
    long countByAction(String action);
    
    /**
     * 统计IP地址日志数
     */
    @Select("SELECT COUNT(*) FROM user_logs WHERE ip_address = #{ipAddress}")
    long countByIpAddress(String ipAddress);
    
    /**
     * 统计时间范围内日志数
     */
    @Select("SELECT COUNT(*) FROM user_logs WHERE created_time BETWEEN #{startTime} AND #{endTime}")
    long countByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计总日志数
     */
    @Select("SELECT COUNT(*) FROM user_logs")
    long countAll();
    
    /**
     * 按操作类型统计数量
     */
    @Select("SELECT action, COUNT(*) as count FROM user_logs GROUP BY action ORDER BY count DESC")
    List<Object> countByActionGroup();
    
    /**
     * 按日期统计日志数量
     */
    @Select("SELECT DATE(created_time) as date, COUNT(*) as count " +
            "FROM user_logs WHERE created_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY DATE(created_time) ORDER BY date")
    List<Object> countByDate(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 按小时统计日志数量
     */
    @Select("SELECT HOUR(created_time) as hour, COUNT(*) as count " +
            "FROM user_logs WHERE created_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY HOUR(created_time) ORDER BY hour")
    List<Object> countByHour(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取最活跃用户（按操作次数排序）
     */
    @Select("SELECT user_id, COUNT(*) as action_count FROM user_logs " +
            "WHERE user_id IS NOT NULL AND created_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY user_id ORDER BY action_count DESC LIMIT #{limit}")
    List<Object> findMostActiveUsers(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("limit") int limit);
    
    /**
     * 获取最频繁IP（按访问次数排序）
     */
    @Select("SELECT ip_address, COUNT(*) as access_count FROM user_logs " +
            "WHERE ip_address IS NOT NULL AND created_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY ip_address ORDER BY access_count DESC LIMIT #{limit}")
    List<Object> findMostFrequentIps(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("limit") int limit);
    
    /**
     * 删除指定时间之前的日志
     */
    @Delete("DELETE FROM user_logs WHERE created_time < #{cutoffTime}")
    int deleteOldLogs(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    /**
     * 根据用户ID删除日志
     */
    @Delete("DELETE FROM user_logs WHERE user_id = #{userId}")
    int deleteByUserId(Long userId);
    
    /**
     * 获取最近日志记录
     */
    @Select("SELECT * FROM user_logs ORDER BY created_time DESC LIMIT #{limit}")
    List<UserLog> findRecent(int limit);
    
    /**
     * 获取用户最近操作记录
     */
    @Select("SELECT * FROM user_logs WHERE user_id = #{userId} ORDER BY created_time DESC LIMIT #{limit}")
    List<UserLog> findRecentByUser(@Param("userId") Long userId, @Param("limit") int limit);
    
    /**
     * 统计用户在时间范围内的操作次数
     */
    @Select("SELECT COUNT(*) FROM user_logs WHERE user_id = #{userId} AND created_time BETWEEN #{startTime} AND #{endTime}")
    long countByUserAndTimeRange(@Param("userId") Long userId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取详细日志信息（包含用户信息）
     */
    @Select("SELECT ul.*, u.username, u.nickname " +
            "FROM user_logs ul " +
            "LEFT JOIN users u ON ul.user_id = u.id " +
            "ORDER BY ul.created_time DESC LIMIT #{offset}, #{limit}")
    List<Object> findDetailedLogs(@Param("offset") int offset, @Param("limit") int limit);
}
