package com.example.v14.mapper;

import com.example.v14.entity.User;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户Mapper接口
 */
@Mapper
public interface UserMapper {
    
    /**
     * 根据ID查询用户
     */
    @Select("SELECT * FROM users WHERE id = #{id}")
    User findById(Long id);
    
    /**
     * 根据用户名查询用户
     */
    @Select("SELECT * FROM users WHERE username = #{username}")
    User findByUsername(String username);
    
    /**
     * 根据邮箱查询用户
     */
    @Select("SELECT * FROM users WHERE email = #{email}")
    User findByEmail(String email);
    
    /**
     * 插入用户
     */
    @Insert("INSERT INTO users (username, password, email, nickname, role, status, storage_used, storage_limit, single_file_limit, created_time, updated_time) " +
            "VALUES (#{username}, #{password}, #{email}, #{nickname}, #{role}, #{status}, #{storageUsed}, #{storageLimit}, #{singleFileLimit}, #{createdTime}, #{updatedTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(User user);
    
    /**
     * 更新用户信息
     */
    @Update("UPDATE users SET username = #{username}, email = #{email}, nickname = #{nickname}, " +
            "avatar = #{avatar}, status = #{status}, storage_used = #{storageUsed}, " +
            "storage_limit = #{storageLimit}, single_file_limit = #{singleFileLimit}, " +
            "updated_time = #{updatedTime} WHERE id = #{id}")
    int update(User user);
    
    /**
     * 更新密码
     */
    @Update("UPDATE users SET password = #{password}, updated_time = #{updatedTime} WHERE id = #{id}")
    int updatePassword(@Param("id") Long id, @Param("password") String password, @Param("updatedTime") LocalDateTime updatedTime);
    
    /**
     * 更新最后登录信息
     */
    @Update("UPDATE users SET last_login_time = #{lastLoginTime}, last_login_ip = #{lastLoginIp} WHERE id = #{id}")
    int updateLastLogin(@Param("id") Long id, @Param("lastLoginTime") LocalDateTime lastLoginTime, @Param("lastLoginIp") String lastLoginIp);
    
    /**
     * 更新用户存储使用量
     */
    @Update("UPDATE users SET storage_used = #{storageUsed}, updated_time = #{updatedTime} WHERE id = #{id}")
    int updateStorageUsed(@Param("id") Long id, @Param("storageUsed") Long storageUsed, @Param("updatedTime") LocalDateTime updatedTime);
    
    /**
     * 增加用户存储使用量
     */
    @Update("UPDATE users SET storage_used = storage_used + #{size}, updated_time = #{updatedTime} WHERE id = #{id}")
    int increaseStorageUsed(@Param("id") Long id, @Param("size") Long size, @Param("updatedTime") LocalDateTime updatedTime);
    
    /**
     * 减少用户存储使用量
     */
    @Update("UPDATE users SET storage_used = GREATEST(0, storage_used - #{size}), updated_time = #{updatedTime} WHERE id = #{id}")
    int decreaseStorageUsed(@Param("id") Long id, @Param("size") Long size, @Param("updatedTime") LocalDateTime updatedTime);
    
    /**
     * 删除用户
     */
    @Delete("DELETE FROM users WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 查询所有用户（分页）
     */
    @Select("SELECT * FROM users ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<User> findAll(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 根据条件搜索用户 - 简化版本
     */
    @Select("SELECT * FROM users WHERE 1=1 " +
            "AND (#{username} IS NULL OR #{username} = '' OR username LIKE CONCAT('%', #{username}, '%')) " +
            "AND (#{email} IS NULL OR #{email} = '' OR email LIKE CONCAT('%', #{email}, '%')) " +
            "AND (#{role} IS NULL OR role = #{role}) " +
            "AND (#{status} IS NULL OR status = #{status}) " +
            "ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<User> searchUsers(@Param("username") String username, 
                          @Param("email") String email,
                          @Param("role") User.UserRole role,
                          @Param("status") User.UserStatus status,
                          @Param("offset") int offset, 
                          @Param("limit") int limit);
    
    /**
     * 统计用户总数
     */
    @Select("SELECT COUNT(*) FROM users")
    long countAll();
    
    /**
     * 根据条件统计用户数 - 简化版本
     */
    @Select("SELECT COUNT(*) FROM users WHERE 1=1 " +
            "AND (#{username} IS NULL OR #{username} = '' OR username LIKE CONCAT('%', #{username}, '%')) " +
            "AND (#{email} IS NULL OR #{email} = '' OR email LIKE CONCAT('%', #{email}, '%')) " +
            "AND (#{role} IS NULL OR role = #{role}) " +
            "AND (#{status} IS NULL OR status = #{status})")
    long countByCondition(@Param("username") String username,
                         @Param("email") String email,
                         @Param("role") User.UserRole role,
                         @Param("status") User.UserStatus status);

    /**
     * 根据状态统计用户数
     */
    @Select("SELECT COUNT(*) FROM users WHERE status = #{status}")
    long countByStatus(User.UserStatus status);

    /**
     * 根据日期范围统计用户数
     */
    @Select("SELECT COUNT(*) FROM users WHERE created_time BETWEEN #{startTime} AND #{endTime}")
    long countByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 获取存储使用排行
     */
    @Select("SELECT * FROM users WHERE storage_used > 0 ORDER BY storage_used DESC LIMIT #{limit}")
    List<User> findTopByStorageUsage(int limit);

    /**
     * 检查用户名是否存在
     */
    @Select("SELECT COUNT(*) FROM users WHERE username = #{username} AND id != #{excludeId}")
    int existsByUsername(@Param("username") String username, @Param("excludeId") Long excludeId);
    
    /**
     * 检查邮箱是否存在
     */
    @Select("SELECT COUNT(*) FROM users WHERE email = #{email} AND id != #{excludeId}")
    int existsByEmail(@Param("email") String email, @Param("excludeId") Long excludeId);
}
