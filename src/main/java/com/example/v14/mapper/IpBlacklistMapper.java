package com.example.v14.mapper;

import com.example.v14.entity.IpBlacklist;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * IP黑名单Mapper接口 - 简化版本
 */
@Mapper
public interface IpBlacklistMapper {
    
    /**
     * 根据ID查询黑名单
     */
    @Select("SELECT * FROM ip_blacklist WHERE id = #{id}")
    IpBlacklist findById(Long id);
    
    /**
     * 插入黑名单记录
     */
    @Insert("INSERT INTO ip_blacklist (ip_address, ip_type, status, reason, expires_at, created_by, created_time, updated_time, trigger_count, last_triggered_at) " +
            "VALUES (#{ipAddress}, #{ipType}, #{status}, #{reason}, #{expiresAt}, #{createdBy}, #{createdTime}, #{updatedTime}, #{triggerCount}, #{lastTriggeredAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(IpBlacklist ipBlacklist);
    
    /**
     * 更新黑名单记录
     */
    @Update("UPDATE ip_blacklist SET ip_address = #{ipAddress}, ip_type = #{ipType}, status = #{status}, " +
            "reason = #{reason}, expires_at = #{expiresAt}, updated_time = #{updatedTime}, " +
            "trigger_count = #{triggerCount}, last_triggered_at = #{lastTriggeredAt} WHERE id = #{id}")
    int update(IpBlacklist ipBlacklist);
    
    /**
     * 删除黑名单记录
     */
    @Delete("DELETE FROM ip_blacklist WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 查询所有黑名单
     */
    @Select("SELECT * FROM ip_blacklist ORDER BY created_time DESC")
    List<IpBlacklist> findAll();
    
    /**
     * 分页查询黑名单
     */
    @Select("SELECT * FROM ip_blacklist ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<IpBlacklist> findWithPaging(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 查询所有启用的黑名单
     */
    @Select("SELECT * FROM ip_blacklist WHERE status = 'ENABLED' " +
            "AND (expires_at IS NULL OR expires_at > NOW()) " +
            "ORDER BY created_time DESC")
    List<IpBlacklist> findAllEnabled();
    
    /**
     * 根据IP地址查询黑名单
     */
    @Select("SELECT * FROM ip_blacklist WHERE ip_address = #{ipAddress}")
    IpBlacklist findByIpAddress(String ipAddress);
    
    /**
     * 根据IP地址和状态查询黑名单
     */
    @Select("SELECT * FROM ip_blacklist WHERE ip_address = #{ipAddress} AND status = #{status}")
    IpBlacklist findByIpAddressAndStatus(@Param("ipAddress") String ipAddress, @Param("status") String status);
    
    /**
     * 检查IP是否在黑名单中
     */
    @Select("SELECT COUNT(*) FROM ip_blacklist WHERE ip_address = #{ipAddress} " +
            "AND status = 'ENABLED' " +
            "AND (expires_at IS NULL OR expires_at > NOW())")
    int countActiveByIpAddress(String ipAddress);
    
    /**
     * 统计黑名单总数
     */
    @Select("SELECT COUNT(*) FROM ip_blacklist")
    long countAll();
    
    /**
     * 统计启用的黑名单数量
     */
    @Select("SELECT COUNT(*) FROM ip_blacklist WHERE status = 'ENABLED' " +
            "AND (expires_at IS NULL OR expires_at > NOW())")
    long countEnabled();
    
    /**
     * 统计已过期的黑名单数量
     */
    @Select("SELECT COUNT(*) FROM ip_blacklist WHERE status = 'ENABLED' " +
            "AND expires_at IS NOT NULL AND expires_at <= NOW()")
    long countExpired();
    
    /**
     * 更新触发信息
     */
    @Update("UPDATE ip_blacklist SET trigger_count = trigger_count + 1, " +
            "last_triggered_at = #{triggeredAt}, updated_time = #{updatedTime} " +
            "WHERE ip_address = #{ipAddress}")
    int updateTriggerInfo(@Param("ipAddress") String ipAddress, 
                         @Param("triggeredAt") LocalDateTime triggeredAt,
                         @Param("updatedTime") LocalDateTime updatedTime);
    
    /**
     * 更新状态
     */
    @Update("UPDATE ip_blacklist SET status = #{status}, updated_time = #{updatedTime} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") String status, @Param("updatedTime") LocalDateTime updatedTime);
    
    // 批量操作已移除，Service层改用循环调用保证稳定性
    
    /**
     * 删除过期的黑名单记录
     */
    @Delete("DELETE FROM ip_blacklist WHERE status = 'ENABLED' " +
            "AND expires_at IS NOT NULL AND expires_at <= NOW()")
    int deleteExpired();
    
    /**
     * 根据创建者查询黑名单
     */
    @Select("SELECT * FROM ip_blacklist WHERE created_by = #{createdBy} ORDER BY created_time DESC")
    List<IpBlacklist> findByCreatedBy(Long createdBy);
    
    /**
     * 根据状态查询黑名单
     */
    @Select("SELECT * FROM ip_blacklist WHERE status = #{status} ORDER BY created_time DESC")
    List<IpBlacklist> findByStatus(String status);
    
    /**
     * 根据IP类型查询黑名单
     */
    @Select("SELECT * FROM ip_blacklist WHERE ip_type = #{ipType} ORDER BY created_time DESC")
    List<IpBlacklist> findByIpType(String ipType);
    
    /**
     * 查询即将过期的黑名单（用于提醒）
     */
    @Select("SELECT * FROM ip_blacklist WHERE status = 'ENABLED' " +
            "AND expires_at IS NOT NULL " +
            "AND expires_at BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{days} DAY) " +
            "ORDER BY expires_at ASC")
    List<IpBlacklist> findExpiringWithinDays(@Param("days") int days);
    
    /**
     * 获取触发次数最多的IP
     */
    @Select("SELECT * FROM ip_blacklist WHERE trigger_count > 0 " +
            "ORDER BY trigger_count DESC LIMIT #{limit}")
    List<IpBlacklist> findTopTriggered(@Param("limit") int limit);
    
    /**
     * 条件查询黑名单（分页）
     */
    @Select("SELECT * FROM ip_blacklist WHERE " +
            "(#{ipAddress} IS NULL OR ip_address LIKE CONCAT('%', #{ipAddress}, '%')) " +
            "AND (#{reason} IS NULL OR reason LIKE CONCAT('%', #{reason}, '%')) " +
            "AND (#{status} IS NULL OR status = #{status}) " +
            "AND (#{ipType} IS NULL OR ip_type = #{ipType}) " +
            "ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<IpBlacklist> findByCondition(@Param("ipAddress") String ipAddress,
                                     @Param("reason") String reason,
                                     @Param("status") String status,
                                     @Param("ipType") String ipType,
                                     @Param("offset") int offset,
                                     @Param("limit") int limit);
}