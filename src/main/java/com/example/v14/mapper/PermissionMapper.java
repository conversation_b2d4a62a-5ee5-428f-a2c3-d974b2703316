package com.example.v14.mapper;

import com.example.v14.entity.Permission;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 权限Mapper接口
 */
@Mapper
public interface PermissionMapper {
    
    /**
     * 根据ID查询权限
     */
    @Select("SELECT * FROM permissions WHERE id = #{id}")
    Permission findById(Long id);
    
    /**
     * 插入权限
     */
    @Insert("INSERT INTO permissions (user_id, resource_type, resource_id, permission_type, granted_by, created_time) " +
            "VALUES (#{userId}, #{resourceType}, #{resourceId}, #{permissionType}, #{grantedBy}, #{createdTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Permission permission);
    
    /**
     * 更新权限
     */
    @Update("UPDATE permissions SET permission_type = #{permissionType}, granted_by = #{grantedBy} WHERE id = #{id}")
    int update(Permission permission);
    
    /**
     * 删除权限
     */
    @Delete("DELETE FROM permissions WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 根据用户ID查询权限
     */
    @Select("SELECT * FROM permissions WHERE user_id = #{userId} ORDER BY created_time DESC")
    List<Permission> findByUserId(Long userId);
    
    /**
     * 根据资源查询权限
     */
    @Select("SELECT * FROM permissions WHERE resource_type = #{resourceType} AND resource_id = #{resourceId} ORDER BY created_time DESC")
    List<Permission> findByResource(@Param("resourceType") Permission.ResourceType resourceType, @Param("resourceId") Long resourceId);
    
    /**
     * 根据用户和资源查询权限
     */
    @Select("SELECT * FROM permissions WHERE user_id = #{userId} AND resource_type = #{resourceType} AND resource_id = #{resourceId}")
    List<Permission> findByUserAndResource(@Param("userId") Long userId, 
                                          @Param("resourceType") Permission.ResourceType resourceType, 
                                          @Param("resourceId") Long resourceId);
    
    /**
     * 检查用户是否有特定权限
     */
    @Select("SELECT COUNT(*) FROM permissions WHERE user_id = #{userId} AND resource_type = #{resourceType} " +
            "AND resource_id = #{resourceId} AND permission_type = #{permissionType}")
    int hasPermission(@Param("userId") Long userId,
                     @Param("resourceType") Permission.ResourceType resourceType,
                     @Param("resourceId") Long resourceId,
                     @Param("permissionType") Permission.PermissionType permissionType);
    
    /**
     * 根据权限类型查询权限
     */
    @Select("SELECT * FROM permissions WHERE permission_type = #{permissionType} ORDER BY created_time DESC")
    List<Permission> findByPermissionType(Permission.PermissionType permissionType);
    
    /**
     * 根据授权者查询权限
     */
    @Select("SELECT * FROM permissions WHERE granted_by = #{grantedBy} ORDER BY created_time DESC")
    List<Permission> findByGrantedBy(Long grantedBy);
    
    /**
     * 复合条件查询权限 - 简化版本
     */
    @Select("SELECT * FROM permissions WHERE 1=1 " +
            "AND (#{userId} IS NULL OR user_id = #{userId}) " +
            "AND (#{resourceType} IS NULL OR resource_type = #{resourceType}) " +
            "AND (#{resourceId} IS NULL OR resource_id = #{resourceId}) " +
            "AND (#{permissionType} IS NULL OR permission_type = #{permissionType}) " +
            "AND (#{grantedBy} IS NULL OR granted_by = #{grantedBy}) " +
            "ORDER BY created_time DESC LIMIT #{offset}, #{limit}")
    List<Permission> findByConditions(@Param("userId") Long userId,
                                     @Param("resourceType") Permission.ResourceType resourceType,
                                     @Param("resourceId") Long resourceId,
                                     @Param("permissionType") Permission.PermissionType permissionType,
                                     @Param("grantedBy") Long grantedBy,
                                     @Param("offset") int offset,
                                     @Param("limit") int limit);
    
    /**
     * 统计用户权限数量
     */
    @Select("SELECT COUNT(*) FROM permissions WHERE user_id = #{userId}")
    long countByUserId(Long userId);
    
    /**
     * 统计资源权限数量
     */
    @Select("SELECT COUNT(*) FROM permissions WHERE resource_type = #{resourceType} AND resource_id = #{resourceId}")
    long countByResource(@Param("resourceType") Permission.ResourceType resourceType, @Param("resourceId") Long resourceId);
    
    /**
     * 统计权限类型数量
     */
    @Select("SELECT COUNT(*) FROM permissions WHERE permission_type = #{permissionType}")
    long countByPermissionType(Permission.PermissionType permissionType);
    
    /**
     * 统计总权限数量
     */
    @Select("SELECT COUNT(*) FROM permissions")
    long countAll();
    
    /**
     * 按权限类型统计数量
     */
    @Select("SELECT permission_type, COUNT(*) as count FROM permissions GROUP BY permission_type ORDER BY count DESC")
    List<Object> countByPermissionTypeGroup();
    
    /**
     * 按资源类型统计数量
     */
    @Select("SELECT resource_type, COUNT(*) as count FROM permissions GROUP BY resource_type ORDER BY count DESC")
    List<Object> countByResourceTypeGroup();
    
    /**
     * 获取用户的所有文件权限
     */
    @Select("SELECT * FROM permissions WHERE user_id = #{userId} AND resource_type = 'FILE' ORDER BY created_time DESC")
    List<Permission> findFilePermissionsByUser(Long userId);
    
    /**
     * 获取用户的所有文件夹权限
     */
    @Select("SELECT * FROM permissions WHERE user_id = #{userId} AND resource_type = 'FOLDER' ORDER BY created_time DESC")
    List<Permission> findFolderPermissionsByUser(Long userId);
    
    /**
     * 获取文件的所有权限
     */
    @Select("SELECT * FROM permissions WHERE resource_type = 'FILE' AND resource_id = #{fileId} ORDER BY created_time DESC")
    List<Permission> findPermissionsByFile(Long fileId);
    
    /**
     * 获取文件夹的所有权限
     */
    @Select("SELECT * FROM permissions WHERE resource_type = 'FOLDER' AND resource_id = #{folderId} ORDER BY created_time DESC")
    List<Permission> findPermissionsByFolder(Long folderId);
    
    /**
     * 删除用户的所有权限
     */
    @Delete("DELETE FROM permissions WHERE user_id = #{userId}")
    int deleteByUserId(Long userId);
    
    /**
     * 删除资源的所有权限
     */
    @Delete("DELETE FROM permissions WHERE resource_type = #{resourceType} AND resource_id = #{resourceId}")
    int deleteByResource(@Param("resourceType") Permission.ResourceType resourceType, @Param("resourceId") Long resourceId);
    
    /**
     * 删除特定权限
     */
    @Delete("DELETE FROM permissions WHERE user_id = #{userId} AND resource_type = #{resourceType} " +
            "AND resource_id = #{resourceId} AND permission_type = #{permissionType}")
    int deleteSpecificPermission(@Param("userId") Long userId,
                                @Param("resourceType") Permission.ResourceType resourceType,
                                @Param("resourceId") Long resourceId,
                                @Param("permissionType") Permission.PermissionType permissionType);
    
    // 批量插入权限 - 已移除，Service层改用循环调用insert方法保证稳定性
    
    // 批量删除权限 - 已移除，Service层改用循环调用deleteById方法保证稳定性
    
    /**
     * 获取最近授予的权限
     */
    @Select("SELECT * FROM permissions ORDER BY created_time DESC LIMIT #{limit}")
    List<Permission> findRecent(int limit);
    
    /**
     * 获取详细权限信息（包含用户和授权者信息）
     */
    @Select("SELECT p.*, u1.username as user_name, u2.username as granted_by_name " +
            "FROM permissions p " +
            "LEFT JOIN users u1 ON p.user_id = u1.id " +
            "LEFT JOIN users u2 ON p.granted_by = u2.id " +
            "ORDER BY p.created_time DESC LIMIT #{offset}, #{limit}")
    List<Object> findDetailedPermissions(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 检查用户是否有资源的任意权限
     */
    @Select("SELECT COUNT(*) FROM permissions WHERE user_id = #{userId} AND resource_type = #{resourceType} AND resource_id = #{resourceId}")
    int hasAnyPermission(@Param("userId") Long userId,
                        @Param("resourceType") Permission.ResourceType resourceType,
                        @Param("resourceId") Long resourceId);
}
