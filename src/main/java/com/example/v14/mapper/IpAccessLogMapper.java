package com.example.v14.mapper;

import com.example.v14.entity.IpAccessLog;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * IP访问日志Mapper接口 - 简化版本
 */
@Mapper
public interface IpAccessLogMapper {
    
    /**
     * 根据ID查询访问记录
     */
    @Select("SELECT * FROM ip_access_logs WHERE id = #{id}")
    IpAccessLog findById(Long id);
    
    /**
     * 插入访问记录
     */
    @Insert("INSERT INTO ip_access_logs (ip_address, user_id, request_url, request_method, " +
            "user_agent, referer, access_status, access_result, location, processing_time, access_time) " +
            "VALUES (#{ipAddress}, #{userId}, #{requestUrl}, #{requestMethod}, " +
            "#{userAgent}, #{referer}, #{accessStatus}, #{accessResult}, #{location}, #{processingTime}, #{accessTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(IpAccessLog ipAccessLog);
    
    // 批量插入访问记录 - 已移除，Service层改用循环调用insert方法保证稳定性
    
    /**
     * 根据IP地址查询访问记录
     */
    @Select("SELECT * FROM ip_access_logs WHERE ip_address = #{ipAddress} ORDER BY access_time DESC LIMIT #{offset}, #{limit}")
    List<IpAccessLog> findByIpAddress(@Param("ipAddress") String ipAddress, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 根据用户ID查询访问记录
     */
    @Select("SELECT * FROM ip_access_logs WHERE user_id = #{userId} ORDER BY access_time DESC LIMIT #{offset}, #{limit}")
    List<IpAccessLog> findByUserId(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 基础条件查询访问记录
     */
    @Select("SELECT * FROM ip_access_logs WHERE " +
            "(#{ipAddress} IS NULL OR ip_address = #{ipAddress}) " +
            "ORDER BY access_time DESC LIMIT #{offset}, #{limit}")
    List<IpAccessLog> findByIpAddressSimple(@Param("ipAddress") String ipAddress, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 统计访问记录数量
     */
    @Select("SELECT COUNT(*) FROM ip_access_logs WHERE " +
            "(#{ipAddress} IS NULL OR ip_address = #{ipAddress})")
    long countByIpAddressSimple(@Param("ipAddress") String ipAddress);
    
    /**
     * 统计IP访问次数
     */
    @Select("SELECT COUNT(*) FROM ip_access_logs WHERE ip_address = #{ipAddress}")
    long countByIpAddress(String ipAddress);
    
    /**
     * 统计用户访问次数
     */
    @Select("SELECT COUNT(*) FROM ip_access_logs WHERE user_id = #{userId}")
    long countByUserId(Long userId);
    
    /**
     * 统计时间范围内访问次数
     */
    @Select("SELECT COUNT(*) FROM ip_access_logs WHERE access_time BETWEEN #{startTime} AND #{endTime}")
    long countByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计总访问次数
     */
    @Select("SELECT COUNT(*) FROM ip_access_logs")
    long countAll();
    
    /**
     * 统计拦截次数
     */
    @Select("SELECT COUNT(*) FROM ip_access_logs WHERE access_status = 'blocked'")
    long countBlocked();
    
    /**
     * 获取最近访问记录
     */
    @Select("SELECT * FROM ip_access_logs ORDER BY access_time DESC LIMIT #{limit}")
    List<IpAccessLog> findRecent(int limit);
    
    /**
     * 根据时间范围查询访问记录
     */
    @Select("SELECT * FROM ip_access_logs WHERE access_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY access_time DESC LIMIT #{offset}, #{limit}")
    List<IpAccessLog> findByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                     @Param("endTime") LocalDateTime endTime,
                                     @Param("offset") int offset, 
                                     @Param("limit") int limit);
    
    /**
     * 删除过期的访问记录
     */
    @Delete("DELETE FROM ip_access_logs WHERE access_time < #{cutoffTime}")
    int deleteOldRecords(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    /**
     * 查找可疑IP地址
     */
    @Select("SELECT ip_address, COUNT(*) as blocked_count, MAX(access_time) as last_blocked " +
            "FROM ip_access_logs " +
            "WHERE access_status = 'blocked' AND access_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY ip_address HAVING blocked_count >= #{threshold} " +
            "ORDER BY blocked_count DESC LIMIT #{limit}")
    List<Map<String, Object>> findSuspiciousIps(@Param("startTime") LocalDateTime startTime, 
                                               @Param("endTime") LocalDateTime endTime, 
                                               @Param("threshold") int threshold, 
                                               @Param("limit") int limit);
    
    /**
     * 获取访问统计
     */
    @Select("SELECT access_status, COUNT(*) as count " +
            "FROM ip_access_logs " +
            "WHERE access_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY access_status")
    List<Map<String, Object>> getAccessStats(@Param("startTime") LocalDateTime startTime, 
                                            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取热门访问路径
     */
    @Select("SELECT request_url, COUNT(*) as count " +
            "FROM ip_access_logs " +
            "WHERE access_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY request_url " +
            "ORDER BY count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getPopularPaths(@Param("startTime") LocalDateTime startTime, 
                                             @Param("endTime") LocalDateTime endTime,
                                             @Param("limit") int limit);
    
    /**
     * 按日期统计访问量
     */
    @Select("SELECT DATE(access_time) as date, COUNT(*) as count " +
            "FROM ip_access_logs " +
            "WHERE access_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY DATE(access_time) " +
            "ORDER BY date")
    List<Map<String, Object>> getDailyStats(@Param("startTime") LocalDateTime startTime, 
                                           @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计IP在时间范围内的访问次数
     */
    @Select("SELECT COUNT(*) FROM ip_access_logs " +
            "WHERE ip_address = #{ipAddress} " +
            "AND access_time BETWEEN #{startTime} AND #{endTime}")
    long countByIpAndTimeRange(@Param("ipAddress") String ipAddress,
                              @Param("startTime") LocalDateTime startTime,
                              @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找访问频率最高的IP
     */
    @Select("SELECT ip_address, COUNT(*) as count " +
            "FROM ip_access_logs " +
            "WHERE access_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY ip_address " +
            "ORDER BY count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> findMostFrequentIps(@Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime,
                                                 @Param("limit") int limit);
    
    /**
     * 查找最受欢迎的页面
     */
    @Select("SELECT request_url, COUNT(*) as count " +
            "FROM ip_access_logs " +
            "WHERE access_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY request_url " +
            "ORDER BY count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> findMostPopularPages(@Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime,
                                                  @Param("limit") int limit);
    
    /**
     * 按日期统计访问量
     */
    @Select("SELECT DATE(access_time) as date, COUNT(*) as count " +
            "FROM ip_access_logs " +
            "WHERE access_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY DATE(access_time) " +
            "ORDER BY date")
    List<Map<String, Object>> countByDate(@Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);
    
    /**
     * 按小时统计访问量
     */
    @Select("SELECT HOUR(access_time) as hour, COUNT(*) as count " +
            "FROM ip_access_logs " +
            "WHERE access_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY HOUR(access_time) " +
            "ORDER BY hour")
    List<Map<String, Object>> countByHour(@Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计时间范围内的访问量（兼容long类型参数）
     */
    @Select("SELECT COUNT(*) FROM ip_access_logs " +
            "WHERE UNIX_TIMESTAMP(access_time) * 1000 BETWEEN #{startTime} AND #{endTime}")
    long countByDateRange(@Param("startTime") long startTime, @Param("endTime") long endTime);
}