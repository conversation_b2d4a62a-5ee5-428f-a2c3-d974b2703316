package com.example.v14.interceptor;

import cn.dev33.satoken.stp.StpUtil;
import com.example.v14.service.IpManagementService;
import com.example.v14.service.LoggingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * IP访问拦截器
 * 用于检查IP黑白名单，控制访问权限，并记录访问日志
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IpAccessInterceptor implements HandlerInterceptor {

    private final IpManagementService ipManagementService;
    private final LoggingService loggingService;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String clientIp = getClientIpAddress(request);
        long startTime = System.currentTimeMillis();

        // 检查IP是否被允许访问
        IpManagementService.IpCheckResult checkResult = ipManagementService.checkIpAccess(clientIp);
        if (!checkResult.isAllowed()) {
            log.warn("IP {} 访问被拒绝 - {}", clientIp, checkResult.getReason());
            
            // 记录访问被拒绝的日志
            try {
                loggingService.logIpAccessDenied(request, checkResult.getReason());
            } catch (Exception e) {
                log.debug("记录IP访问拒绝日志失败: {}", e.getMessage());
            }
            
            handleAccessDenied(response, clientIp, checkResult.getReason());
            return false;
        }

        // 记录IP访问日志
        try {
            Long userId = null;
            if (StpUtil.isLogin()) {
                userId = StpUtil.getLoginIdAsLong();
            }
            loggingService.logIpAccess(request, userId);
        } catch (Exception e) {
            log.debug("记录IP访问日志失败: {}", e.getMessage());
        }

        log.debug("IP {} 访问允许，处理时间: {}ms", clientIp, System.currentTimeMillis() - startTime);

        return true;
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            // 多级代理的情况，第一个IP为客户端真实IP
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        String proxyClientIp = request.getHeader("Proxy-Client-IP");
        if (proxyClientIp != null && !proxyClientIp.isEmpty() && !"unknown".equalsIgnoreCase(proxyClientIp)) {
            return proxyClientIp;
        }
        
        String wlProxyClientIp = request.getHeader("WL-Proxy-Client-IP");
        if (wlProxyClientIp != null && !wlProxyClientIp.isEmpty() && !"unknown".equalsIgnoreCase(wlProxyClientIp)) {
            return wlProxyClientIp;
        }
        
        String httpClientIp = request.getHeader("HTTP_CLIENT_IP");
        if (httpClientIp != null && !httpClientIp.isEmpty() && !"unknown".equalsIgnoreCase(httpClientIp)) {
            return httpClientIp;
        }
        
        String httpXForwardedFor = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (httpXForwardedFor != null && !httpXForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(httpXForwardedFor)) {
            return httpXForwardedFor;
        }
        
        // 如果都没有，返回request.getRemoteAddr()
        return request.getRemoteAddr();
    }
    
    /**
     * 处理访问被拒绝的情况
     */
    private void handleAccessDenied(HttpServletResponse response, String clientIp, String reason) throws IOException {
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        response.setContentType("application/json;charset=UTF-8");
        
        String jsonResponse = String.format(
            "{\"success\": false, \"message\": \"访问被拒绝: %s\", \"code\": 403, \"ip\": \"%s\"}", 
            reason, clientIp
        );
        
        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
    }
}
