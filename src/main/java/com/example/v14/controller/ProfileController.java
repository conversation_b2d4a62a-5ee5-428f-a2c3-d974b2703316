package com.example.v14.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.example.v14.common.Result;
import com.example.v14.entity.User;
import com.example.v14.service.UserService;
import com.example.v14.service.AnalyticsService;
import com.example.v14.mapper.FileMapper;
import com.example.v14.mapper.FolderMapper;
import com.example.v14.mapper.ShareMapper;
import com.example.v14.config.FileConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Map;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 个人中心控制器
 */
@Slf4j
@Controller
@RequiredArgsConstructor
public class ProfileController {
    
    private final UserService userService;
    private final AnalyticsService analyticsService;
    private final FileMapper fileMapper;
    private final FolderMapper folderMapper;
    private final ShareMapper shareMapper;
    private final FileConfig fileConfig;
    
    /**
     * 个人中心页面
     */
    @GetMapping("/profile")
    public String profilePage(Model model) {
        Long userId = StpUtil.getLoginIdAsLong();
        User user = userService.findById(userId);
        
        model.addAttribute("user", user);
        
        return "profile";
    }
    
    /**
     * 获取用户信息
     */
    @GetMapping("/api/profile")
    @ResponseBody
    public Result<User> getUserProfile() {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            User user = userService.findById(userId);
            
            // 清除敏感信息
            user.setPassword(null);
            
            return Result.success(user);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 更新用户信息
     */
    @PutMapping("/api/profile")
    @ResponseBody
    public Result<Object> updateProfile(@RequestParam(required = false) String nickname,
                                       @RequestParam(required = false) String email) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            User user = userService.findById(userId);
            
            if (user == null) {
                return Result.notFound("用户不存在");
            }
            
            // 更新信息
            if (nickname != null && !nickname.trim().isEmpty()) {
                user.setNickname(nickname.trim());
            }
            
            if (email != null && !email.trim().isEmpty()) {
                user.setEmail(email.trim());
            }
            
            userService.updateUser(user);
            
            // 更新Session中的用户信息
            StpUtil.getSession().set("user", user);
            
            return Result.success("信息更新成功");
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 修改密码
     */
    @PutMapping("/api/profile/password")
    @ResponseBody
    public Result<Object> changePassword(@RequestParam String oldPassword,
                                        @RequestParam String newPassword,
                                        @RequestParam String confirmPassword) {
        try {
            // 验证新密码
            if (!newPassword.equals(confirmPassword)) {
                return Result.badRequest("两次输入的密码不一致");
            }

            if (newPassword.length() < 6) {
                return Result.badRequest("密码长度至少6位");
            }

            Long userId = StpUtil.getLoginIdAsLong();
            userService.changePassword(userId, oldPassword, newPassword);

            return Result.success("密码修改成功");
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 头像上传
     */
    @PostMapping("/api/profile/avatar")
    @ResponseBody
    public Result<String> uploadAvatar(@RequestParam("avatar") MultipartFile avatarFile) {
        try {
            // 验证文件
            if (avatarFile.isEmpty()) {
                return Result.badRequest("请选择头像文件");
            }

            // 验证文件类型
            String contentType = avatarFile.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return Result.badRequest("只能上传图片文件");
            }

            // 验证文件大小（2MB）
            if (avatarFile.getSize() > 2 * 1024 * 1024) {
                return Result.badRequest("头像文件大小不能超过2MB");
            }

            Long userId = StpUtil.getLoginIdAsLong();
            
            // 创建头像存储目录
            String uploadPath = fileConfig.getUploadPath() + "avatars" + File.separator;
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                boolean created = uploadDir.mkdirs();
                if (!created) {
                    log.error("创建头像目录失败: {}", uploadPath);
                    return Result.error("头像目录创建失败");
                }
                log.info("头像目录创建成功: {}", uploadPath);
            }

            // 生成文件名
            String originalFileName = avatarFile.getOriginalFilename();
            String fileExtension = "";
            if (originalFileName != null && originalFileName.contains(".")) {
                fileExtension = originalFileName.substring(originalFileName.lastIndexOf("."));
            }
            String fileName = "avatar_" + userId + "_" + System.currentTimeMillis() + fileExtension;
            String filePath = uploadPath + fileName;

            // 保存文件
            Path targetPath = Paths.get(filePath);
            try {
                Files.copy(avatarFile.getInputStream(), targetPath, java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                log.info("头像文件保存成功: {}", filePath);
            } catch (IOException e) {
                log.error("头像文件保存失败: {}", filePath, e);
                return Result.error("文件保存失败: " + e.getMessage());
            }

            // 生成访问URL
            String avatarUrl = "/api/files/avatar/" + fileName;

            // 更新用户头像信息
            User user = userService.findById(userId);
            if (user != null) {
                user.setAvatar(avatarUrl);
                try {
                    userService.updateUser(user);
                    log.info("用户头像URL更新成功: userId={}, avatarUrl={}", userId, avatarUrl);
                } catch (Exception e) {
                    log.error("更新用户头像URL失败: userId={}", userId, e);
                    return Result.error("数据库更新失败: " + e.getMessage());
                }
            } else {
                log.error("未找到用户: userId={}", userId);
                return Result.error("用户不存在");
            }

            log.info("用户头像上传成功: userId={}, fileName={}", userId, fileName);
            return Result.success("头像上传成功", avatarUrl);
        } catch (Exception e) {
            log.error("头像上传失败", e);
            return Result.error(e.getMessage());
        }
    }


    
    /**
     * 获取用户统计信息
     */
    @GetMapping("/api/profile/stats")
    @ResponseBody
    public Result<UserStats> getUserStats() {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            User user = userService.findById(userId);

            // 获取真实的用户统计数据
            UserStats stats = new UserStats();

            // 文件统计
            stats.setFileCount(fileMapper.countByUser(userId));

            // 文件夹统计
            stats.setFolderCount(folderMapper.countByUser(userId));

            // 分享统计
            stats.setShareCount(shareMapper.countByUser(userId));

            // 下载统计（用户下载的文件次数）
            Map<String, Object> downloadStats = analyticsService.getUserDownloadStats(userId,
                LocalDateTime.now().minusMonths(1), LocalDateTime.now());
            stats.setDownloadCount((Long) downloadStats.getOrDefault("downloadCount", 0L));

            // 存储使用情况 - 使用实际计算的存储使用量
            Long actualStorageUsed = fileMapper.sumFileSizeByUser(userId);
            stats.setStorageUsed(actualStorageUsed);
            stats.setStorageLimit(user.getStorageLimit());

            // 同步更新用户表中的存储使用量（如果不一致）
            if (!actualStorageUsed.equals(user.getStorageUsed())) {
                userService.updateStorageUsed(userId, actualStorageUsed);
            }

            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取用户统计失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 用户统计信息类
     */
    public static class UserStats {
        private Long fileCount;
        private Long folderCount;
        private Long shareCount;
        private Long downloadCount;
        private Long storageUsed;
        private Long storageLimit;
        
        // Getters and Setters
        public Long getFileCount() { return fileCount; }
        public void setFileCount(Long fileCount) { this.fileCount = fileCount; }
        
        public Long getFolderCount() { return folderCount; }
        public void setFolderCount(Long folderCount) { this.folderCount = folderCount; }
        
        public Long getShareCount() { return shareCount; }
        public void setShareCount(Long shareCount) { this.shareCount = shareCount; }
        
        public Long getDownloadCount() { return downloadCount; }
        public void setDownloadCount(Long downloadCount) { this.downloadCount = downloadCount; }
        
        public Long getStorageUsed() { return storageUsed; }
        public void setStorageUsed(Long storageUsed) { this.storageUsed = storageUsed; }
        
        public Long getStorageLimit() { return storageLimit; }
        public void setStorageLimit(Long storageLimit) { this.storageLimit = storageLimit; }
    }
}
