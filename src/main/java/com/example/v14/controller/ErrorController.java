package com.example.v14.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 错误页面控制器
 */
@Controller
public class ErrorController {
    
    /**
     * 系统错误页面
     */
    @GetMapping("/error")
    public String errorPage(Model model) {
        model.addAttribute("error", "系统暂时不可用，请稍后重试");
        return "error";
    }
}