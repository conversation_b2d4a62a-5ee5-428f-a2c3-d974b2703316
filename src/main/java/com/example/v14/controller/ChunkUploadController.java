package com.example.v14.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.example.v14.common.Result;
import com.example.v14.entity.ChunkUpload;
import com.example.v14.service.ChunkUploadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 分片上传控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/chunk-upload")
@RequiredArgsConstructor
public class ChunkUploadController {
    
    private final ChunkUploadService chunkUploadService;
    
    /**
     * 初始化分片上传
     */
    @PostMapping("/init")
    public Result<ChunkUpload> initUpload(@RequestParam String fileName,
                                         @RequestParam Long fileSize,
                                         @RequestParam String fileMd5,
                                         @RequestParam(defaultValue = "2097152") Long chunkSize, // 默认2MB
                                         @RequestParam(defaultValue = "0") Long folderId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            
            ChunkUpload chunkUpload = chunkUploadService.initChunkUpload(
                    fileName, fileSize, fileMd5, chunkSize, userId, folderId);
            
            return Result.success("分片上传初始化成功", chunkUpload);
        } catch (Exception e) {
            log.error("初始化分片上传失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 上传分片
     */
    @PostMapping("/chunk")
    public Result<ChunkUpload> uploadChunk(@RequestParam String uploadId,
                                          @RequestParam Integer chunkNumber,
                                          @RequestParam("chunk") MultipartFile chunkFile) {
        try {
            log.info("接收分片上传请求: uploadId={}, chunkNumber={}, chunkSize={}",
                    uploadId, chunkNumber, chunkFile.getSize());
            ChunkUpload chunkUpload = chunkUploadService.uploadChunk(uploadId, chunkNumber, chunkFile);
            return Result.success("分片上传成功", chunkUpload);
        } catch (Exception e) {
            log.error("分片上传失败: uploadId={}, chunkNumber={}", uploadId, chunkNumber, e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取上传状态
     */
    @GetMapping("/status/{uploadId}")
    public Result<ChunkUpload> getUploadStatus(@PathVariable String uploadId) {
        try {
            ChunkUpload chunkUpload = chunkUploadService.getUploadStatus(uploadId);
            if (chunkUpload == null) {
                return Result.notFound("上传会话不存在");
            }
            return Result.success(chunkUpload);
        } catch (Exception e) {
            log.error("获取上传状态失败: uploadId={}", uploadId, e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 取消上传
     */
    @DeleteMapping("/{uploadId}")
    public Result<Object> cancelUpload(@PathVariable String uploadId) {
        try {
            chunkUploadService.cancelUpload(uploadId);
            return Result.success("上传已取消");
        } catch (Exception e) {
            log.error("取消上传失败: uploadId={}", uploadId, e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 检查分片是否已上传
     */
    @GetMapping("/check/{uploadId}/{chunkNumber}")
    public Result<Boolean> checkChunk(@PathVariable String uploadId,
                                     @PathVariable Integer chunkNumber) {
        try {
            ChunkUpload chunkUpload = chunkUploadService.getUploadStatus(uploadId);
            if (chunkUpload == null) {
                return Result.success(false);
            }
            
            boolean uploaded = chunkUpload.getUploadedChunks().contains(chunkNumber);
            return Result.success(uploaded);
        } catch (Exception e) {
            log.error("检查分片状态失败: uploadId={}, chunkNumber={}", uploadId, chunkNumber, e);
            return Result.error(e.getMessage());
        }
    }
}
