package com.example.v14.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.example.v14.common.Result;
import com.example.v14.entity.Folder;
import com.example.v14.service.FolderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

/**
 * 文件夹管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/folders")
@RequiredArgsConstructor
public class FolderController {
    
    private final FolderService folderService;
    
    /**
     * 创建文件夹
     */
    @PostMapping
    public Result<Folder> createFolder(@RequestBody CreateFolderRequest request) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            Folder folder = folderService.createFolder(request.getName(), request.getParentId(), userId);
            return Result.success("文件夹创建成功", folder);
        } catch (Exception e) {
            log.error("创建文件夹失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 创建文件夹请求DTO
     */
    public static class CreateFolderRequest {
        private String name;
        private Long parentId = 0L;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Long getParentId() {
            return parentId;
        }

        public void setParentId(Long parentId) {
            this.parentId = parentId;
        }
    }
    
    /**
     * 重命名文件夹
     */
    @PutMapping("/{folderId}/rename")
    public Result<Object> renameFolder(@PathVariable Long folderId, @RequestBody RenameRequest request) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            folderService.renameFolder(folderId, request.getNewName(), userId);
            return Result.success("文件夹重命名成功");
        } catch (Exception e) {
            log.error("重命名文件夹失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 重命名请求DTO
     */
    public static class RenameRequest {
        private String newName;

        public String getNewName() {
            return newName;
        }

        public void setNewName(String newName) {
            this.newName = newName;
        }
    }
    
    /**
     * 移动文件夹
     */
    @PutMapping("/{folderId}/move")
    public Result<Object> moveFolder(@PathVariable Long folderId, @RequestParam Long targetParentId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            log.info("开始移动文件夹: folderId={}, targetParentId={}, userId={}", folderId, targetParentId, userId);
            folderService.moveFolder(folderId, targetParentId, userId);
            log.info("文件夹移动成功: folderId={}", folderId);
            return Result.success("文件夹移动成功");
        } catch (Exception e) {
            log.error("移动文件夹失败: folderId={}, targetParentId={}, userId={}", folderId, targetParentId, StpUtil.getLoginIdAsLong(), e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 删除文件夹
     */
    @DeleteMapping("/{folderId}")
    public Result<Object> deleteFolder(@PathVariable Long folderId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            folderService.deleteFolder(folderId, userId);
            return Result.success("文件夹删除成功");
        } catch (Exception e) {
            log.error("删除文件夹失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取文件夹列表
     */
    @GetMapping
    public Result<List<Folder>> getFolders(@RequestParam(defaultValue = "0") Long parentId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            List<Folder> folders = folderService.getUserFolders(userId, parentId);
            return Result.success(folders);
        } catch (Exception e) {
            log.error("获取文件夹列表失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取所有文件夹（用于移动操作的目标选择）
     */
    @GetMapping("/all")
    public Result<List<Folder>> getAllFolders() {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            List<Folder> folders = folderService.getAllUserFolders(userId);
            return Result.success(folders);
        } catch (Exception e) {
            log.error("获取所有文件夹失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取文件夹信息
     */
    @GetMapping("/{folderId}")
    public Result<Folder> getFolderInfo(@PathVariable Long folderId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            Folder folder = folderService.findById(folderId);
            
            if (folder == null || !folder.getUserId().equals(userId)) {
                return Result.notFound("文件夹不存在");
            }
            
            return Result.success(folder);
        } catch (Exception e) {
            log.error("获取文件夹信息失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 下载文件夹
     */
    @GetMapping("/{folderId}/download")
    public ResponseEntity<Resource> downloadFolder(@PathVariable Long folderId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            
            // 获取文件夹信息
            Folder folder = folderService.findById(folderId);
            if (folder == null || !folder.getUserId().equals(userId)) {
                return ResponseEntity.notFound().build();
            }
            
            // 创建ZIP文件
            File zipFile = folderService.downloadFolder(folderId, userId);
            
            if (!zipFile.exists()) {
                return ResponseEntity.notFound().build();
            }
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION,
                       "attachment; filename=\"" + encodeFileName(folder.getName() + ".zip") + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, "application/zip");
            
            Resource resource = new FileSystemResource(zipFile);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(zipFile.length())
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("文件夹下载失败", e);
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * 编码文件名
     */
    private String encodeFileName(String fileName) {
        try {
            return URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException e) {
            return fileName;
        }
    }
}
