package com.example.v14.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.example.v14.common.Result;
import com.example.v14.entity.User;
import com.example.v14.entity.FileInfo;
import com.example.v14.service.UserService;
import com.example.v14.service.FileService;
import com.example.v14.service.AnalyticsService;
import com.example.v14.mapper.FileMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 认证控制器
 */
@Slf4j
@Controller
@RequiredArgsConstructor
public class AuthController {
    
    private final UserService userService;
    private final FileService fileService;
    private final AnalyticsService analyticsService;
    private final FileMapper fileMapper;
    
    /**
     * 首页
     */
    @GetMapping("/")
    public String index() {
        if (StpUtil.isLogin()) {
            return "redirect:/dashboard";
        }
        return "redirect:/login";
    }
    
    /**
     * 登录页面
     */
    @GetMapping("/login")
    public String loginPage() {
        if (StpUtil.isLogin()) {
            return "redirect:/dashboard";
        }
        return "login";
    }
    
    /**
     * 注册页面
     */
    @GetMapping("/register")
    public String registerPage() {
        if (StpUtil.isLogin()) {
            return "redirect:/dashboard";
        }
        return "register";
    }
    
    /**
     * 用户登录
     */
    @PostMapping("/api/auth/login")
    @ResponseBody
    public Result<Object> login(@RequestParam String username, 
                               @RequestParam String password,
                               HttpServletRequest request) {
        try {
            User user = userService.login(username, password);
            
            // Sa-Token登录
            StpUtil.login(user.getId());
            StpUtil.getSession().set("user", user);
            StpUtil.getSession().set("role", user.getRole().name());
            
            // 更新最后登录信息
            String ip = getClientIp(request);
            userService.updateLastLogin(user.getId(), ip);
            
            log.info("用户登录成功: {}, IP: {}", username, ip);
            
            return Result.success("登录成功");
            
        } catch (Exception e) {
            log.error("用户登录失败: {}, 错误: {}", username, e.getMessage());
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 用户注册
     */
    @PostMapping("/api/auth/register")
    @ResponseBody
    public Result<Object> register(@RequestParam String username,
                                  @RequestParam String password,
                                  @RequestParam(required = false) String email,
                                  @RequestParam(required = false) String nickname,
                                  HttpServletRequest request) {
        try {
            User user = userService.register(username, password, email, nickname);
            
            String ip = getClientIp(request);
            log.info("用户注册成功: {}, IP: {}", username, ip);
            
            return Result.success("注册成功");
            
        } catch (Exception e) {
            log.error("用户注册失败: {}, 错误: {}", username, e.getMessage());
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 用户登出
     */
    @PostMapping("/api/auth/logout")
    @ResponseBody
    public Result<Object> logout() {
        try {
            StpUtil.logout();
            return Result.success("登出成功");
        } catch (Exception e) {
            log.error("用户登出失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/api/auth/current")
    @ResponseBody
    public Result<User> getCurrentUser() {
        try {
            if (!StpUtil.isLogin()) {
                return Result.unauthorized("未登录");
            }
            
            User user = (User) StpUtil.getSession().get("user");
            if (user == null) {
                Long userId = StpUtil.getLoginIdAsLong();
                user = userService.findById(userId);
                StpUtil.getSession().set("user", user);
            }
            
            return Result.success(user);
            
        } catch (Exception e) {
            log.error("获取当前用户信息失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 仪表板页面
     */
    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        if (!StpUtil.isLogin()) {
            return "redirect:/login";
        }
        
        try {
            User user = (User) StpUtil.getSession().get("user");
            if (user == null) {
                Long userId = StpUtil.getLoginIdAsLong();
                user = userService.findById(userId);
                StpUtil.getSession().set("user", user);
            }
            
            model.addAttribute("user", user);
            return "dashboard";
            
        } catch (Exception e) {
            log.error("加载仪表板失败: {}", e.getMessage());
            return "redirect:/login";
        }
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
