package com.example.v14.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import com.example.v14.common.Result;
import com.example.v14.entity.Permission;
import com.example.v14.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 权限管理控制器
 */
@Slf4j
@Controller
@RequiredArgsConstructor
@SaCheckRole("ADMIN")
public class PermissionController {
    
    private final PermissionService permissionService;
    
    /**
     * 权限管理页面
     */
    @GetMapping("/admin/permissions")
    public String permissionsPage(Model model) {
        // 获取权限统计信息
        Map<String, Object> stats = permissionService.getPermissionStats();
        model.addAttribute("stats", stats);
        
        return "admin/permissions";
    }
    
    /**
     * 授予权限
     */
    @PostMapping("/api/admin/permissions/grant")
    @ResponseBody
    public Result<Object> grantPermission(@RequestParam Long userId,
                                         @RequestParam Permission.ResourceType resourceType,
                                         @RequestParam Long resourceId,
                                         @RequestParam Permission.PermissionType permissionType) {
        try {
            Long grantedBy = StpUtil.getLoginIdAsLong();
            permissionService.grantPermission(userId, resourceType, resourceId, permissionType, grantedBy);
            return Result.success("权限授予成功");
        } catch (Exception e) {
            log.error("授予权限失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 撤销权限
     */
    @DeleteMapping("/api/admin/permissions/revoke")
    @ResponseBody
    public Result<Object> revokePermission(@RequestParam Long userId,
                                          @RequestParam Permission.ResourceType resourceType,
                                          @RequestParam Long resourceId,
                                          @RequestParam Permission.PermissionType permissionType) {
        try {
            permissionService.revokePermission(userId, resourceType, resourceId, permissionType);
            return Result.success("权限撤销成功");
        } catch (Exception e) {
            log.error("撤销权限失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 检查权限
     */
    @GetMapping("/api/admin/permissions/check")
    @ResponseBody
    public Result<Boolean> checkPermission(@RequestParam Long userId,
                                          @RequestParam Permission.ResourceType resourceType,
                                          @RequestParam Long resourceId,
                                          @RequestParam Permission.PermissionType permissionType) {
        try {
            boolean hasPermission = permissionService.hasPermission(userId, resourceType, resourceId, permissionType);
            return Result.success(hasPermission);
        } catch (Exception e) {
            log.error("检查权限失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取用户权限列表
     */
    @GetMapping("/api/admin/permissions/user/{userId}")
    @ResponseBody
    public Result<List<Permission>> getUserPermissions(@PathVariable Long userId) {
        try {
            List<Permission> permissions = permissionService.getUserPermissions(userId);
            return Result.success(permissions);
        } catch (Exception e) {
            log.error("获取用户权限失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取用户文件权限
     */
    @GetMapping("/api/admin/permissions/user/{userId}/files")
    @ResponseBody
    public Result<List<Permission>> getUserFilePermissions(@PathVariable Long userId) {
        try {
            List<Permission> permissions = permissionService.getUserFilePermissions(userId);
            return Result.success(permissions);
        } catch (Exception e) {
            log.error("获取用户文件权限失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取用户文件夹权限
     */
    @GetMapping("/api/admin/permissions/user/{userId}/folders")
    @ResponseBody
    public Result<List<Permission>> getUserFolderPermissions(@PathVariable Long userId) {
        try {
            List<Permission> permissions = permissionService.getUserFolderPermissions(userId);
            return Result.success(permissions);
        } catch (Exception e) {
            log.error("获取用户文件夹权限失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取资源权限列表
     */
    @GetMapping("/api/admin/permissions/resource")
    @ResponseBody
    public Result<List<Permission>> getResourcePermissions(@RequestParam Permission.ResourceType resourceType,
                                                          @RequestParam Long resourceId) {
        try {
            List<Permission> permissions = permissionService.getResourcePermissions(resourceType, resourceId);
            return Result.success(permissions);
        } catch (Exception e) {
            log.error("获取资源权限失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取文件权限列表
     */
    @GetMapping("/api/admin/permissions/file/{fileId}")
    @ResponseBody
    public Result<List<Permission>> getFilePermissions(@PathVariable Long fileId) {
        try {
            List<Permission> permissions = permissionService.getFilePermissions(fileId);
            return Result.success(permissions);
        } catch (Exception e) {
            log.error("获取文件权限失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取文件夹权限列表
     */
    @GetMapping("/api/admin/permissions/folder/{folderId}")
    @ResponseBody
    public Result<List<Permission>> getFolderPermissions(@PathVariable Long folderId) {
        try {
            List<Permission> permissions = permissionService.getFolderPermissions(folderId);
            return Result.success(permissions);
        } catch (Exception e) {
            log.error("获取文件夹权限失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 批量授予权限
     */
    @PostMapping("/api/admin/permissions/batch-grant")
    @ResponseBody
    public Result<Object> batchGrantPermissions(@RequestParam List<Long> userIds,
                                               @RequestParam Permission.ResourceType resourceType,
                                               @RequestParam Long resourceId,
                                               @RequestParam List<Permission.PermissionType> permissionTypes) {
        try {
            Long grantedBy = StpUtil.getLoginIdAsLong();
            permissionService.batchGrantPermissions(userIds, resourceType, resourceId, permissionTypes, grantedBy);
            return Result.success("批量授予权限成功");
        } catch (Exception e) {
            log.error("批量授予权限失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 批量撤销权限
     */
    @DeleteMapping("/api/admin/permissions/batch-revoke")
    @ResponseBody
    public Result<Object> batchRevokePermissions(@RequestParam List<Long> permissionIds) {
        try {
            permissionService.batchRevokePermissions(permissionIds);
            return Result.success("批量撤销权限成功");
        } catch (Exception e) {
            log.error("批量撤销权限失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 删除用户所有权限
     */
    @DeleteMapping("/api/admin/permissions/user/{userId}")
    @ResponseBody
    public Result<Object> deleteUserPermissions(@PathVariable Long userId) {
        try {
            permissionService.deleteUserPermissions(userId);
            return Result.success("删除用户权限成功");
        } catch (Exception e) {
            log.error("删除用户权限失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 删除资源所有权限
     */
    @DeleteMapping("/api/admin/permissions/resource")
    @ResponseBody
    public Result<Object> deleteResourcePermissions(@RequestParam Permission.ResourceType resourceType,
                                                    @RequestParam Long resourceId) {
        try {
            permissionService.deleteResourcePermissions(resourceType, resourceId);
            return Result.success("删除资源权限成功");
        } catch (Exception e) {
            log.error("删除资源权限失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 复制权限
     */
    @PostMapping("/api/admin/permissions/copy")
    @ResponseBody
    public Result<Object> copyPermissions(@RequestParam Permission.ResourceType fromResourceType,
                                         @RequestParam Long fromResourceId,
                                         @RequestParam Permission.ResourceType toResourceType,
                                         @RequestParam Long toResourceId) {
        try {
            Long grantedBy = StpUtil.getLoginIdAsLong();
            permissionService.copyPermissions(fromResourceType, fromResourceId, toResourceType, toResourceId, grantedBy);
            return Result.success("权限复制成功");
        } catch (Exception e) {
            log.error("权限复制失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取权限统计信息
     */
    @GetMapping("/api/admin/permissions/stats")
    @ResponseBody
    public Result<Map<String, Object>> getPermissionStats() {
        try {
            Map<String, Object> stats = permissionService.getPermissionStats();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取权限统计失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 检查权限冲突
     */
    @GetMapping("/api/admin/permissions/check-conflict")
    @ResponseBody
    public Result<Boolean> checkPermissionConflict(@RequestParam Long userId,
                                                   @RequestParam Permission.ResourceType resourceType,
                                                   @RequestParam Long resourceId) {
        try {
            boolean hasConflict = permissionService.hasPermissionConflict(userId, resourceType, resourceId);
            return Result.success(hasConflict);
        } catch (Exception e) {
            log.error("检查权限冲突失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 修复权限冲突
     */
    @PostMapping("/api/admin/permissions/fix-conflict")
    @ResponseBody
    public Result<Object> fixPermissionConflicts(@RequestParam Long userId,
                                                 @RequestParam Permission.ResourceType resourceType,
                                                 @RequestParam Long resourceId) {
        try {
            Long grantedBy = StpUtil.getLoginIdAsLong();
            permissionService.fixPermissionConflicts(userId, resourceType, resourceId, grantedBy);
            return Result.success("权限冲突修复成功");
        } catch (Exception e) {
            log.error("修复权限冲突失败", e);
            return Result.error(e.getMessage());
        }
    }
}
