package com.example.v14.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.example.v14.common.Result;
import com.example.v14.entity.FileInfo;
import com.example.v14.entity.Folder;
import com.example.v14.entity.User;
import com.example.v14.service.FileService;
import com.example.v14.service.FolderService;
import com.example.v14.service.UserService;
import com.example.v14.controller.FileController.BatchOperationRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.ArrayList;

/**
 * 回收站控制器
 */
@Slf4j
@Controller
@RequiredArgsConstructor
public class RecycleController {

    private final FileService fileService;
    private final FolderService folderService;
    private final UserService userService;
    
    /**
     * 回收站页面
     */
    @GetMapping("/recycle")
    public String recyclePage(Model model) {
        Long userId = StpUtil.getLoginIdAsLong();
        log.info("回收站页面访问 - 用户ID: {}", userId);

        // 获取用户信息
        User user = userService.findById(userId);
        log.info("用户查询结果 - 用户ID: {}, 用户对象: {}", userId, user != null ? "存在" : "null");
        
        if (user == null) {
            log.error("用户查询失败 - 用户ID: {} 对应的用户不存在", userId);
            return "redirect:/login";
        }

        // 获取回收站文件列表
        List<FileInfo> deletedFiles = fileService.getDeletedFiles(userId, 1, 50);

        // 获取回收站文件夹列表
        List<Folder> deletedFolders = folderService.getDeletedFolders(userId, 1, 50);

        model.addAttribute("user", user);
        model.addAttribute("deletedFiles", deletedFiles);
        model.addAttribute("deletedFolders", deletedFolders);

        return "recycle";
    }
    
    /**
     * 获取回收站文件列表
     */
    @GetMapping("/api/recycle/files")
    @ResponseBody
    public Result<List<FileInfo>> getDeletedFiles(@RequestParam(defaultValue = "1") int page,
                                                 @RequestParam(defaultValue = "20") int size) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            List<FileInfo> files = fileService.getDeletedFiles(userId, page, size);
            return Result.success(files);
        } catch (Exception e) {
            log.error("获取回收站文件失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取回收站文件夹列表
     */
    @GetMapping("/api/recycle/folders")
    @ResponseBody
    public Result<List<Folder>> getDeletedFolders(@RequestParam(defaultValue = "1") int page,
                                                  @RequestParam(defaultValue = "20") int size) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            List<Folder> folders = folderService.getDeletedFolders(userId, page, size);
            return Result.success(folders);
        } catch (Exception e) {
            log.error("获取回收站文件夹失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取回收站所有项目（文件和文件夹）
     */
    @GetMapping("/api/recycle/items")
    @ResponseBody
    public Result<RecycleItems> getDeletedItems(@RequestParam(defaultValue = "1") int page,
                                               @RequestParam(defaultValue = "20") int size) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            List<FileInfo> files = fileService.getDeletedFiles(userId, page, size);
            List<Folder> folders = folderService.getDeletedFolders(userId, page, size);

            RecycleItems items = new RecycleItems();
            items.setFiles(files);
            items.setFolders(folders);

            return Result.success(items);
        } catch (Exception e) {
            log.error("获取回收站项目失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 恢复文件
     */
    @PostMapping("/api/recycle/{fileId}/restore")
    @ResponseBody
    public Result<Object> restoreFile(@PathVariable Long fileId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            fileService.restoreFile(fileId, userId);
            return Result.success("文件恢复成功");
        } catch (Exception e) {
            log.error("恢复文件失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 永久删除文件
     */
    @DeleteMapping("/api/recycle/{fileId}")
    @ResponseBody
    public Result<Object> permanentDeleteFile(@PathVariable Long fileId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            log.info("收到永久删除请求: fileId={}, userId={}", fileId, userId);

            // 删除前检查文件是否存在
            FileInfo beforeDelete = fileService.getFileInfo(fileId, userId);
            log.info("删除前文件状态: {}", beforeDelete != null ?
                    String.format("存在(ID=%d, 删除状态=%s)", beforeDelete.getId(), beforeDelete.getIsDeleted()) : "不存在");

            fileService.permanentDeleteFile(fileId, userId);

            // 删除后再次检查
            FileInfo afterDelete = fileService.getFileInfo(fileId, userId);
            log.info("删除后文件状态: {}", afterDelete != null ?
                    String.format("仍存在(ID=%d, 删除状态=%s)", afterDelete.getId(), afterDelete.getIsDeleted()) : "已删除");

            log.info("永久删除操作完成: fileId={}", fileId);
            return Result.success("文件已永久删除");
        } catch (Exception e) {
            log.error("永久删除文件失败: fileId={}", fileId, e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 批量恢复文件
     */
    @PostMapping("/api/recycle/batch/restore")
    @ResponseBody
    public Result<Object> batchRestoreFiles(@RequestBody BatchOperationRequest request) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();

            if (request.getFileIds() == null || request.getFileIds().isEmpty()) {
                return Result.badRequest("请选择要恢复的文件");
            }

            for (Long fileId : request.getFileIds()) {
                fileService.restoreFile(fileId, userId);
            }

            return Result.success("批量恢复成功");
        } catch (Exception e) {
            log.error("批量恢复文件失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 批量永久删除文件
     */
    @PostMapping("/api/recycle/batch/delete")
    @ResponseBody
    public Result<Object> batchPermanentDeleteFiles(@RequestBody BatchOperationRequest request) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();

            if (request.getFileIds() == null || request.getFileIds().isEmpty()) {
                return Result.badRequest("请选择要删除的文件");
            }

            int successCount = 0;
            int failCount = 0;

            for (Long fileId : request.getFileIds()) {
                try {
                    fileService.permanentDeleteFile(fileId, userId);
                    successCount++;
                } catch (Exception e) {
                    log.error("删除文件失败: fileId={}", fileId, e);
                    failCount++;
                }
            }

            String message = String.format("批量删除完成，成功删除 %d 个文件", successCount);
            if (failCount > 0) {
                message += String.format("，%d 个文件删除失败", failCount);
            }

            return Result.success(message);
        } catch (Exception e) {
            log.error("批量永久删除文件失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 清空回收站
     */
    @DeleteMapping("/api/recycle/clear")
    @ResponseBody
    public Result<Object> clearRecycle() {
        try {
            Long userId = StpUtil.getLoginIdAsLong();

            // 获取所有回收站文件和文件夹
            List<FileInfo> deletedFiles = fileService.getDeletedFiles(userId, 1, Integer.MAX_VALUE);
            List<Folder> deletedFolders = folderService.getDeletedFolders(userId, 1, Integer.MAX_VALUE);

            if (deletedFiles.isEmpty() && deletedFolders.isEmpty()) {
                return Result.success("回收站已经是空的");
            }

            int fileSuccessCount = 0;
            int fileFailCount = 0;
            int folderSuccessCount = 0;
            int folderFailCount = 0;

            // 永久删除所有文件
            for (FileInfo file : deletedFiles) {
                try {
                    fileService.permanentDeleteFile(file.getId(), userId);
                    fileSuccessCount++;
                } catch (Exception e) {
                    log.error("删除文件失败: fileId={}, fileName={}", file.getId(), file.getName(), e);
                    fileFailCount++;
                }
            }

            // 永久删除所有文件夹
            for (Folder folder : deletedFolders) {
                try {
                    folderService.permanentDeleteFolder(folder.getId(), userId);
                    folderSuccessCount++;
                } catch (Exception e) {
                    log.error("删除文件夹失败: folderId={}, folderName={}", folder.getId(), folder.getName(), e);
                    folderFailCount++;
                }
            }

            StringBuilder message = new StringBuilder("回收站清空完成");
            if (fileSuccessCount > 0 || folderSuccessCount > 0) {
                message.append("，成功删除");
                if (fileSuccessCount > 0) {
                    message.append(" ").append(fileSuccessCount).append(" 个文件");
                }
                if (folderSuccessCount > 0) {
                    if (fileSuccessCount > 0) {
                        message.append("和");
                    }
                    message.append(" ").append(folderSuccessCount).append(" 个文件夹");
                }
            }

            if (fileFailCount > 0 || folderFailCount > 0) {
                message.append("，");
                if (fileFailCount > 0) {
                    message.append(fileFailCount).append(" 个文件删除失败");
                }
                if (folderFailCount > 0) {
                    if (fileFailCount > 0) {
                        message.append("，");
                    }
                    message.append(folderFailCount).append(" 个文件夹删除失败");
                }
            }

            return Result.success(message.toString());
        } catch (Exception e) {
            log.error("清空回收站失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 恢复文件夹
     */
    @PostMapping("/api/recycle/folders/{folderId}/restore")
    @ResponseBody
    public Result<Object> restoreFolder(@PathVariable Long folderId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            log.info("收到文件夹恢复请求: folderId={}, userId={}", folderId, userId);

            folderService.restoreFolder(folderId, userId);

            log.info("文件夹恢复操作完成: folderId={}", folderId);
            return Result.success("文件夹已恢复");
        } catch (Exception e) {
            log.error("恢复文件夹失败: folderId={}", folderId, e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 永久删除文件夹
     */
    @DeleteMapping("/api/recycle/folders/{folderId}")
    @ResponseBody
    public Result<Object> permanentDeleteFolder(@PathVariable Long folderId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            log.info("收到文件夹永久删除请求: folderId={}, userId={}", folderId, userId);

            folderService.permanentDeleteFolder(folderId, userId);

            log.info("文件夹永久删除操作完成: folderId={}", folderId);
            return Result.success("文件夹已永久删除");
        } catch (Exception e) {
            log.error("永久删除文件夹失败: folderId={}", folderId, e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 回收站项目数据传输对象
     */
    public static class RecycleItems {
        private List<FileInfo> files;
        private List<Folder> folders;

        public List<FileInfo> getFiles() {
            return files;
        }

        public void setFiles(List<FileInfo> files) {
            this.files = files;
        }

        public List<Folder> getFolders() {
            return folders;
        }

        public void setFolders(List<Folder> folders) {
            this.folders = folders;
        }
    }
}
