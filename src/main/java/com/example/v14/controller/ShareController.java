package com.example.v14.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.example.v14.common.Result;
import com.example.v14.entity.Share;
import com.example.v14.entity.FileInfo;
import com.example.v14.entity.Folder;
import com.example.v14.entity.User;
import com.example.v14.service.FolderService;
import com.example.v14.dto.ShareWithFileInfo;
import com.example.v14.service.ShareService;
import com.example.v14.service.FileService;
import com.example.v14.service.UserService;
import com.example.v14.config.FileConfig;
import com.example.v14.mapper.FileMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 分享控制器
 */
@Slf4j
@Controller
@RequiredArgsConstructor
public class ShareController {

    private final ShareService shareService;
    private final FileService fileService;
    private final FolderService folderService;
    private final UserService userService;
    private final FileConfig fileConfig;
    private final FileMapper fileMapper;
    
    /**
     * 分享管理页面
     */
    @GetMapping("/shares")
    public String sharesPage(Model model) {
        Long userId = StpUtil.getLoginIdAsLong();
        log.info("分享页面访问 - 用户ID: {}", userId);

        // 获取用户信息
        User user = userService.findById(userId);
        log.info("用户查询结果 - 用户ID: {}, 用户对象: {}", userId, user != null ? "存在" : "null");
        
        if (user == null) {
            log.error("用户查询失败 - 用户ID: {} 对应的用户不存在", userId);
            return "redirect:/login";
        }

        // 获取用户分享列表（包含文件信息）
        List<ShareWithFileInfo> shares = shareService.getUserSharesWithFileInfo(userId, 1, 50);

        // 获取分享统计
        ShareService.ShareStats stats = shareService.getShareStats(userId);

        model.addAttribute("user", user);
        model.addAttribute("shares", shares);
        model.addAttribute("stats", stats);

        return "shares";
    }
    
    /**
     * 分享访问页面
     */
    @GetMapping("/share/{shareCode}")
    public String shareAccessPage(@PathVariable String shareCode, Model model, HttpServletRequest request) {
        Share share = shareService.getShareByCode(shareCode);
        
        if (share == null || !share.isAvailable()) {
            model.addAttribute("error", "分享链接无效或已过期");
            return "share-error";
        }
        
        // 检查是否已经通过密码验证
        String sessionKey = "share_verified_" + shareCode;
        Boolean isVerified = (Boolean) request.getSession().getAttribute(sessionKey);
        boolean needPassword = share.hasPassword() && !Boolean.TRUE.equals(isVerified);
        
        model.addAttribute("share", share);
        model.addAttribute("needPassword", needPassword);
        
        // 如果不需要密码或已验证通过，获取文件/文件夹信息
        if (!needPassword) {
            if (share.getFileId() != null) {
                // 文件分享
                FileInfo fileInfo = fileMapper.findById(share.getFileId());
                if (fileInfo != null && !Boolean.TRUE.equals(fileInfo.getIsDeleted())) {
                    model.addAttribute("fileName", fileInfo.getOriginalName());
                    model.addAttribute("fileSize", formatFileSize(fileInfo.getFileSize()));
                    model.addAttribute("fileType", getFileTypeDisplay(fileInfo.getFileType()));
                    model.addAttribute("contentType", "file");
                } else {
                    model.addAttribute("error", "分享的文件不存在或已删除");
                    return "share-error";
                }
            } else if (share.getFolderId() != null) {
                // 文件夹分享
                Folder folder = folderService.findById(share.getFolderId());
                if (folder != null && !folder.isDeleted()) {
                    // 计算文件夹总大小
                    long folderSize = folderService.calculateFolderSize(share.getFolderId(), folder.getUserId());
                    model.addAttribute("fileName", folder.getName());
                    model.addAttribute("fileSize", formatFileSize(folderSize));
                    model.addAttribute("fileType", "文件夹");
                    model.addAttribute("contentType", "folder");
                } else {
                    model.addAttribute("error", "分享的文件夹不存在或已删除");
                    return "share-error";
                }
            }
        }
        
        return "share-access";
    }
    
    /**
     * 创建分享链接
     */
    @PostMapping("/api/shares")
    @ResponseBody
    public Result<Share> createShare(@RequestParam(required = false) Long fileId,
                                   @RequestParam(required = false) Long folderId,
                                   @RequestParam Share.ShareType shareType,
                                   @RequestParam(required = false) String password,
                                   @RequestParam(required = false) String expireTime,
                                   @RequestParam(required = false) Integer downloadLimit) {
        try {
            // 验证参数
            if ((fileId == null && folderId == null) || (fileId != null && folderId != null)) {
                return Result.badRequest("必须指定文件ID或文件夹ID中的一个");
            }
            
            Long userId = StpUtil.getLoginIdAsLong();
            
            LocalDateTime expire = null;
            if (expireTime != null && !expireTime.isEmpty()) {
                expire = LocalDateTime.parse(expireTime);
            }
            
            Share share;
            if (fileId != null) {
                share = shareService.createFileShare(fileId, userId, shareType, password, expire, downloadLimit);
            } else {
                share = shareService.createFolderShare(folderId, userId, shareType, password, expire, downloadLimit);
            }
            
            return Result.success("分享链接创建成功", share);
        } catch (Exception e) {
            log.error("创建分享链接失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 验证分享访问密码
     */
    @PostMapping("/api/shares/{shareCode}/verify")
    @ResponseBody
    public Result<Object> verifySharePassword(@PathVariable String shareCode,
                                            @RequestParam(required = false) String password,
                                            HttpServletRequest request) {
        try {
            boolean valid = shareService.validateShareAccess(shareCode, password);
            if (valid) {
                // 验证成功，将状态保存到Session
                String sessionKey = "share_verified_" + shareCode;
                request.getSession().setAttribute(sessionKey, true);
                log.info("分享密码验证成功，已保存到Session: {}", sessionKey);
                
                return Result.success("验证成功");
            } else {
                return Result.error("密码错误或分享已失效");
            }
        } catch (Exception e) {
            log.error("验证分享密码失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取分享内容信息（用于预览）
     */
    @GetMapping("/api/shares/{shareCode}/info")
    @ResponseBody
    public Result<Object> getShareInfo(@PathVariable String shareCode,
                                      @RequestParam(required = false) String password,
                                      HttpServletRequest request) {
        try {
            // 检查Session中的验证状态
            String sessionKey = "share_verified_" + shareCode;
            Boolean isVerified = (Boolean) request.getSession().getAttribute(sessionKey);
            
            if (!Boolean.TRUE.equals(isVerified) && !shareService.validateShareAccess(shareCode, password)) {
                return Result.unauthorized("无权访问");
            }

            Share share = shareService.getShareByCode(shareCode);

            // 构建返回信息
            ShareInfoResponse response = new ShareInfoResponse();
            response.setShare(share);
            
            if (share.getFileId() != null) {
                // 文件分享
                FileInfo fileInfo = fileMapper.findById(share.getFileId());
                if (fileInfo == null || Boolean.TRUE.equals(fileInfo.getIsDeleted())) {
                    return Result.error("分享的文件不存在或已删除");
                }
                response.setFileInfo(fileInfo);
            } else if (share.getFolderId() != null) {
                // 文件夹分享 - 获取文件夹信息和内容列表
                Folder folder = folderService.findById(share.getFolderId());
                if (folder == null || folder.isDeleted()) {
                    return Result.error("分享的文件夹不存在或已删除");
                }
                response.setFolder(folder);
                
                // 获取文件夹内容用于预览
                FolderPreviewInfo folderPreview = new FolderPreviewInfo();
                folderPreview.setFolder(folder);
                
                // 获取文件夹内的文件列表
                List<FileInfo> files = fileMapper.findByUserAndFolder(folder.getUserId(), folder.getId());
                folderPreview.setFiles(files.stream().filter(f -> !Boolean.TRUE.equals(f.getIsDeleted())).collect(java.util.stream.Collectors.toList()));
                
                // 获取子文件夹列表
                List<Folder> subFolders = folderService.getUserFolders(folder.getUserId(), folder.getId());
                folderPreview.setSubFolders(subFolders);
                
                response.setFolderPreview(folderPreview);
            }

            return Result.success(response);
        } catch (Exception e) {
            log.error("获取分享信息失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 下载分享文件
     */
    @GetMapping("/api/shares/{shareCode}/download")
    public ResponseEntity<Resource> downloadSharedFile(@PathVariable String shareCode,
                                                      @RequestParam(required = false) String password,
                                                      HttpServletRequest request) {
        try {
            // 检查Session中的验证状态
            String sessionKey = "share_verified_" + shareCode;
            Boolean isVerified = (Boolean) request.getSession().getAttribute(sessionKey);
            
            if (!Boolean.TRUE.equals(isVerified) && !shareService.validateShareAccess(shareCode, password)) {
                return ResponseEntity.status(401).build();
            }

            Share share = shareService.getShareByCode(shareCode);
            if (!share.allowDownload()) {
                return ResponseEntity.status(403).build();
            }

            if (share.getFileId() != null) {
                // 文件分享下载
                FileInfo fileInfo = fileMapper.findById(share.getFileId());
                if (fileInfo == null || Boolean.TRUE.equals(fileInfo.getIsDeleted())) {
                    log.warn("分享的文件不存在或已删除: fileId={}", share.getFileId());
                    return ResponseEntity.notFound().build();
                }

                // 构建文件路径
                String filePath = fileConfig.getUploadPath() + fileInfo.getFilePath();
                File file = new File(filePath);

                if (!file.exists()) {
                    log.warn("分享的文件物理文件不存在: {}", filePath);
                    return ResponseEntity.notFound().build();
                }

                // 记录下载
                shareService.recordShareDownload(shareCode);

                // 设置响应头
                HttpHeaders headers = new HttpHeaders();
                headers.add(HttpHeaders.CONTENT_DISPOSITION,
                           "attachment; filename=\"" + encodeFileName(fileInfo.getName()) + "\"");
                headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);

                Resource resource = new FileSystemResource(file);

                log.info("分享文件下载: shareCode={}, fileName={}, fileSize={}",
                        shareCode, fileInfo.getName(), file.length());

                return ResponseEntity.ok()
                        .headers(headers)
                        .contentLength(file.length())
                        .body(resource);
                        
            } else if (share.getFolderId() != null) {
                // 文件夹分享下载
                Folder folder = folderService.findById(share.getFolderId());
                if (folder == null || folder.isDeleted()) {
                    log.warn("分享的文件夹不存在或已删除: folderId={}", share.getFolderId());
                    return ResponseEntity.notFound().build();
                }

                // 创建ZIP文件
                File zipFile = folderService.downloadFolder(share.getFolderId(), folder.getUserId());
                
                if (!zipFile.exists()) {
                    return ResponseEntity.notFound().build();
                }

                // 记录下载
                shareService.recordShareDownload(shareCode);

                // 设置响应头
                HttpHeaders headers = new HttpHeaders();
                headers.add(HttpHeaders.CONTENT_DISPOSITION,
                           "attachment; filename=\"" + encodeFileName(folder.getName() + ".zip") + "\"");
                headers.add(HttpHeaders.CONTENT_TYPE, "application/zip");

                Resource resource = new FileSystemResource(zipFile);

                log.info("分享文件夹下载: shareCode={}, folderName={}, zipSize={}",
                        shareCode, folder.getName(), zipFile.length());

                return ResponseEntity.ok()
                        .headers(headers)
                        .contentLength(zipFile.length())
                        .body(resource);
            }
            
            return ResponseEntity.notFound().build();

        } catch (Exception e) {
            log.error("下载分享文件失败: shareCode={}", shareCode, e);
            return ResponseEntity.status(500).build();
        }
    }
    
    /**
     * 更新分享设置
     */
    @PutMapping("/api/shares/{shareId}")
    @ResponseBody
    public Result<Object> updateShare(@PathVariable Long shareId,
                                    @RequestParam(required = false) String password,
                                    @RequestParam(required = false) String expireTime,
                                    @RequestParam(required = false) Integer downloadLimit) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();

            LocalDateTime expire = null;
            if (expireTime != null && !expireTime.isEmpty()) {
                expire = LocalDateTime.parse(expireTime);
            }

            shareService.updateShare(shareId, userId, password, expire, downloadLimit);
            return Result.success("分享设置更新成功");
        } catch (Exception e) {
            log.error("更新分享设置失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 取消分享
     */
    @DeleteMapping("/api/shares/{shareId}")
    @ResponseBody
    public Result<Object> cancelShare(@PathVariable Long shareId) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            shareService.cancelShare(shareId, userId);
            return Result.success("分享已取消");
        } catch (Exception e) {
            log.error("取消分享失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取用户分享列表
     */
    @GetMapping("/api/shares")
    @ResponseBody
    public Result<List<Share>> getUserShares(@RequestParam(defaultValue = "1") int page,
                                           @RequestParam(defaultValue = "20") int size) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            List<Share> shares = shareService.getUserShares(userId, page, size);
            return Result.success(shares);
        } catch (Exception e) {
            log.error("获取分享列表失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取分享统计
     */
    @GetMapping("/api/shares/stats")
    @ResponseBody
    public Result<ShareService.ShareStats> getShareStats() {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            ShareService.ShareStats stats = shareService.getShareStats(userId);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取分享统计失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量操作分享
     */
    @PostMapping("/api/shares/batch")
    @ResponseBody
    public Result<Object> batchOperation(@RequestParam String operation,
                                       @RequestParam List<Long> shareIds) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();

            log.info("收到批量分享操作请求: 操作类型={}, 用户ID={}, 分享ID列表={}", operation, userId, shareIds);

            switch (operation.toLowerCase()) {
                case "cancel":
                    shareService.batchCancelShares(shareIds, userId);
                    return Result.success("批量取消分享成功");

                case "enable":
                    shareService.batchEnableShares(shareIds, userId);
                    return Result.success("批量启用分享成功");

                case "delete":
                    shareService.batchDeleteShares(shareIds, userId);
                    return Result.success("批量删除分享成功");

                default:
                    return Result.badRequest("不支持的操作类型: " + operation);
            }

        } catch (Exception e) {
            log.error("批量操作分享失败: operation={}, shareIds={}", operation, shareIds, e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 编码文件名
     */
    private String encodeFileName(String fileName) {
        try {
            return URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException e) {
            return fileName;
        }
    }

    /**
     * 分享信息响应类
     */
    public static class ShareInfoResponse {
        private Share share;
        private FileInfo fileInfo;
        private Folder folder;
        private FolderPreviewInfo folderPreview;

        public Share getShare() {
            return share;
        }

        public void setShare(Share share) {
            this.share = share;
        }

        public FileInfo getFileInfo() {
            return fileInfo;
        }

        public void setFileInfo(FileInfo fileInfo) {
            this.fileInfo = fileInfo;
        }

        public Folder getFolder() {
            return folder;
        }

        public void setFolder(Folder folder) {
            this.folder = folder;
        }

        public FolderPreviewInfo getFolderPreview() {
            return folderPreview;
        }

        public void setFolderPreview(FolderPreviewInfo folderPreview) {
            this.folderPreview = folderPreview;
        }
    }
    
    /**
     * 文件夹预览信息类
     */
    public static class FolderPreviewInfo {
        private Folder folder;
        private List<FileInfo> files;
        private List<Folder> subFolders;

        public Folder getFolder() {
            return folder;
        }

        public void setFolder(Folder folder) {
            this.folder = folder;
        }

        public List<FileInfo> getFiles() {
            return files;
        }

        public void setFiles(List<FileInfo> files) {
            this.files = files;
        }

        public List<Folder> getSubFolders() {
            return subFolders;
        }

        public void setSubFolders(List<Folder> subFolders) {
            this.subFolders = subFolders;
        }
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(Long fileSize) {
        if (fileSize == null || fileSize <= 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        double size = fileSize.doubleValue();
        int unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }
    
    /**
     * 获取文件类型显示名称
     */
    private String getFileTypeDisplay(String fileType) {
        if (fileType == null || fileType.isEmpty()) {
            return "未知类型";
        }
        
        // 根据文件类型返回友好的显示名称
        switch (fileType.toLowerCase()) {
            case "jpg":
            case "jpeg":
            case "png":
            case "gif":
            case "bmp":
            case "webp":
                return "图片文件";
            case "txt":
                return "文本文件";
            case "pdf":
                return "PDF文档";
            case "doc":
            case "docx":
                return "Word文档";
            case "xls":
            case "xlsx":
                return "Excel表格";
            case "ppt":
            case "pptx":
                return "PowerPoint演示";
            case "zip":
            case "rar":
            case "7z":
                return "压缩文件";
            case "mp4":
            case "avi":
            case "mkv":
                return "视频文件";
            case "mp3":
            case "wav":
            case "flac":
                return "音频文件";
            default:
                return fileType.toUpperCase() + "文件";
        }
    }
}
