package com.example.v14.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import com.example.v14.common.Result;
import com.example.v14.service.AnalyticsService;
import com.example.v14.service.LoggingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 数据分析控制器
 */
@Slf4j
@Controller
@RequiredArgsConstructor
@SaCheckRole("ADMIN")
public class AnalyticsController {
    
    private final AnalyticsService analyticsService;
    private final LoggingService loggingService;
    
    /**
     * 分析统计页面
     */
    @GetMapping("/admin/analytics")
    public String analyticsPage(Model model) {
        // 默认显示最近30天的数据
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(30);
        
        // 获取仪表板数据
        Map<String, Object> dashboardData = analyticsService.getDashboardData(startTime, endTime);
        model.addAttribute("dashboardData", dashboardData);
        
        return "admin/analytics";
    }
    
    /**
     * 获取下载统计概览
     */
    @GetMapping("/api/admin/analytics/download-overview")
    @ResponseBody
    public Result<Map<String, Object>> getDownloadOverview(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(30);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            
            Map<String, Object> overview = analyticsService.getDownloadOverview(startTime, endTime);
            return Result.success(overview);
        } catch (Exception e) {
            log.error("获取下载统计概览失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取热门下载文件
     */
    @GetMapping("/api/admin/analytics/popular-files")
    @ResponseBody
    public Result<Object> getPopularFiles(@RequestParam(defaultValue = "10") int limit) {
        try {
            return Result.success(analyticsService.getPopularFiles(limit));
        } catch (Exception e) {
            log.error("获取热门文件失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取活跃下载用户
     */
    @GetMapping("/api/admin/analytics/active-users")
    @ResponseBody
    public Result<Object> getActiveUsers(@RequestParam(defaultValue = "10") int limit) {
        try {
            return Result.success(analyticsService.getActiveDownloadUsers(limit));
        } catch (Exception e) {
            log.error("获取活跃用户失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取下载趋势数据
     */
    @GetMapping("/api/admin/analytics/download-trends")
    @ResponseBody
    public Result<Map<String, Object>> getDownloadTrends(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(30);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            
            Map<String, Object> trends = analyticsService.getDownloadTrends(startTime, endTime);
            return Result.success(trends);
        } catch (Exception e) {
            log.error("获取下载趋势失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取访问统计
     */
    @GetMapping("/api/admin/analytics/access-stats")
    @ResponseBody
    public Result<Map<String, Object>> getAccessStats(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(30);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            
            Map<String, Object> stats = analyticsService.getAccessStats(startTime, endTime);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取访问统计失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取用户活跃度统计
     */
    @GetMapping("/api/admin/analytics/user-activity")
    @ResponseBody
    public Result<Map<String, Object>> getUserActivity(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(30);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            
            Map<String, Object> activity = analyticsService.getUserActivityStats(startTime, endTime);
            return Result.success(activity);
        } catch (Exception e) {
            log.error("获取用户活跃度统计失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取存储统计
     */
    @GetMapping("/api/admin/analytics/storage-stats")
    @ResponseBody
    public Result<Map<String, Object>> getStorageStats() {
        try {
            Map<String, Object> stats = analyticsService.getStorageStats();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取存储统计失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取用户下载统计
     */
    @GetMapping("/api/admin/analytics/user-downloads/{userId}")
    @ResponseBody
    public Result<Map<String, Object>> getUserDownloadStats(
            @PathVariable Long userId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(30);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            
            Map<String, Object> stats = analyticsService.getUserDownloadStats(userId, startTime, endTime);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取用户下载统计失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取文件下载统计
     */
    @GetMapping("/api/admin/analytics/file-downloads/{fileId}")
    @ResponseBody
    public Result<Map<String, Object>> getFileDownloadStats(
            @PathVariable Long fileId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(30);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            
            Map<String, Object> stats = analyticsService.getFileDownloadStats(fileId, startTime, endTime);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取文件下载统计失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取用户行为统计
     */
    @GetMapping("/api/admin/analytics/user-actions/{userId}")
    @ResponseBody
    public Result<Map<String, Object>> getUserActionStats(
            @PathVariable Long userId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(30);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            
            Map<String, Object> stats = loggingService.getUserActionStats(userId, startTime, endTime);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取用户行为统计失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取IP访问统计
     */
    @GetMapping("/api/admin/analytics/ip-access")
    @ResponseBody
    public Result<Map<String, Object>> getIpAccessStats(
            @RequestParam String ipAddress,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(30);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            
            Map<String, Object> stats = loggingService.getIpAccessStats(ipAddress, startTime, endTime);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取IP访问统计失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取系统行为统计
     */
    @GetMapping("/api/admin/analytics/system-actions")
    @ResponseBody
    public Result<Map<String, Object>> getSystemActionStats(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(30);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            
            Map<String, Object> stats = loggingService.getSystemActionStats(startTime, endTime);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取系统行为统计失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 生成统计报告
     */
    @GetMapping("/api/admin/analytics/report")
    @ResponseBody
    public Result<Map<String, Object>> generateReport(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(30);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            
            Map<String, Object> report = analyticsService.generateReport(startTime, endTime);
            return Result.success(report);
        } catch (Exception e) {
            log.error("生成统计报告失败", e);
            return Result.error(e.getMessage());
        }
    }
}
