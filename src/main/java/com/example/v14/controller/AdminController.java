package com.example.v14.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import com.example.v14.common.Result;
import com.example.v14.entity.User;
import com.example.v14.entity.FileInfo;
import com.example.v14.entity.IpBlacklist;
import com.example.v14.entity.IpWhitelist;
import com.example.v14.service.AdminService;
import com.example.v14.service.IpManagementService;
import com.example.v14.service.FileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 管理员控制器
 */
@Slf4j
@Controller
@RequiredArgsConstructor
@SaCheckRole("ADMIN")
public class AdminController {

    private final AdminService adminService;
    private final IpManagementService ipManagementService;
    private final FileService fileService;
    private final com.example.v14.service.IpLocationService ipLocationService;
    
    /**
     * 管理员面板首页
     */
    @GetMapping("/admin")
    public String adminDashboard(Model model) {
        // 获取系统统计信息
        AdminService.SystemStats stats = adminService.getSystemStats();
        model.addAttribute("stats", stats);
        
        // 获取最近文件
        List<FileInfo> recentFiles = adminService.getRecentFiles(10);
        model.addAttribute("recentFiles", recentFiles);
        
        // 获取存储使用排行
        List<User> storageRanking = adminService.getStorageUsageRanking(10);
        model.addAttribute("storageRanking", storageRanking);
        
        return "admin/dashboard";
    }
    
    /**
     * 用户管理页面
     */
    @GetMapping("/admin/users")
    public String userManagement(@RequestParam(defaultValue = "1") int page,
                                @RequestParam(defaultValue = "20") int size,
                                @RequestParam(required = false) String username,
                                @RequestParam(required = false) String email,
                                @RequestParam(required = false) User.UserRole role,
                                @RequestParam(required = false) User.UserStatus status,
                                Model model) {
        
        List<User> users = adminService.getUserList(username, email, role, status, page, size);
        long totalUsers = adminService.countUsers(username, email, role, status);
        
        model.addAttribute("users", users);
        model.addAttribute("totalUsers", totalUsers);
        model.addAttribute("currentPage", page);
        model.addAttribute("pageSize", size);
        model.addAttribute("totalPages", (totalUsers + size - 1) / size);
        
        // 搜索条件
        model.addAttribute("searchUsername", username);
        model.addAttribute("searchEmail", email);
        model.addAttribute("searchRole", role);
        model.addAttribute("searchStatus", status);
        
        return "admin/users";
    }
    
    /**
     * 系统统计页面
     */
    @GetMapping("/admin/stats")
    public String systemStats(Model model) {
        // 系统统计
        AdminService.SystemStats stats = adminService.getSystemStats();
        model.addAttribute("stats", stats);
        
        // 文件统计
        Map<String, Object> fileStats = adminService.getFileStats();
        model.addAttribute("fileStats", fileStats);
        
        return "admin/stats";
    }
    
    /**
     * 系统设置页面
     */
    @GetMapping("/admin/system")
    public String systemSettings(Model model) {
        // 获取系统基本信息
        AdminService.SystemStats stats = adminService.getSystemStats();
        model.addAttribute("stats", stats);
        
        return "admin/system";
    }
    
    /**
     * 获取系统统计数据（API）
     */
    @GetMapping("/api/admin/stats")
    @ResponseBody
    public Result<AdminService.SystemStats> getSystemStats() {
        try {
            AdminService.SystemStats stats = adminService.getSystemStats();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取系统统计失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取用户列表（API）
     */
    @GetMapping("/api/admin/users")
    @ResponseBody
    public Result<Map<String, Object>> getUserList(@RequestParam(defaultValue = "1") int page,
                                                   @RequestParam(defaultValue = "20") int size,
                                                   @RequestParam(required = false) String username,
                                                   @RequestParam(required = false) String email,
                                                   @RequestParam(required = false) User.UserRole role,
                                                   @RequestParam(required = false) User.UserStatus status) {
        try {
            List<User> users = adminService.getUserList(username, email, role, status, page, size);
            long totalUsers = adminService.countUsers(username, email, role, status);
            
            Map<String, Object> result = Map.of(
                "users", users,
                "total", totalUsers,
                "page", page,
                "size", size
            );
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 更新用户状态
     */
    @PutMapping("/api/admin/users/{userId}/status")
    @ResponseBody
    public Result<Object> updateUserStatus(@PathVariable Long userId,
                                          @RequestParam User.UserStatus status) {
        try {
            adminService.updateUserStatus(userId, status);
            return Result.success("用户状态更新成功");
        } catch (Exception e) {
            log.error("更新用户状态失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取单个用户信息
     */
    @GetMapping("/api/admin/users/{userId}")
    @ResponseBody
    public Result<User> getUserById(@PathVariable Long userId) {
        try {
            User user = adminService.getUserById(userId);
            if (user == null) {
                return Result.notFound("用户不存在");
            }
            // 清除敏感信息
            user.setPassword(null);
            return Result.success(user);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 创建用户
     */
    @PostMapping("/api/admin/users")
    @ResponseBody
    public Result<Object> createUser(@RequestParam String username,
                                    @RequestParam String password,
                                    @RequestParam(required = false) String email,
                                    @RequestParam(required = false) String nickname,
                                    @RequestParam User.UserRole role,
                                    @RequestParam(required = false) Long storageLimit) {
        try {
            adminService.createUser(username, password, email, nickname, role, storageLimit);
            return Result.success("用户创建成功");
        } catch (Exception e) {
            log.error("创建用户失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/api/admin/users/{userId}")
    @ResponseBody
    public Result<Object> updateUser(@PathVariable Long userId,
                                    @RequestParam(required = false) String username,
                                    @RequestParam(required = false) String email,
                                    @RequestParam(required = false) String nickname,
                                    @RequestParam(required = false) User.UserRole role,
                                    @RequestParam(required = false) User.UserStatus status,
                                    @RequestParam(required = false) Long storageLimit) {
        try {
            adminService.updateUserInfo(userId, username, email, nickname, role, status, storageLimit);
            return Result.success("用户更新成功");
        } catch (Exception e) {
            log.error("更新用户失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量更新用户状态
     */
    @PutMapping("/api/admin/users/batch-status")
    @ResponseBody
    public Result<Object> batchUpdateUserStatus(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> userIds = (List<Long>) request.get("userIds");
            String statusStr = (String) request.get("status");
            User.UserStatus status = User.UserStatus.valueOf(statusStr);

            for (Long userId : userIds) {
                adminService.updateUserStatus(userId, status);
            }

            return Result.success("批量更新状态成功");
        } catch (Exception e) {
            log.error("批量更新用户状态失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量删除用户
     */
    @DeleteMapping("/api/admin/users/batch-delete")
    @ResponseBody
    public Result<Object> batchDeleteUsers(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> userIds = (List<Long>) request.get("userIds");

            for (Long userId : userIds) {
                adminService.deleteUser(userId);
            }

            return Result.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除用户失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 更新用户角色
     */
    @PutMapping("/api/admin/users/{userId}/role")
    @ResponseBody
    public Result<Object> updateUserRole(@PathVariable Long userId, 
                                        @RequestParam User.UserRole role) {
        try {
            adminService.updateUserRole(userId, role);
            return Result.success("用户角色更新成功");
        } catch (Exception e) {
            log.error("更新用户角色失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 更新用户存储限制
     */
    @PutMapping("/api/admin/users/{userId}/storage")
    @ResponseBody
    public Result<Object> updateUserStorageLimit(@PathVariable Long userId,
                                                @RequestParam Long storageLimit,
                                                @RequestParam Long singleFileLimit) {
        try {
            adminService.updateUserStorageLimit(userId, storageLimit, singleFileLimit);
            return Result.success("用户存储限制更新成功");
        } catch (Exception e) {
            log.error("更新用户存储限制失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 删除用户
     */
    @DeleteMapping("/api/admin/users/{userId}")
    @ResponseBody
    public Result<Object> deleteUser(@PathVariable Long userId) {
        try {
            adminService.deleteUser(userId);
            return Result.success("用户删除成功");
        } catch (Exception e) {
            log.error("删除用户失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取文件统计
     */
    @GetMapping("/api/admin/file-stats")
    @ResponseBody
    public Result<Map<String, Object>> getFileStats() {
        try {
            Map<String, Object> stats = adminService.getFileStats();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取文件统计失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 清理删除的文件
     */
    @PostMapping("/api/admin/cleanup")
    @ResponseBody
    public Result<Object> cleanupDeletedFiles(@RequestParam(defaultValue = "30") int days) {
        try {
            int count = adminService.cleanupDeletedFiles(days);
            return Result.success("清理完成，共清理 " + count + " 个文件");
        } catch (Exception e) {
            log.error("清理删除文件失败", e);
            return Result.error(e.getMessage());
        }
    }

    // ==================== IP管理相关接口 ====================

    /**
     * IP管理页面
     */
    @GetMapping("/admin/ip-management")
    public String ipManagementPage(Model model) {
        // 获取IP统计信息
        IpManagementService.IpStats stats = ipManagementService.getIpStats();
        model.addAttribute("ipStats", stats);

        return "admin/ip-management";
    }

    /**
     * 获取IP黑名单列表
     */
    @GetMapping("/api/admin/ip/blacklist")
    @ResponseBody
    public Result<Map<String, Object>> getBlacklistPage(
            @RequestParam(required = false) String ipAddress,
            @RequestParam(required = false) String reason,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<IpBlacklist> blacklists = ipManagementService.getBlacklistPage(ipAddress, reason, null, null, page, size);
            long total = ipManagementService.countBlacklist(ipAddress, reason, null, null);

            Map<String, Object> result = Map.of(
                "blacklists", blacklists,
                "total", total,
                "page", page,
                "size", size
            );

            return Result.success(result);
        } catch (Exception e) {
            log.error("获取IP黑名单失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取IP白名单列表
     */
    @GetMapping("/api/admin/ip/whitelist")
    @ResponseBody
    public Result<Map<String, Object>> getWhitelistPage(
            @RequestParam(required = false) String ipAddress,
            @RequestParam(required = false) String description,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<IpWhitelist> whitelists = ipManagementService.getWhitelistPage(ipAddress, description, null, null, page, size);
            long total = ipManagementService.countWhitelist(ipAddress, description, null, null);

            Map<String, Object> result = Map.of(
                "whitelists", whitelists,
                "total", total,
                "page", page,
                "size", size
            );

            return Result.success(result);
        } catch (Exception e) {
            log.error("获取IP白名单失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 添加IP到黑名单
     */
    @PostMapping("/api/admin/ip/blacklist")
    @ResponseBody
    public Result<Object> addToBlacklist(@RequestParam String ipAddress,
                                        @RequestParam String reason) {
        try {
            Long adminId = StpUtil.getLoginIdAsLong();
            ipManagementService.addToBlacklist(ipAddress, reason, adminId, null);
            return Result.success("IP已添加到黑名单");
        } catch (Exception e) {
            log.error("添加IP到黑名单失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 添加IP到白名单
     */
    @PostMapping("/api/admin/ip/whitelist")
    @ResponseBody
    public Result<Object> addToWhitelist(@RequestParam String ipAddress,
                                        @RequestParam String description) {
        try {
            Long adminId = StpUtil.getLoginIdAsLong();
            ipManagementService.addToWhitelist(ipAddress, description, adminId, null);
            return Result.success("IP已添加到白名单");
        } catch (Exception e) {
            log.error("添加IP到白名单失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 从黑名单删除IP
     */
    @DeleteMapping("/api/admin/ip/blacklist/{id}")
    @ResponseBody
    public Result<Object> removeFromBlacklist(@PathVariable Long id) {
        try {
            // 这里需要先查询IP地址，然后删除
            // 为简化，直接通过ID删除
            ipManagementService.batchDeleteBlacklist(List.of(id));
            return Result.success("IP已从黑名单移除");
        } catch (Exception e) {
            log.error("从黑名单移除IP失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 从白名单删除IP
     */
    @DeleteMapping("/api/admin/ip/whitelist/{id}")
    @ResponseBody
    public Result<Object> removeFromWhitelist(@PathVariable Long id) {
        try {
            ipManagementService.batchDeleteWhitelist(List.of(id));
            return Result.success("IP已从白名单移除");
        } catch (Exception e) {
            log.error("从白名单移除IP失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量删除黑名单
     */
    @DeleteMapping("/api/admin/ip/blacklist/batch")
    @ResponseBody
    public Result<Object> batchDeleteBlacklist(@RequestBody List<Long> ids) {
        try {
            ipManagementService.batchDeleteBlacklist(ids);
            return Result.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除黑名单失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量删除白名单
     */
    @DeleteMapping("/api/admin/ip/whitelist/batch")
    @ResponseBody
    public Result<Object> batchDeleteWhitelist(@RequestBody List<Long> ids) {
        try {
            ipManagementService.batchDeleteWhitelist(ids);
            return Result.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除白名单失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取IP统计信息
     */
    @GetMapping("/api/admin/ip/stats")
    @ResponseBody
    public Result<IpManagementService.IpStats> getIpStats() {
        try {
            IpManagementService.IpStats stats = ipManagementService.getIpStats();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取IP统计信息失败", e);
            return Result.error(e.getMessage());
        }
    }

    // ==================== 统计分析相关接口 ====================

    /**
     * 获取概览统计
     */
    @GetMapping("/api/admin/stats/overview")
    @ResponseBody
    public Result<Map<String, Object>> getOverviewStats(@RequestParam(defaultValue = "30") int days) {
        try {
            Map<String, Object> stats = adminService.getOverviewStats(days);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取概览统计失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取活跃度统计
     */
    @GetMapping("/api/admin/stats/activity")
    @ResponseBody
    public Result<Map<String, Object>> getActivityStats(@RequestParam(defaultValue = "30") int days) {
        try {
            Map<String, Object> stats = adminService.getActivityStats(days);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取活跃度统计失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取存储统计
     */
    @GetMapping("/api/admin/stats/storage")
    @ResponseBody
    public Result<Map<String, Object>> getStorageStats() {
        try {
            Map<String, Object> stats = adminService.getStorageStats();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取存储统计失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取排行榜数据
     */
    @GetMapping("/api/admin/stats/ranking")
    @ResponseBody
    public Result<List<Map<String, Object>>> getRankingData(@RequestParam String type,
                                                           @RequestParam(defaultValue = "10") int limit) {
        try {
            List<Map<String, Object>> ranking = adminService.getRankingData(type, limit);
            return Result.success(ranking);
        } catch (Exception e) {
            log.error("获取排行榜数据失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取图表数据
     */
    @GetMapping("/api/admin/stats/chart")
    @ResponseBody
    public Result<Map<String, Object>> getChartData(@RequestParam(defaultValue = "30") int days) {
        try {
            Map<String, Object> chartData = adminService.getChartData(days);
            return Result.success(chartData);
        } catch (Exception e) {
            log.error("获取图表数据失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取文件类型统计
     */
    @GetMapping("/api/admin/stats/file-types")
    @ResponseBody
    public Result<Map<String, Object>> getFileTypeStats() {
        try {
            Map<String, Object> stats = adminService.getFileTypeStats();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取文件类型统计失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 导出统计报告
     */
    @GetMapping("/api/admin/stats/export")
    public void exportStatsReport(@RequestParam(defaultValue = "30") int days,
                                 HttpServletResponse response) {
        try {
            response.setContentType("text/csv");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition",
                "attachment; filename=stats-report-" + days + "days.csv");

            adminService.exportStatsReport(days, response.getOutputStream());
        } catch (Exception e) {
            log.error("导出统计报告失败", e);
        }
    }

    /**
     * 用户文件管理页面
     */
    @GetMapping("/admin/users/{userId}/files")
    public String userFilesManagement(@PathVariable Long userId, Model model) {
        try {
            // 获取用户信息
            User user = adminService.getUserById(userId);
            if (user == null) {
                model.addAttribute("error", "用户不存在");
                return "error";
            }
            
            // 获取用户文件列表
            List<FileInfo> userFiles = adminService.getUserFiles(userId);
            
            model.addAttribute("user", user);
            model.addAttribute("userFiles", userFiles);
            model.addAttribute("userId", userId);
            
            return "admin/user-files";
        } catch (Exception e) {
            log.error("获取用户文件失败", e);
            model.addAttribute("error", "获取用户文件失败: " + e.getMessage());
            return "error";
        }
    }

    /**
     * 获取用户文件列表API
     */
    @GetMapping("/api/admin/users/{userId}/files")
    @ResponseBody
    public Result<Map<String, Object>> getUserFiles(@PathVariable Long userId,
                                                   @RequestParam(defaultValue = "1") int page,
                                                   @RequestParam(defaultValue = "20") int size,
                                                   @RequestParam(required = false) String fileName) {
        try {
            List<FileInfo> files = adminService.getUserFiles(userId, fileName, page, size);
            long totalFiles = adminService.countUserFiles(userId, fileName);
            
            Map<String, Object> result = Map.of(
                "files", files,
                "total", totalFiles,
                "page", page,
                "size", size
            );
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取用户文件列表失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除用户文件
     */
    @DeleteMapping("/api/admin/users/{userId}/files/{fileId}")
    @ResponseBody
    public Result<Object> deleteUserFile(@PathVariable Long userId, @PathVariable Long fileId) {
        try {
            adminService.deleteUserFile(userId, fileId);
            return Result.success("文件删除成功");
        } catch (Exception e) {
            log.error("删除用户文件失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 管理员删除文件（简化API）
     */
    @DeleteMapping("/api/admin/files/{fileId}")
    @ResponseBody
    public Result<Object> adminDeleteFile(@PathVariable Long fileId) {
        try {
            // 获取文件信息
            FileInfo fileInfo = fileService.getFileById(fileId);
            if (fileInfo == null) {
                return Result.error("文件不存在");
            }
            
            // 管理员可以删除任何用户的文件
            adminService.deleteUserFile(fileInfo.getUserId(), fileId);
            return Result.success("文件删除成功");
        } catch (Exception e) {
            log.error("管理员删除文件失败", e);
            return Result.error(e.getMessage() != null ? e.getMessage() : "删除失败");
        }
    }
    
    // =================== IP地理位置管理API ===================
    
    /**
     * 查询IP地理位置
     */
    @GetMapping("/api/admin/ip/location/{ip}")
    @ResponseBody
    public Result<String> getIpLocation(@PathVariable String ip) {
        try {
            String location = ipLocationService.getIpLocation(ip);
            return Result.success(location);
        } catch (Exception e) {
            log.error("查询IP地理位置失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 批量查询IP地理位置
     */
    @PostMapping("/api/admin/ip/location/batch")
    @ResponseBody
    public Result<Object> batchQueryLocations(@RequestBody List<String> ipAddresses) {
        try {
            ipLocationService.batchQueryLocations(ipAddresses);
            return Result.success("批量查询已启动");
        } catch (Exception e) {
            log.error("批量查询IP地理位置失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 清理地理位置缓存
     */
    @PostMapping("/api/admin/ip/location/cache/clear")
    @ResponseBody
    public Result<Object> clearLocationCache() {
        try {
            ipLocationService.clearLocationCache();
            return Result.success("地理位置缓存清理成功");
        } catch (Exception e) {
            log.error("清理地理位置缓存失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取地理位置缓存统计
     */
    @GetMapping("/api/admin/ip/location/cache/stats")
    @ResponseBody
    public Result<Map<String, Object>> getLocationCacheStats() {
        try {
            Map<String, Object> stats = ipLocationService.getLocationCacheStats();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取地理位置缓存统计失败", e);
            return Result.error(e.getMessage());
        }
    }
}
