package com.example.v14.config;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotRoleException;
import com.example.v14.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 全局异常处理器
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理未登录异常
     */
    @ExceptionHandler(NotLoginException.class)
    public ModelAndView handleNotLoginException(NotLoginException e, HttpServletRequest request) {
        log.warn("用户未登录访问受保护页面: {}", request.getRequestURI());
        
        // 如果是API请求，返回JSON响应
        if (isAjaxRequest(request)) {
            ModelAndView modelAndView = new ModelAndView("redirect:/login");
            return modelAndView;
        }
        
        // 普通页面请求，重定向到登录页
        return new ModelAndView("redirect:/login");
    }

    /**
     * 处理权限不足异常
     */
    @ExceptionHandler(NotRoleException.class)
    public ModelAndView handleNotRoleException(NotRoleException e, HttpServletRequest request) {
        log.warn("用户权限不足访问管理页面: {}, 需要权限: {}", request.getRequestURI(), e.getRole());
        
        // 如果是API请求，返回JSON响应
        if (isAjaxRequest(request)) {
            ModelAndView modelAndView = new ModelAndView("redirect:/login");
            return modelAndView;
        }
        
        // 普通页面请求，重定向到登录页
        return new ModelAndView("redirect:/login");
    }

    /**
     * 处理其他运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ModelAndView handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        log.error("系统运行时异常: {}", request.getRequestURI(), e);
        
        // 如果是模板渲染时的用户对象为null异常，重定向到登录页
        if (e.getMessage() != null && e.getMessage().contains("user") && e.getMessage().contains("null")) {
            log.warn("检测到用户对象为null的模板渲染异常，重定向到登录页");
            return new ModelAndView("redirect:/login");
        }
        
        // 其他异常显示错误页面
        ModelAndView modelAndView = new ModelAndView("error");
        modelAndView.addObject("error", "系统内部错误，请稍后重试");
        modelAndView.addObject("message", e.getMessage());
        return modelAndView;
    }

    /**
     * 处理MyBatis系统异常
     */
    @ExceptionHandler(org.mybatis.spring.MyBatisSystemException.class)
    public Object handleMyBatisException(org.mybatis.spring.MyBatisSystemException e, HttpServletRequest request) {
        log.error("MyBatis系统异常: {}", request.getRequestURI(), e);
        
        if (isAjaxRequest(request)) {
            return Result.error("数据库操作失败，请稍后重试");
        }
        
        ModelAndView modelAndView = new ModelAndView("error");
        modelAndView.addObject("error", "数据库操作失败");
        modelAndView.addObject("message", "系统遇到数据库问题，请联系管理员");
        return modelAndView;
    }

    /**
     * 处理数据访问异常
     */
    @ExceptionHandler(org.springframework.dao.DataAccessException.class)
    public Object handleDataAccessException(org.springframework.dao.DataAccessException e, HttpServletRequest request) {
        log.error("数据访问异常: {}", request.getRequestURI(), e);
        
        if (isAjaxRequest(request)) {
            return Result.error("数据访问失败，请检查数据库连接");
        }
        
        ModelAndView modelAndView = new ModelAndView("error");
        modelAndView.addObject("error", "数据访问失败");
        modelAndView.addObject("message", "无法连接到数据库，请稍后重试");
        return modelAndView;
    }

    /**
     * 判断是否为Ajax请求
     */
    private boolean isAjaxRequest(HttpServletRequest request) {
        String xRequestedWith = request.getHeader("X-Requested-With");
        return "XMLHttpRequest".equals(xRequestedWith) || 
               request.getRequestURI().startsWith("/api/");
    }
}