package com.example.v14.config;

import cn.dev33.satoken.stp.StpInterface;
import cn.dev33.satoken.stp.StpUtil;
import com.example.v14.entity.User;
import com.example.v14.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Sa-Token权限验证接口实现
 * 用于获取用户的角色和权限信息
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class StpInterfaceImpl implements StpInterface {
    
    private final UserService userService;
    
    /**
     * 返回一个账号所拥有的权限码集合
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        // 本系统暂时不使用细粒度权限，返回空列表
        return new ArrayList<>();
    }
    
    /**
     * 返回一个账号所拥有的角色标识集合
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        List<String> roles = new ArrayList<>();
        
        try {
            // 首先尝试从Session中获取角色信息
            if (StpUtil.isLogin()) {
                String roleFromSession = (String) StpUtil.getSession().get("role");
                if (roleFromSession != null) {
                    roles.add(roleFromSession);
                    log.debug("从Session获取用户角色: userId={}, role={}", loginId, roleFromSession);
                    return roles;
                }
            }
            
            // 如果Session中没有，从数据库查询
            Long userId = Long.valueOf(loginId.toString());
            User user = userService.findById(userId);
            
            if (user != null && user.getRole() != null) {
                String roleName = user.getRole().name();
                roles.add(roleName);
                
                // 同时更新Session中的角色信息
                if (StpUtil.isLogin()) {
                    StpUtil.getSession().set("role", roleName);
                    StpUtil.getSession().set("user", user);
                }
                
                log.debug("从数据库获取用户角色: userId={}, role={}", userId, roleName);
            } else {
                log.warn("用户不存在或角色为空: userId={}", userId);
            }
            
        } catch (Exception e) {
            log.error("获取用户角色失败: userId={}, error={}", loginId, e.getMessage());
        }
        
        return roles;
    }
}
