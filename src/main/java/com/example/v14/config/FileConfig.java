package com.example.v14.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import jakarta.annotation.PostConstruct;
import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 文件配置类
 * 支持相对路径、绝对路径、用户目录路径和环境变量
 */
@Slf4j
@Configuration
public class FileConfig implements WebMvcConfigurer {

    @Value("${file.upload.path}")
    private String uploadPathConfig;

    @Value("${file.temp.path}")
    private String tempPathConfig;

    private String resolvedUploadPath;
    private String resolvedTempPath;
    
    /**
     * 初始化文件目录
     */
    @PostConstruct
    public void init() {
        // 解析和创建上传目录
        resolvedUploadPath = resolvePath(uploadPathConfig, "uploads");
        createDirectoryIfNotExists(resolvedUploadPath, "上传目录");

        // 解析和创建临时目录
        resolvedTempPath = resolvePath(tempPathConfig, "temp");
        createDirectoryIfNotExists(resolvedTempPath, "临时目录");

        log.info("文件存储配置初始化完成:");
        log.info("  上传目录: {} -> {}", uploadPathConfig, resolvedUploadPath);
        log.info("  临时目录: {} -> {}", tempPathConfig, resolvedTempPath);
    }

    /**
     * 解析路径配置，支持相对路径、绝对路径、用户目录路径
     */
    private String resolvePath(String configPath, String defaultSubDir) {
        if (configPath == null || configPath.trim().isEmpty()) {
            configPath = "./" + defaultSubDir + "/";
        }

        String resolvedPath;

        if (configPath.startsWith("~/")) {
            // 用户目录路径：~/uploads/ -> /home/<USER>/uploads/
            String userHome = System.getProperty("user.home");
            resolvedPath = userHome + configPath.substring(1);
        } else if (configPath.startsWith("./") || configPath.startsWith(".\\")) {
            // 相对路径：./uploads/ -> /project/path/uploads/
            String currentDir = System.getProperty("user.dir");
            resolvedPath = Paths.get(currentDir, configPath.substring(2)).toString();
        } else if (isAbsolutePath(configPath)) {
            // 绝对路径：直接使用
            resolvedPath = configPath;
        } else {
            // 其他情况，当作相对路径处理
            String currentDir = System.getProperty("user.dir");
            resolvedPath = Paths.get(currentDir, configPath).toString();
        }

        // 确保路径以分隔符结尾
        if (!resolvedPath.endsWith(File.separator) && !resolvedPath.endsWith("/")) {
            resolvedPath += File.separator;
        }

        return resolvedPath;
    }

    /**
     * 判断是否为绝对路径
     */
    private boolean isAbsolutePath(String path) {
        Path pathObj = Paths.get(path);
        return pathObj.isAbsolute();
    }

    /**
     * 创建目录（如果不存在）
     */
    private void createDirectoryIfNotExists(String path, String description) {
        try {
            File dir = new File(path);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (created) {
                    log.info("{}创建成功: {}", description, path);
                } else {
                    log.warn("{}创建失败: {}", description, path);
                }
            } else {
                log.info("{}已存在: {}", description, path);
            }

            // 检查目录权限
            if (!dir.canRead() || !dir.canWrite()) {
                log.warn("{}权限不足: {} (可读: {}, 可写: {})",
                        description, path, dir.canRead(), dir.canWrite());
            }
        } catch (Exception e) {
            log.error("创建{}失败: {}", description, path, e);
            throw new RuntimeException("创建" + description + "失败: " + path, e);
        }
    }
    
    /**
     * 配置静态资源映射
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 映射上传文件访问路径
        registry.addResourceHandler("/uploads/**")
                .addResourceLocations("file:" + resolvedUploadPath);

        // 映射静态资源
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
    }

    /**
     * 获取解析后的上传路径
     */
    public String getUploadPath() {
        return resolvedUploadPath;
    }

    /**
     * 获取解析后的临时路径
     */
    public String getTempPath() {
        return resolvedTempPath;
    }

    /**
     * 获取原始配置路径（用于调试）
     */
    public String getUploadPathConfig() {
        return uploadPathConfig;
    }

    public String getTempPathConfig() {
        return tempPathConfig;
    }
}
