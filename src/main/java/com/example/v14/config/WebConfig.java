package com.example.v14.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.stp.StpUtil;
import com.example.v14.interceptor.IpAccessInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 */
@Configuration
@RequiredArgsConstructor
public class WebConfig implements WebMvcConfigurer {
    
    private final IpAccessInterceptor ipAccessInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加IP访问拦截器
        registry.addInterceptor(ipAccessInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(
                    "/static/**",
                    "/favicon.ico",
                    "/error",
                    "/api/admin/ip/**" // 排除IP管理接口，避免管理员被锁定
                )
                .order(1); // 设置优先级，IP拦截器优先级最高
    }
}
