package com.example.v14.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token配置类
 */
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {
    
    /**
     * 注册Sa-Token拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册Sa-Token拦截器，校验规则为StpUtil.checkLogin()登录校验
        registry.addInterceptor(new SaInterceptor(handle -> {
            // 登录校验 -- 拦截所有路由，并排除登录相关的路由用于开放访问
            SaRouter.match("/**")
                    .notMatch("/", "/login", "/register", "/api/auth/**", "/static/**", "/css/**", "/js/**", "/images/**", "/favicon.ico")
                    .notMatch("/share/**", "/api/shares/**") // 分享链接和分享API不需要登录
                    .notMatch("/error") // 错误页面不需要登录
                    .check(r -> StpUtil.checkLogin());
            
            // 管理员权限校验
            SaRouter.match("/admin/**", "/api/admin/**")
                    .check(r -> StpUtil.checkRole("ADMIN"));
                    
        })).addPathPatterns("/**");
    }
}
