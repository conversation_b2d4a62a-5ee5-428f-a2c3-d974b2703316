package com.example.v14.config;

import com.example.v14.service.ChunkUploadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 定时任务配置
 */
@Slf4j
@Configuration
@EnableScheduling
@RequiredArgsConstructor
public class ScheduleConfig {
    
    private final ChunkUploadService chunkUploadService;
    
    /**
     * 清理过期的分片上传会话
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3600000毫秒
    public void cleanupExpiredUploads() {
        try {
            log.info("开始清理过期的分片上传会话");
            chunkUploadService.cleanupExpiredUploads();
            log.info("清理过期的分片上传会话完成");
        } catch (Exception e) {
            log.error("清理过期的分片上传会话失败", e);
        }
    }
}
