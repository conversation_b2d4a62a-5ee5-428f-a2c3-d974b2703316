package com.example.v14.util;

import org.springframework.stereotype.Component;

/**
 * 文件图标工具类
 */
@Component("fileIconUtil")
public class FileIconUtil {
    
    /**
     * 根据文件名获取对应的图标
     */
    public String getIcon(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "📄";
        }
        
        String extension = getFileExtension(fileName).toLowerCase();
        
        // 图片文件
        if (extension.matches("(jpg|jpeg|png|gif|bmp|webp|svg|ico)")) {
            return "🖼️";
        }
        
        // 视频文件
        if (extension.matches("(mp4|avi|mov|wmv|flv|mkv|webm|m4v)")) {
            return "🎬";
        }
        
        // 音频文件
        if (extension.matches("(mp3|wav|flac|aac|ogg|wma|m4a)")) {
            return "🎵";
        }
        
        // 文档文件
        if (extension.matches("(doc|docx)")) {
            return "📝";
        }
        
        if (extension.matches("(xls|xlsx)")) {
            return "📊";
        }
        
        if (extension.matches("(ppt|pptx)")) {
            return "📋";
        }
        
        if (extension.equals("pdf")) {
            return "📕";
        }
        
        // 文本文件
        if (extension.matches("(txt|md|readme)")) {
            return "📃";
        }
        
        // 代码文件
        if (extension.matches("(java|js|html|css|php|py|cpp|c|h|xml|json|yml|yaml)")) {
            return "💻";
        }
        
        // 压缩文件
        if (extension.matches("(zip|rar|7z|tar|gz|bz2)")) {
            return "🗜️";
        }
        
        // 可执行文件
        if (extension.matches("(exe|msi|dmg|pkg|deb|rpm)")) {
            return "⚙️";
        }
        
        // 默认文件图标
        return "📄";
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }
}
