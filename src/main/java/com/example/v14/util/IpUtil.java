package com.example.v14.util;

import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.regex.Pattern;

/**
 * IP地址工具类
 * 支持单IP和CIDR网段的匹配、验证等功能
 */
@Slf4j
public class IpUtil {
    
    private static final Pattern IP_PATTERN = Pattern.compile(
        "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
    );
    
    // IPv6地址匹配模式
    private static final Pattern IPV6_PATTERN = Pattern.compile(
        "^\\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))(%.+)?\\s*$"
    );
    
    private static final Pattern CIDR_PATTERN = Pattern.compile(
        "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)/(?:[0-9]|[1-2][0-9]|3[0-2])$"
    );
    
    /**
     * 验证IP地址格式是否正确（支持IPv4和IPv6）
     * @param ip IP地址
     * @return 是否有效
     */
    public static boolean isValidIp(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        
        String trimmedIp = ip.trim();
        
        // 检查IPv4格式
        if (IP_PATTERN.matcher(trimmedIp).matches()) {
            return true;
        }
        
        // 检查IPv6格式
        if (IPV6_PATTERN.matcher(trimmedIp).matches()) {
            return true;
        }
        
        // 特殊处理常见的本地地址
        if ("localhost".equalsIgnoreCase(trimmedIp) || 
            "::1".equals(trimmedIp) || 
            "0:0:0:0:0:0:0:1".equals(trimmedIp)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 验证IPv4地址格式
     * @param ip IP地址
     * @return 是否有效的IPv4地址
     */
    public static boolean isValidIpv4(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        return IP_PATTERN.matcher(ip.trim()).matches();
    }
    
    /**
     * 验证IPv6地址格式
     * @param ip IP地址
     * @return 是否有效的IPv6地址
     */
    public static boolean isValidIpv6(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        
        String trimmedIp = ip.trim();
        
        // 检查IPv6格式
        if (IPV6_PATTERN.matcher(trimmedIp).matches()) {
            return true;
        }
        
        // 特殊处理常见的IPv6本地地址
        if ("::1".equals(trimmedIp) || "0:0:0:0:0:0:0:1".equals(trimmedIp)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 验证CIDR格式是否正确
     * @param cidr CIDR格式的网段
     * @return 是否有效
     */
    public static boolean isValidCidr(String cidr) {
        if (cidr == null || cidr.trim().isEmpty()) {
            return false;
        }
        return CIDR_PATTERN.matcher(cidr.trim()).matches();
    }
    
    /**
     * 验证IP地址或CIDR格式
     * @param ipOrCidr IP地址或CIDR网段
     * @return 是否有效
     */
    public static boolean isValidIpOrCidr(String ipOrCidr) {
        return isValidIp(ipOrCidr) || isValidCidr(ipOrCidr);
    }
    
    /**
     * 判断IP地址是否在指定的CIDR网段内
     * @param ip 待检查的IP地址
     * @param cidr CIDR网段
     * @return 是否在网段内
     */
    public static boolean isIpInCidr(String ip, String cidr) {
        try {
            if (!isValidIp(ip) || !isValidCidr(cidr)) {
                return false;
            }
            
            String[] cidrParts = cidr.split("/");
            String networkIp = cidrParts[0];
            int prefixLength = Integer.parseInt(cidrParts[1]);
            
            long ipLong = ipToLong(ip);
            long networkLong = ipToLong(networkIp);
            
            // 计算子网掩码
            long mask = 0xFFFFFFFFL << (32 - prefixLength);
            
            // 检查IP是否在网段内
            return (ipLong & mask) == (networkLong & mask);
            
        } catch (Exception e) {
            log.error("检查IP是否在CIDR网段内时出错: ip={}, cidr={}", ip, cidr, e);
            return false;
        }
    }
    
    /**
     * 检查IP是否匹配规则（支持单IP和CIDR，兼容IPv6）
     * @param ip 待检查的IP
     * @param rule 规则（单IP或CIDR）
     * @return 是否匹配
     */
    public static boolean matchesRule(String ip, String rule) {
        if (!isValidIp(ip) || rule == null || rule.trim().isEmpty()) {
            return false;
        }
        
        rule = rule.trim();
        
        // 处理IPv6地址的直接匹配
        if (isValidIpv6(ip) && isValidIpv6(rule)) {
            return normalizeIpv6(ip).equals(normalizeIpv6(rule));
        }
        
        // 如果是单IP，直接比较
        if (isValidIp(rule)) {
            return ip.equals(rule);
        }
        
        // 如果是IPv4 CIDR，检查是否在网段内
        if (isValidCidr(rule) && isValidIpv4(ip)) {
            return isIpInCidr(ip, rule);
        }
        
        return false;
    }
    
    /**
     * 标准化IPv6地址格式
     * @param ipv6 IPv6地址
     * @return 标准化后的IPv6地址
     */
    private static String normalizeIpv6(String ipv6) {
        if (ipv6 == null) return null;
        
        // 处理特殊的本地地址
        if ("::1".equals(ipv6) || "0:0:0:0:0:0:0:1".equals(ipv6)) {
            return "::1";
        }
        
        // 简单处理，实际生产环境中可能需要更复杂的标准化
        return ipv6.toLowerCase();
    }
    
    /**
     * 将IP地址转换为长整型
     * @param ip IP地址
     * @return 长整型值
     */
    public static long ipToLong(String ip) {
        String[] parts = ip.split("\\.");
        long result = 0;
        for (int i = 0; i < 4; i++) {
            result |= (Long.parseLong(parts[i]) << (24 - i * 8));
        }
        return result & 0xFFFFFFFFL;
    }
    
    /**
     * 将长整型转换为IP地址
     * @param ipLong 长整型IP
     * @return IP地址字符串
     */
    public static String longToIp(long ipLong) {
        return String.format("%d.%d.%d.%d",
            (ipLong >> 24) & 0xFF,
            (ipLong >> 16) & 0xFF,
            (ipLong >> 8) & 0xFF,
            ipLong & 0xFF);
    }
    
    /**
     * 判断是否为私有IP地址
     * @param ip IP地址
     * @return 是否为私有IP
     */
    public static boolean isPrivateIp(String ip) {
        if (!isValidIp(ip)) {
            return false;
        }
        
        long ipLong = ipToLong(ip);
        
        // 10.0.0.0 - ************** (10.0.0.0/8)
        if ((ipLong >= ipToLong("10.0.0.0")) && (ipLong <= ipToLong("**************"))) {
            return true;
        }
        
        // ********** - ************** (**********/12)
        if ((ipLong >= ipToLong("**********")) && (ipLong <= ipToLong("**************"))) {
            return true;
        }
        
        // *********** - *************** (***********/16)
        if ((ipLong >= ipToLong("***********")) && (ipLong <= ipToLong("***************"))) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 判断是否为本地IP地址（支持IPv4和IPv6）
     * @param ip IP地址
     * @return 是否为本地IP
     */
    public static boolean isLocalIp(String ip) {
        if (!isValidIp(ip)) {
            return false;
        }
        
        String trimmedIp = ip.trim();
        
        // IPv4本地地址
        if ("127.0.0.1".equals(trimmedIp) || "0.0.0.0".equals(trimmedIp) || "localhost".equalsIgnoreCase(trimmedIp)) {
            return true;
        }
        
        // IPv6本地地址
        if ("::1".equals(trimmedIp) || "0:0:0:0:0:0:0:1".equals(trimmedIp)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取CIDR网段的第一个IP
     * @param cidr CIDR网段
     * @return 网络地址
     */
    public static String getNetworkAddress(String cidr) {
        if (!isValidCidr(cidr)) {
            return null;
        }
        
        try {
            String[] parts = cidr.split("/");
            String ip = parts[0];
            int prefixLength = Integer.parseInt(parts[1]);
            
            long ipLong = ipToLong(ip);
            long mask = 0xFFFFFFFFL << (32 - prefixLength);
            long networkLong = ipLong & mask;
            
            return longToIp(networkLong);
        } catch (Exception e) {
            log.error("获取网络地址失败: cidr={}", cidr, e);
            return null;
        }
    }
    
    /**
     * 获取CIDR网段的最后一个IP
     * @param cidr CIDR网段
     * @return 广播地址
     */
    public static String getBroadcastAddress(String cidr) {
        if (!isValidCidr(cidr)) {
            return null;
        }
        
        try {
            String[] parts = cidr.split("/");
            String ip = parts[0];
            int prefixLength = Integer.parseInt(parts[1]);
            
            long ipLong = ipToLong(ip);
            long mask = 0xFFFFFFFFL << (32 - prefixLength);
            long networkLong = ipLong & mask;
            long broadcastLong = networkLong | (~mask & 0xFFFFFFFFL);
            
            return longToIp(broadcastLong);
        } catch (Exception e) {
            log.error("获取广播地址失败: cidr={}", cidr, e);
            return null;
        }
    }
    
    /**
     * 计算CIDR网段包含的IP数量
     * @param cidr CIDR网段
     * @return IP数量
     */
    public static long getCidrIpCount(String cidr) {
        if (!isValidCidr(cidr)) {
            return 0;
        }
        
        try {
            String[] parts = cidr.split("/");
            int prefixLength = Integer.parseInt(parts[1]);
            return 1L << (32 - prefixLength);
        } catch (Exception e) {
            log.error("计算CIDR IP数量失败: cidr={}", cidr, e);
            return 0;
        }
    }
    
    /**
     * 获取客户端真实IP地址（支持IPv4和IPv6）
     * @param xForwardedFor X-Forwarded-For头
     * @param xRealIp X-Real-IP头
     * @param remoteAddr 远程地址
     * @return 真实IP地址
     */
    public static String getRealIp(String xForwardedFor, String xRealIp, String remoteAddr) {
        String ip = null;
        
        // 1. 从X-Forwarded-For头获取
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            // X-Forwarded-For可能包含多个IP，取第一个
            ip = xForwardedFor.split(",")[0].trim();
        }
        
        // 2. 从X-Real-IP头获取
        if ((ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) && 
            xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            ip = xRealIp;
        }
        
        // 3. 使用远程地址
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = remoteAddr;
        }
        
        // 验证IP格式
        if (ip != null && isValidIp(ip)) {
            return ip;
        }
        
        // 如果都获取不到有效IP，返回IPv4默认值
        return "127.0.0.1";
    }
    
    /**
     * 判断IP类型
     * @param ipOrCidr IP地址或CIDR
     * @return single或range
     */
    public static String getIpType(String ipOrCidr) {
        if (isValidIp(ipOrCidr)) {
            return "single";
        } else if (isValidCidr(ipOrCidr)) {
            return "range";
        }
        return "unknown";
    }
}