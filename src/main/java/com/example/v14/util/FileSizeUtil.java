package com.example.v14.util;

import org.springframework.stereotype.Component;

/**
 * 文件大小格式化工具类
 */
@Component("fileSizeUtil")
public class FileSizeUtil {
    
    /**
     * 格式化文件大小
     */
    public String format(Long bytes) {
        if (bytes == null || bytes == 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        double size = bytes.doubleValue();
        int unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }
    
    /**
     * 格式化文件大小（简化版本，去掉小数点后的0）
     */
    public String formatSimple(Long bytes) {
        if (bytes == null || bytes == 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        double size = bytes.doubleValue();
        int unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        // 如果是整数，不显示小数点
        if (size == Math.floor(size)) {
            return String.format("%.0f %s", size, units[unitIndex]);
        } else {
            return String.format("%.2f %s", size, units[unitIndex]);
        }
    }
}
