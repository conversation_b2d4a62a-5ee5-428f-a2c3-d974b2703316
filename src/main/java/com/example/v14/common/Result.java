package com.example.v14.common;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 统一响应结果类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Result<T> {
    
    /**
     * 响应码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 成功响应码
     */
    public static final Integer SUCCESS_CODE = 200;
    
    /**
     * 失败响应码
     */
    public static final Integer ERROR_CODE = 500;
    
    /**
     * 未授权响应码
     */
    public static final Integer UNAUTHORIZED_CODE = 401;
    
    /**
     * 禁止访问响应码
     */
    public static final Integer FORBIDDEN_CODE = 403;
    
    /**
     * 资源不存在响应码
     */
    public static final Integer NOT_FOUND_CODE = 404;
    
    /**
     * 参数错误响应码
     */
    public static final Integer BAD_REQUEST_CODE = 400;
    
    /**
     * 成功响应
     */
    public static <T> Result<T> success() {
        return new Result<T>(SUCCESS_CODE, "操作成功", null);
    }
    
    /**
     * 成功响应带数据
     */
    public static <T> Result<T> success(T data) {
        return new Result<T>(SUCCESS_CODE, "操作成功", data);
    }
    
    /**
     * 成功响应带消息和数据
     */
    public static <T> Result<T> success(String message, T data) {
        return new Result<T>(SUCCESS_CODE, message, data);
    }
    
    /**
     * 失败响应
     */
    public static <T> Result<T> error() {
        return new Result<T>(ERROR_CODE, "操作失败", null);
    }
    
    /**
     * 失败响应带消息
     */
    public static <T> Result<T> error(String message) {
        return new Result<T>(ERROR_CODE, message, null);
    }
    
    /**
     * 失败响应带码和消息
     */
    public static <T> Result<T> error(Integer code, String message) {
        return new Result<T>(code, message, null);
    }
    
    /**
     * 未授权响应
     */
    public static <T> Result<T> unauthorized() {
        return new Result<T>(UNAUTHORIZED_CODE, "未授权访问", null);
    }
    
    /**
     * 未授权响应带消息
     */
    public static <T> Result<T> unauthorized(String message) {
        return new Result<T>(UNAUTHORIZED_CODE, message, null);
    }
    
    /**
     * 禁止访问响应
     */
    public static <T> Result<T> forbidden() {
        return new Result<T>(FORBIDDEN_CODE, "禁止访问", null);
    }
    
    /**
     * 禁止访问响应带消息
     */
    public static <T> Result<T> forbidden(String message) {
        return new Result<T>(FORBIDDEN_CODE, message, null);
    }
    
    /**
     * 资源不存在响应
     */
    public static <T> Result<T> notFound() {
        return new Result<T>(NOT_FOUND_CODE, "资源不存在", null);
    }
    
    /**
     * 资源不存在响应带消息
     */
    public static <T> Result<T> notFound(String message) {
        return new Result<T>(NOT_FOUND_CODE, message, null);
    }
    
    /**
     * 参数错误响应
     */
    public static <T> Result<T> badRequest() {
        return new Result<T>(BAD_REQUEST_CODE, "参数错误", null);
    }
    
    /**
     * 参数错误响应带消息
     */
    public static <T> Result<T> badRequest(String message) {
        return new Result<T>(BAD_REQUEST_CODE, message, null);
    }
    
    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return SUCCESS_CODE.equals(this.code);
    }
}
