# Application Configuration
spring.application.name=v14

# Server Configuration
server.port=8080
server.servlet.context-path=/
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# Database Configuration
spring.datasource.url=******************************************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# HikariCP Connection Pool Configuration
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.pool-name=HikariPool-v14
spring.datasource.hikari.max-lifetime=1200000
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.validation-timeout=3000
spring.datasource.hikari.leak-detection-threshold=60000

# Transaction Configuration
spring.transaction.default-timeout=30
spring.transaction.rollback-on-commit-failure=true

# MyBatis Configuration
mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.type-aliases-package=com.example.v14.entity
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# Thymeleaf Configuration
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
spring.thymeleaf.mode=HTML
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.cache=false

# File Upload Configuration
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=5GB
spring.servlet.multipart.max-request-size=5GB
spring.servlet.multipart.file-size-threshold=2KB

# Sa-Token Configuration
sa-token.token-name=satoken
sa-token.timeout=2592000
sa-token.activity-timeout=-1
sa-token.is-concurrent=true
sa-token.is-share=true
sa-token.token-style=uuid
sa-token.is-log=false

# Redis Configuration (for Sa-Token)
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=
spring.data.redis.database=0
spring.data.redis.timeout=10000ms
spring.data.redis.lettuce.pool.max-active=8
spring.data.redis.lettuce.pool.max-wait=-1ms
spring.data.redis.lettuce.pool.max-idle=8
spring.data.redis.lettuce.pool.min-idle=0

# File Storage Configuration
# ????????????????
# ????????????
# ?????FILE_UPLOAD_PATH, FILE_TEMP_PATH
# ???
#   ????: ./uploads/, ./temp/
#   ????: /var/lib/llb/uploads/, C:/data/uploads/
#   ????: ~/llb/uploads/, ~/llb/temp/
file.upload.path=${FILE_UPLOAD_PATH:./uploads/}
file.temp.path=${FILE_TEMP_PATH:./temp/}

# Logging Configuration
logging.level.com.example.v14.mapper=DEBUG
logging.level.org.springframework.web=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# Jackson Configuration (for LocalDateTime serialization)
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
