<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件分享 - LLB文件管理系统</title>
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <link rel="stylesheet" th:href="@{/static/css/share-access.css}">
</head>
<body>
    <div class="share-container">
        <div class="share-header">
            <h1>LLB文件分享</h1>
            <p>安全、快速的文件分享服务</p>
        </div>
        
        <div class="share-content">
            <!-- 需要密码验证 -->
            <div th:if="${needPassword}" class="password-form" id="passwordForm">
                <div class="form-card">
                    <div class="form-header">
                        <div class="share-icon">🔒</div>
                        <h2>请输入访问密码</h2>
                        <p>此分享受密码保护</p>
                    </div>
                    
                    <form id="verifyForm">
                        <div class="form-group">
                            <input type="password" id="sharePassword" placeholder="请输入访问密码" required>
                        </div>
                        <button type="submit" class="btn btn-primary btn-full">
                            <span class="btn-text">访问文件</span>
                            <span class="btn-loading" style="display: none;">验证中...</span>
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- 文件信息展示 -->
            <div th:unless="${needPassword}" class="file-info" id="fileInfo">
                <div class="file-card">
                    <div class="file-header">
                        <div class="file-icon" th:text="${contentType == 'folder' ? '📁' : '📄'}">📄</div>
                        <div class="file-details">
                            <h2 class="file-name" th:text="${fileName}">文件名称</h2>
                            <div class="file-meta">
                                <span class="file-size" th:text="${fileSize}">文件大小</span>
                                <span class="file-type" th:text="${fileType}">文件类型</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="share-info">
                        <div class="share-stats">
                            <div class="stat-item">
                                <span class="stat-label">分享时间</span>
                                <span class="stat-value" th:text="${#temporals.format(share.createdTime, 'yyyy-MM-dd HH:mm')}">分享时间</span>
                            </div>
                            <div class="stat-item" th:if="${share.expireTime != null}">
                                <span class="stat-label">过期时间</span>
                                <span class="stat-value" th:text="${#temporals.format(share.expireTime, 'yyyy-MM-dd HH:mm')}">过期时间</span>
                            </div>
                            <div class="stat-item" th:if="${share.downloadLimit != null && share.downloadLimit != -1}">
                                <span class="stat-label">下载限制</span>
                                <span class="stat-value" th:text="${share.downloadCount} + '/' + ${share.downloadLimit}">下载次数</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="file-actions">
                        <button th:if="${share.shareType.name() == 'VIEW'}" class="btn btn-outline" onclick="previewFile()">
                            <span class="btn-icon" th:text="${contentType == 'folder' ? '👁️' : '👁️'}">👁️</span>
                            <span th:text="${contentType == 'folder' ? '预览文件夹' : '预览文件'}">预览文件</span>
                        </button>
                        <button th:if="${share.shareType.name() == 'DOWNLOAD' || share.shareType.name() == 'VIEW'}" 
                                class="btn btn-primary" onclick="downloadFile()" id="downloadBtn">
                            <span class="btn-icon" th:text="${contentType == 'folder' ? '📦' : '⬇️'}">⬇️</span>
                            <span th:text="${contentType == 'folder' ? '下载文件夹' : '下载文件'}">下载文件</span>
                        </button>
                    </div>
                    
                    <!-- 文件预览区域 -->
                    <div class="file-preview" id="filePreview" style="display: none;">
                        <div class="preview-header">
                            <h3>文件预览</h3>
                            <button class="close-preview" onclick="closePreview()">&times;</button>
                        </div>
                        <div class="preview-content" id="previewContent">
                            <!-- 预览内容将在这里动态加载 -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 分享信息 -->
            <div class="share-footer">
                <div class="share-tips">
                    <h4>使用说明</h4>
                    <ul>
                        <li>请在有效期内下载文件</li>
                        <li>下载链接仅限本次使用</li>
                        <li>请勿将分享链接泄露给他人</li>
                        <li>如有问题请联系分享者</li>
                    </ul>
                </div>
                
                <div class="powered-by">
                    <p>Powered by <strong>LLB文件管理系统</strong></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Toast 提示 -->
    <div id="toast" class="toast"></div>
    
    <script th:src="@{/static/js/common.js}"></script>
    <script th:src="@{/static/js/share-access.js}"></script>
    <script th:inline="javascript">
        // 传递分享信息到前端
        window.shareCode = /*[[${share.shareCode}]]*/ '';
        window.needPassword = /*[[${needPassword}]]*/ false;
        window.contentType = /*[[${contentType}]]*/ 'file';
    </script>
</body>
</html>
