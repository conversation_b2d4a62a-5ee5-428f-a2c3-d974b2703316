<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传 - LLB文件管理系统</title>
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <link rel="stylesheet" th:href="@{/static/css/dashboard.css}">
    <link rel="stylesheet" th:href="@{/static/css/upload.css}">
    <style>
        /* 确保上传区域样式正确显示 */
        .upload-zone {
            border: 3px dashed #e0e0e0 !important;
            border-radius: 12px !important;
            padding: 60px 40px !important;
            text-align: center !important;
            background-color: #ffffff !important;
            transition: all 0.3s ease !important;
            cursor: pointer !important;
            position: relative !important;
            overflow: hidden !important;
            display: block !important;
            width: 100% !important;
            box-sizing: border-box !important;
        }

        .upload-zone:hover {
            border-color: #ff6b9d !important;
            background: linear-gradient(135deg, #ffffff, #fdf2f8) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
        }

        .upload-zone.drag-over {
            border-color: #ff6b9d !important;
            background: linear-gradient(135deg, #ffc1e3, #ffb3d1) !important;
            transform: scale(1.02) !important;
        }

        .upload-icon {
            display: inline-block !important;
            margin-bottom: 16px !important;
            opacity: 0.6 !important;
            transition: all 0.3s ease !important;
            color: #ff6b9d !important;
        }

        .upload-icon svg {
            width: 64px !important;
            height: 64px !important;
            stroke: currentColor !important;
        }

        .upload-zone h3 {
            font-size: 24px !important;
            font-weight: 600 !important;
            color: #333333 !important;
            margin-bottom: 8px !important;
        }

        .upload-zone p {
            font-size: 16px !important;
            color: #666666 !important;
            margin-bottom: 24px !important;
        }

        .upload-buttons {
            display: flex !important;
            gap: 16px !important;
            justify-content: center !important;
            margin-top: 20px !important;
        }

        .upload-buttons .btn {
            padding: 12px 24px !important;
            border-radius: 8px !important;
            font-size: 16px !important;
            font-weight: 500 !important;
            border: none !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
        }

        .upload-buttons .btn-primary {
            background-color: #007bff !important;
            color: white !important;
        }

        .upload-buttons .btn-primary:hover {
            background-color: #0056b3 !important;
        }

        .upload-buttons .btn-secondary {
            background-color: #6c757d !important;
            color: white !important;
        }

        .upload-buttons .btn-secondary:hover {
            background-color: #545b62 !important;
        }
    </style>
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>LLB文件系统</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="/dashboard" class="nav-link">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">仪表板</span>
                    </a></li>
                    <li><a href="/files" class="nav-link">
                        <span class="nav-icon">📁</span>
                        <span class="nav-text">文件管理</span>
                    </a></li>
                    <li><a href="/upload" class="nav-link active">
                        <span class="nav-icon">⬆️</span>
                        <span class="nav-text">文件上传</span>
                    </a></li>
                    <li><a href="/shares" class="nav-link">
                        <span class="nav-icon">🔗</span>
                        <span class="nav-text">分享管理</span>
                    </a></li>
                    <li><a href="/recycle" class="nav-link">
                        <span class="nav-icon">🗑️</span>
                        <span class="nav-text">回收站</span>
                    </a></li>
                    <li><a href="/profile" class="nav-link">
                        <span class="nav-icon">👤</span>
                        <span class="nav-text">个人中心</span>
                    </a></li>
                    <li th:if="${user != null && user.role.name() == 'ADMIN'}">
                        <a href="/admin" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            <span class="nav-text">系统管理</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>文件上传</h1>
                    <nav class="breadcrumb">
                        <a href="/files">根目录</a>
                        <span th:each="folder : ${breadcrumbPath}">
                            / <a th:href="@{/files(folderId=${folder.id})}" th:text="${folder.name}">文件夹</a>
                        </span>
                        <span th:if="${currentFolder != null}">
                            / <span class="current-folder" th:text="${currentFolder.name}">当前文件夹</span>
                        </span>
                    </nav>
                </div>
                <div class="top-bar-right">
                    <div class="user-info">
                        <div class="user-menu">
                            <button class="user-avatar" onclick="toggleUserMenu()">
                                <img th:src="${user != null and user.avatar != null ? user.avatar : '/static/images/default-avatar.svg'}" alt="头像">
                            </button>
                            <div class="user-dropdown" id="userDropdown">
                                <a href="/profile">个人设置</a>
                                <a href="#" onclick="logout()">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 上传内容 -->
            <div class="upload-content">
                <!-- 上传区域 -->
                <div class="upload-area" id="uploadArea">
                    <label for="fileInput" class="upload-zone" id="uploadZone">
                        <div class="upload-icon">
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                <polyline points="14,2 14,8 20,8"></polyline>
                                <line x1="16" y1="13" x2="8" y2="13"></line>
                                <line x1="16" y1="17" x2="8" y2="17"></line>
                                <polyline points="10,9 9,9 8,9"></polyline>
                            </svg>
                        </div>
                        <h3>拖拽文件或文件夹到此处上传</h3>
                        <p>或者点击下方按钮选择</p>
                    </label>
                    <div class="upload-buttons">
                        <button type="button" class="btn btn-primary" onclick="selectFiles()">
                            📄 选择文件
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="selectFolder()">
                            📁 选择文件夹
                        </button>
                    </div>
                    <input type="file" id="fileInput" multiple style="display: none;">
                    <input type="file" id="folderInput" webkitdirectory directory multiple style="display: none;">
                </div>
                
                <!-- 上传列表 -->
                <div class="upload-list" id="uploadList" style="display: none;">
                    <div class="upload-header">
                        <h3>上传列表</h3>
                        <div class="upload-actions">
                            <button class="btn btn-outline" onclick="clearCompleted()">清除已完成</button>
                            <button class="btn btn-outline" onclick="cancelAll()">取消全部</button>
                        </div>
                    </div>
                    <div class="upload-items" id="uploadItems">
                        <!-- 上传项目将在这里动态添加 -->
                    </div>
                </div>
                
                <!-- 上传统计 -->
                <div class="upload-stats" id="uploadStats" style="display: none;">
                    <div class="stats-item">
                        <span class="stats-label">总文件数:</span>
                        <span class="stats-value" id="totalFiles">0</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">已完成:</span>
                        <span class="stats-value" id="completedFiles">0</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">总大小:</span>
                        <span class="stats-value" id="totalSize">0 B</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">上传速度:</span>
                        <span class="stats-value" id="uploadSpeed">0 B/s</span>
                    </div>
                </div>
                
                <!-- 上传提示 -->
                <div class="upload-tips">
                    <h4>上传说明</h4>
                    <ul>
                        <li>支持拖拽上传，可同时上传多个文件或整个文件夹</li>
                        <li>支持文件夹上传，自动保持文件夹结构</li>
                        <li>单个文件大小限制：<span class="highlight">1GB</span>（管理员：5GB）</li>
                        <li>支持所有文件类型上传</li>
                        <li>上传过程中请勿关闭页面</li>
                        <li>大文件上传支持断点续传</li>
                    </ul>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Toast 提示 -->
    <div id="toast" class="toast"></div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <script th:src="@{/static/js/common.js}"></script>
    <script th:src="@{/static/js/upload.js}"></script>
    <script th:inline="javascript">
        // 传递当前文件夹ID到前端
        window.currentFolderId = /*[[${currentFolder?.id ?: 0}]]*/ 0;
    </script>
</body>
</html>
