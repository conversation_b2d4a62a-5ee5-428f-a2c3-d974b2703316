<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - LLB文件管理系统</title>
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <link rel="stylesheet" th:href="@{/static/css/dashboard.css}">
    <link rel="stylesheet" th:href="@{/static/css/profile.css}">
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>LLB文件系统</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="/dashboard" class="nav-link">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">仪表板</span>
                    </a></li>
                    <li><a href="/files" class="nav-link">
                        <span class="nav-icon">📁</span>
                        <span class="nav-text">文件管理</span>
                    </a></li>
                    <li><a href="/upload" class="nav-link">
                        <span class="nav-icon">⬆️</span>
                        <span class="nav-text">文件上传</span>
                    </a></li>
                    <li><a href="/shares" class="nav-link">
                        <span class="nav-icon">🔗</span>
                        <span class="nav-text">分享管理</span>
                    </a></li>
                    <li><a href="/recycle" class="nav-link">
                        <span class="nav-icon">🗑️</span>
                        <span class="nav-text">回收站</span>
                    </a></li>
                    <li><a href="/profile" class="nav-link active">
                        <span class="nav-icon">👤</span>
                        <span class="nav-text">个人中心</span>
                    </a></li>
                    <li th:if="${user != null && user.role.name() == 'ADMIN'}">
                        <a href="/admin" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            <span class="nav-text">系统管理</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>个人中心</h1>
                </div>
                <div class="top-bar-right">
                    <div class="user-info">
                        <div class="user-menu">
                            <button class="user-avatar" onclick="toggleUserMenu()">
                                <img th:src="${user != null and user.avatar != null ? user.avatar : '/static/images/default-avatar.svg'}" alt="头像">
                            </button>
                            <div class="user-dropdown" id="userDropdown">
                                <a href="/profile">个人设置</a>
                                <a href="#" onclick="logout()">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 个人中心内容 -->
            <div class="profile-content">
                <!-- 用户信息卡片 -->
                <div class="profile-card">
                    <div class="profile-header">
                        <div class="avatar-section">
                            <div class="avatar-container">
                                <img th:src="${user != null and user.avatar != null ? user.avatar : '/static/images/default-avatar.svg'}" alt="头像" id="avatarImg">
                                <div class="avatar-overlay">
                                    <button class="avatar-upload-btn" onclick="uploadAvatar()">更换头像</button>
                                </div>
                            </div>
                            <input type="file" id="avatarInput" accept="image/*" style="display: none;" onchange="handleAvatarUpload()">
                        </div>
                        <div class="user-basic-info">
                            <h2 th:text="${user.nickname}">用户昵称</h2>
                            <p class="username" th:text="'@' + ${user.username}">@username</p>
                            <div class="user-badges">
                                <span th:if="${user != null && user.role.name() == 'ADMIN'}" class="badge admin">管理员</span>
                                <span th:if="${user != null && user.role.name() == 'USER'}" class="badge user">普通用户</span>
                                <span th:classappend="${user.status.name().toLowerCase()}" class="badge status" th:text="${user.status.name()}">状态</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 用户统计 -->
                    <div class="profile-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="fileCount">0</div>
                            <div class="stat-label">文件数量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="folderCount">0</div>
                            <div class="stat-label">文件夹数量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="shareCount">0</div>
                            <div class="stat-label">分享数量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="downloadCount">0</div>
                            <div class="stat-label">下载次数</div>
                        </div>
                    </div>
                </div>
                
                <!-- 存储使用情况 -->
                <div class="storage-card">
                    <h3>存储使用情况</h3>
                    <div class="storage-info">
                        <div class="storage-text">
                            <span id="storageUsedText" th:text="${@fileSizeUtil.format(user.storageUsed)}">0 MB</span>
                            <span th:if="${user.storageLimit != -1}"> / </span>
                            <span th:if="${user.storageLimit != -1}" id="storageLimitText" th:text="${@fileSizeUtil.format(user.storageLimit)}">1.5 GB</span>
                            <span th:if="${user.storageLimit == -1}" class="unlimited">无限制</span>
                        </div>
                        <div class="storage-bar" th:if="${user.storageLimit != -1}">
                            <div class="storage-used" id="storageBar" th:style="'width: ' + ${user.storageUsed * 100 / user.storageLimit} + '%'"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 个人信息设置 -->
                <div class="settings-card">
                    <h3>个人信息</h3>
                    <form id="profileForm" class="profile-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="nickname">昵称</label>
                                <input type="text" id="nickname" name="nickname" th:value="${user.nickname}">
                            </div>
                            <div class="form-group">
                                <label for="email">邮箱</label>
                                <input type="email" id="email" name="email" th:value="${user.email}">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>用户名</label>
                                <input type="text" th:value="${user.username}" readonly class="readonly">
                            </div>
                            <div class="form-group">
                                <label>注册时间</label>
                                <input type="text" th:value="${#temporals.format(user.createdTime, 'yyyy-MM-dd HH:mm')}" readonly class="readonly">
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn btn-primary" onclick="updateProfile()">保存修改</button>
                        </div>
                    </form>
                </div>
                
                <!-- 密码修改 -->
                <div class="settings-card">
                    <h3>修改密码</h3>
                    <form id="passwordForm" class="profile-form">
                        <div class="form-group">
                            <label for="oldPassword">当前密码</label>
                            <input type="password" id="oldPassword" name="oldPassword" required>
                        </div>
                        <div class="form-group">
                            <label for="newPassword">新密码</label>
                            <input type="password" id="newPassword" name="newPassword" required>
                        </div>
                        <div class="form-group">
                            <label for="confirmPassword">确认新密码</label>
                            <input type="password" id="confirmPassword" name="confirmPassword" required>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn btn-primary" onclick="changePassword()">修改密码</button>
                        </div>
                    </form>
                </div>
                
                <!-- 账户信息 -->
                <div class="settings-card">
                    <h3>账户信息</h3>
                    <div class="account-info">
                        <div class="info-item">
                            <label>最后登录时间:</label>
                            <span th:text="${user.lastLoginTime != null ? #temporals.format(user.lastLoginTime, 'yyyy-MM-dd HH:mm:ss') : '从未登录'}">从未登录</span>
                        </div>
                        <div class="info-item">
                            <label>最后登录IP:</label>
                            <span th:text="${user.lastLoginIp != null ? user.lastLoginIp : '未知'}">未知</span>
                        </div>
                        <div class="info-item">
                            <label>单文件大小限制:</label>
                            <span th:text="${user.singleFileLimit != -1 ? @fileSizeUtil.format(user.singleFileLimit) : '无限制'}">1 GB</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Toast 提示 -->
    <div id="toast" class="toast"></div>
    
    <script th:src="@{/static/js/common.js}"></script>
    <script th:src="@{/static/js/profile.js}"></script>
    <script th:inline="javascript">
        // 传递用户信息到前端
        window.currentUser = /*[[${user}]]*/ {};
    </script>
</body>
</html>
