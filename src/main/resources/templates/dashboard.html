<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板 - LLB文件管理系统</title>
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <link rel="stylesheet" th:href="@{/static/css/dashboard.css}">
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>LLB文件系统</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="/dashboard" class="nav-link active">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">仪表板</span>
                    </a></li>
                    <li><a href="/files" class="nav-link">
                        <span class="nav-icon">📁</span>
                        <span class="nav-text">文件管理</span>
                    </a></li>
                    <li><a href="/upload" class="nav-link">
                        <span class="nav-icon">⬆️</span>
                        <span class="nav-text">文件上传</span>
                    </a></li>
                    <li><a href="/shares" class="nav-link">
                        <span class="nav-icon">🔗</span>
                        <span class="nav-text">分享管理</span>
                    </a></li>
                    <li><a href="/recycle" class="nav-link">
                        <span class="nav-icon">🗑️</span>
                        <span class="nav-text">回收站</span>
                    </a></li>
                    <li><a href="/profile" class="nav-link">
                        <span class="nav-icon">👤</span>
                        <span class="nav-text">个人中心</span>
                    </a></li>
                    <li th:if="${user != null && user.role.name() == 'ADMIN'}">
                        <a href="/admin" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            <span class="nav-text">系统管理</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>仪表板</h1>
                    <nav class="breadcrumb">
                        <span>系统概览</span>
                    </nav>
                </div>
                <div class="top-bar-right">
                    <div class="user-info">
                        <div class="user-menu">
                            <button class="user-avatar" onclick="toggleUserMenu()">
                                <img th:src="${user != null and user.avatar != null ? user.avatar : '/static/images/default-avatar.svg'}" alt="头像">
                            </button>
                            <div class="user-dropdown" id="userDropdown">
                                <a href="/profile">个人设置</a>
                                <a href="#" onclick="logout()">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 仪表板内容 -->
            <div class="dashboard-content">
                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📁</div>
                        <div class="stat-info">
                            <h3 id="fileCount">0</h3>
                            <p>文件总数</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">💾</div>
                        <div class="stat-info">
                            <h3 id="storageUsed">0 MB</h3>
                            <p>已用空间</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">📊</div>
                        <div class="stat-info">
                            <h3 id="storagePercent">0%</h3>
                            <p>空间使用率</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">⬇️</div>
                        <div class="stat-info">
                            <h3 id="downloadCount">0</h3>
                            <p>下载次数</p>
                        </div>
                    </div>
                </div>
                
                <!-- 存储空间使用情况 -->
                <div class="storage-section">
                    <h2>存储空间使用情况</h2>
                    <div class="storage-bar">
                        <div class="storage-used" id="storageBar" style="width: 0%"></div>
                    </div>
                    <div class="storage-info">
                        <span id="storageText">已使用 0 MB / 总共 1.5 GB</span>
                        <span th:if="${user != null && user.role.name() == 'ADMIN'}" class="storage-unlimited">管理员无限制</span>
                    </div>
                </div>
                
                <!-- 最近文件 -->
                <div class="recent-files">
                    <h2>最近文件</h2>
                    <div class="file-list" id="recentFileList">
                        <div class="loading">加载中...</div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Toast 提示 -->
    <div id="toast" class="toast"></div>
    
    <script th:src="@{/static/js/common.js}"></script>
    <script th:src="@{/static/js/dashboard.js}"></script>
    <script th:inline="javascript">
        // 传递用户信息到前端
        window.currentUser = /*[[${user}]]*/ {};
    </script>
</body>
</html>
