<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件管理 - LLB文件管理系统</title>
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <link rel="stylesheet" th:href="@{/static/css/dashboard.css}">
    <link rel="stylesheet" th:href="@{/static/css/files.css}">
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>LLB文件系统</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="/dashboard" class="nav-link">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">仪表板</span>
                    </a></li>
                    <li><a href="/files" class="nav-link active">
                        <span class="nav-icon">📁</span>
                        <span class="nav-text">文件管理</span>
                    </a></li>
                    <li><a href="/upload" class="nav-link">
                        <span class="nav-icon">⬆️</span>
                        <span class="nav-text">文件上传</span>
                    </a></li>
                    <li><a href="/shares" class="nav-link">
                        <span class="nav-icon">🔗</span>
                        <span class="nav-text">分享管理</span>
                    </a></li>
                    <li><a href="/recycle" class="nav-link">
                        <span class="nav-icon">🗑️</span>
                        <span class="nav-text">回收站</span>
                    </a></li>
                    <li><a href="/profile" class="nav-link">
                        <span class="nav-icon">👤</span>
                        <span class="nav-text">个人中心</span>
                    </a></li>
                    <li th:if="${user != null && user.role.name() == 'ADMIN'}">
                        <a href="/admin" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            <span class="nav-text">系统管理</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>文件管理</h1>
                    <!-- 面包屑导航 -->
                    <nav class="breadcrumb">
                        <a href="/files">根目录</a>
                        <span th:each="folder : ${breadcrumbPath}">
                            / <a th:href="@{/files(folderId=${folder.id})}" th:text="${folder.name}">文件夹</a>
                        </span>
                    </nav>
                </div>
                <div class="top-bar-right">
                    <div class="toolbar">
                        <!-- 返回上一级按钮 -->
                        <button th:if="${currentFolder != null}" class="btn btn-secondary"
                                th:onclick="'goToParentFolder(' + ${currentFolder.parentId ?: 0} + ')'"
                                title="返回上一级">
                            <span class="nav-icon">⬅️</span>
                            返回上级
                        </button>
                        <button class="btn btn-secondary" onclick="refreshFilesSimple()" title="刷新文件列表">
                            <span class="nav-icon">🔄</span>
                            刷新文件
                        </button>
                        <button class="btn btn-primary" onclick="showCreateFolderModal()">
                            <span class="nav-icon">📁</span>
                            新建文件夹
                        </button>
                        <a th:href="@{/upload(folderId=${currentFolder?.id ?: 0})}" class="btn btn-primary">
                            <span class="nav-icon">⬆️</span>
                            上传文件
                        </a>
                    </div>
                    <div class="user-info">
                        <div class="user-menu">
                            <button class="user-avatar" onclick="toggleUserMenu()">
                                <img th:src="${user != null and user.avatar != null ? user.avatar : '/static/images/default-avatar.svg'}" alt="头像">
                            </button>
                            <div class="user-dropdown" id="userDropdown">
                                <a href="/profile">个人设置</a>
                                <a href="#" onclick="logout()">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 文件管理内容 -->
            <div class="files-content">
                <!-- 搜索栏 -->
                <div class="search-bar">
                    <div class="search-input-group">
                        <input type="text" id="searchInput" placeholder="搜索文件..." class="search-input">
                        <button class="btn btn-outline" onclick="searchFiles()">搜索</button>
                        <button class="btn btn-outline" onclick="clearSearch()" id="clearSearchBtn" style="display: none;">清除</button>
                    </div>
                    <div class="selection-controls">
                        <label class="select-all-label">
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            <span>全选</span>
                        </label>
                    </div>
                    <div class="view-controls">
                        <button class="view-btn active" data-view="grid" onclick="switchView('grid')">
                            <span class="nav-icon">⊞</span>
                        </button>
                        <button class="view-btn" data-view="list" onclick="switchView('list')">
                            <span class="nav-icon">☰</span>
                        </button>
                    </div>
                </div>
                
                <!-- 批量操作栏 -->
                <div class="batch-actions" id="batchActions" style="display: none;">
                    <div class="batch-info">
                        已选择 <span id="selectedCount">0</span> 项
                    </div>
                    <div class="batch-buttons">
                        <button class="btn btn-outline" onclick="showMoveModal()">移动</button>
                        <button class="btn btn-outline" onclick="batchCopy()">复制</button>
                        <button class="btn btn-outline" onclick="batchDownload()">下载</button>
                        <button class="btn btn-outline" onclick="batchDelete()">删除</button>
                        <button class="btn btn-outline" onclick="clearSelection()">取消选择</button>
                    </div>
                </div>
                
                <!-- 文件列表 -->
                <div class="file-grid" id="fileGrid">
                    <!-- 文件夹 -->
                    <div th:each="folder : ${folders}" class="file-item folder-item" th:data-id="${folder.id}" data-type="folder">
                        <div class="file-checkbox">
                            <input type="checkbox" th:value="${folder.id}" onchange="updateSelection()">
                        </div>
                        <div class="file-icon">📁</div>
                        <div class="file-info">
                            <div class="file-name" th:text="${folder.name}">文件夹名称</div>
                            <div class="file-meta">
                                <span th:text="${#temporals.format(folder.createdTime, 'yyyy-MM-dd HH:mm')}">创建时间</span>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="action-btn" th:onclick="'downloadFolder(' + ${folder.id} + ')'">下载</button>
                            <button class="action-btn" th:onclick="'shareFolderModal(' + ${folder.id} + ')'" title="分享文件夹">分享</button>
                            <button class="action-btn" th:onclick="'openFolder(' + ${folder.id} + ')'">打开</button>
                            <button class="action-btn" th:onclick="'renameFolder(' + ${folder.id} + ')'">重命名</button>
                            <button class="action-btn" th:onclick="'deleteFolder(' + ${folder.id} + ')'">删除</button>
                        </div>
                    </div>
                    
                    <!-- 文件 -->
                    <div th:each="file : ${files}" class="file-item" th:data-id="${file.id}" data-type="file">
                        <div class="file-checkbox">
                            <input type="checkbox" th:value="${file.id}" onchange="updateSelection()">
                        </div>
                        <div class="file-icon" th:text="${@fileIconUtil.getIcon(file.originalName)}">📄</div>
                        <div class="file-info">
                            <div class="file-name" th:text="${file.name}">文件名称</div>
                            <div class="file-meta">
                                <span th:text="${@fileSizeUtil.format(file.fileSize)}">文件大小</span> • 
                                <span th:text="${#temporals.format(file.createdTime, 'yyyy-MM-dd HH:mm')}">创建时间</span>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="action-btn" th:onclick="'downloadFile(' + ${file.id} + ')'">下载</button>
                            <button class="action-btn" th:onclick="'shareFile(' + ${file.id} + ')'">分享</button>
                            <button class="action-btn" th:onclick="'copyFile(' + ${file.id} + ')'">复制</button>
                            <button class="action-btn" th:onclick="'renameFile(' + ${file.id} + ')'">重命名</button>
                            <button class="action-btn" th:onclick="'deleteFile(' + ${file.id} + ')'">删除</button>
                        </div>
                    </div>
                </div>
                
                <!-- 空状态 -->
                <div class="empty-state" th:if="${#lists.isEmpty(folders) && #lists.isEmpty(files)}">
                    <div class="empty-icon">📂</div>
                    <h3>文件夹为空</h3>
                    <p>开始上传文件或创建文件夹吧</p>
                    <div class="empty-actions">
                        <a th:href="@{/upload(folderId=${currentFolder?.id ?: 0})}" class="btn btn-primary">上传文件</a>
                        <button class="btn btn-outline" onclick="showCreateFolderModal()">新建文件夹</button>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- 创建文件夹模态框 -->
    <div class="modal" id="createFolderModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新建文件夹</h3>
                <button class="modal-close" onclick="hideCreateFolderModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="createFolderForm">
                    <div class="form-group">
                        <label for="folderName">文件夹名称</label>
                        <input type="text" id="folderName" name="folderName" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="hideCreateFolderModal()">取消</button>
                <button class="btn btn-primary" onclick="createFolder()">创建</button>
            </div>
        </div>
    </div>

    <!-- 分享模态框 -->
    <div class="modal" id="shareModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="shareModalTitle">分享文件</h3>
                <button class="modal-close" onclick="hideShareModal()">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 分享表单 -->
                <div class="share-form-section">
                    <form id="shareForm">
                        <div class="form-group">
                            <label for="shareType">分享类型</label>
                            <select id="shareType" name="shareType">
                                <option value="DOWNLOAD">允许下载</option>
                                <option value="VIEW">仅查看</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="sharePassword">访问密码（可选）</label>
                            <input type="text" id="sharePassword" name="password" placeholder="留空表示无密码">
                        </div>

                        <div class="form-group">
                            <label for="expireTime">过期时间（可选）</label>
                            <input type="datetime-local" id="expireTime" name="expireTime">
                        </div>

                        <div class="form-group">
                            <label for="downloadLimit">下载次数限制（可选）</label>
                            <input type="number" id="downloadLimit" name="downloadLimit" min="1" placeholder="留空表示无限制">
                        </div>
                    </form>
                </div>

                <!-- 分享结果 -->
                <div class="share-result-section" style="display: none;">
                    <div class="form-group">
                        <label for="shareLink">分享链接</label>
                        <div class="input-group">
                            <input type="text" id="shareLink" readonly>
                            <button class="btn btn-outline" onclick="copyShareLink()">复制</button>
                        </div>
                    </div>

                    <div id="shareInfo" class="share-info">
                        <!-- 分享信息将在这里显示 -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="share-form-section">
                    <button class="btn btn-outline" onclick="hideShareModal()">取消</button>
                    <button class="btn btn-primary" onclick="createShare()">创建分享</button>
                </div>
                <div class="share-result-section" style="display: none;">
                    <button class="btn btn-outline" onclick="createNewShare()">重新设置</button>
                    <button class="btn btn-primary" onclick="hideShareModal()">完成</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 提示 -->
    <div id="toast" class="toast"></div>
    
    <script th:src="@{/static/js/common.js}"></script>
    <script th:src="@{/static/js/files.js}"></script>
    <script th:inline="javascript">
        // 传递当前文件夹ID到前端
        window.currentFolderId = /*[[${currentFolder?.id ?: 0}]]*/ 0;
    </script>
</body>
</html>
