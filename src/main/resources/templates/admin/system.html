<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - LLB文件管理系统</title>
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <link rel="stylesheet" th:href="@{/static/css/dashboard.css}">
    <link rel="stylesheet" th:href="@{/static/css/admin.css}">
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>LLB管理面板</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="/admin" class="nav-link">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">管理面板</span>
                    </a></li>
                    <li><a href="/admin/users" class="nav-link">
                        <span class="nav-icon">👥</span>
                        <span class="nav-text">用户管理</span>
                    </a></li>
                    <li><a href="/admin/stats" class="nav-link">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">统计分析</span>
                    </a></li>
                    <li><a href="/admin/ip-management" class="nav-link">
                        <span class="nav-icon">🛡️</span>
                        <span class="nav-text">IP管理</span>
                    </a></li>
                    <li><a href="/admin/system" class="nav-link active">
                        <span class="nav-icon">⚙️</span>
                        <span class="nav-text">系统设置</span>
                    </a></li>
                    <li class="nav-divider"></li>
                    <li><a href="/dashboard" class="nav-link">
                        <span class="nav-icon">🏠</span>
                        <span class="nav-text">返回用户界面</span>
                    </a></li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>系统设置</h1>
                    <p class="page-subtitle">系统配置和管理功能设置</p>
                </div>
                <div class="top-bar-right">
                    <div class="user-info">
                        <div class="user-menu">
                            <button class="user-avatar" onclick="toggleUserMenu()">
                                <img th:src="${user != null && user.avatarPath != null ? user.avatarPath : '/static/images/default-avatar.svg'}" 
                                     alt="用户头像">
                            </button>
                            <div class="user-dropdown" id="userDropdown">
                                <a href="/profile">个人设置</a>
                                <a href="/auth/logout">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 系统设置内容 -->
            <div class="admin-dashboard-content">
                
                <!-- 系统信息 -->
                <div class="content-section">
                    <div class="section-header">
                        <h2>系统信息</h2>
                    </div>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>系统版本</label>
                            <span>LLB NetworkDisk v2.3.1</span>
                        </div>
                        <div class="info-item">
                            <label>Java版本</label>
                            <span th:text="${@environment.getProperty('java.version')}">Java 17</span>
                        </div>
                        <div class="info-item">
                            <label>Spring Boot版本</label>
                            <span>3.5.4</span>
                        </div>
                        <div class="info-item">
                            <label>数据库</label>
                            <span>MySQL</span>
                        </div>
                        <div class="info-item" th:if="${stats}">
                            <label>总用户数</label>
                            <span th:text="${stats.totalUsers}">0</span>
                        </div>
                        <div class="info-item" th:if="${stats}">
                            <label>总文件数</label>
                            <span th:text="${stats.totalFiles}">0</span>
                        </div>
                    </div>
                </div>
                
                <!-- 存储配置 -->
                <div class="content-section">
                    <div class="section-header">
                        <h2>存储配置</h2>
                    </div>
                    <div class="setting-item">
                        <label>默认用户存储限制</label>
                        <div class="setting-control">
                            <input type="number" class="form-control" value="5" min="1" max="100">
                            <span class="unit-text">GB</span>
                        </div>
                        <small class="help-text">新注册用户的默认存储空间限制</small>
                    </div>
                    
                    <div class="setting-item">
                        <label>单文件大小限制</label>
                        <div class="setting-control">
                            <input type="number" class="form-control" value="500" min="1" max="2048">
                            <span class="unit-text">MB</span>
                        </div>
                        <small class="help-text">单个文件上传的最大大小限制</small>
                    </div>
                    
                    <div class="setting-item">
                        <label>允许的文件类型</label>
                        <div class="setting-control">
                            <textarea class="form-control" rows="3" placeholder="jpg,png,gif,pdf,doc,docx,txt,zip">jpg,png,gif,pdf,doc,docx,txt,zip,rar,mp4,avi,mp3</textarea>
                        </div>
                        <small class="help-text">用逗号分隔文件扩展名</small>
                    </div>
                    
                    <div class="setting-actions">
                        <button class="btn btn-primary" onclick="saveStorageSettings()">保存设置</button>
                        <button class="btn btn-secondary" onclick="resetStorageSettings()">重置</button>
                    </div>
                </div>
                
                <!-- 安全配置 -->
                <div class="content-section">
                    <div class="section-header">
                        <h2>安全配置</h2>
                    </div>
                    <div class="setting-item">
                        <label>密码最小长度</label>
                        <div class="setting-control">
                            <input type="number" class="form-control" value="6" min="4" max="32">
                            <span class="unit-text">字符</span>
                        </div>
                    </div>
                    
                    <div class="setting-item">
                        <label>登录失败锁定</label>
                        <div class="setting-control">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        <small class="help-text">启用后，连续登录失败将临时锁定账户</small>
                    </div>
                    
                    <div class="setting-item">
                        <label>最大登录尝试次数</label>
                        <div class="setting-control">
                            <input type="number" class="form-control" value="5" min="3" max="20">
                            <span class="unit-text">次</span>
                        </div>
                    </div>
                    
                    <div class="setting-actions">
                        <button class="btn btn-primary" onclick="saveSecuritySettings()">保存设置</button>
                        <button class="btn btn-secondary" onclick="resetSecuritySettings()">重置</button>
                    </div>
                </div>
                
                <!-- 系统维护 -->
                <div class="content-section">
                    <div class="section-header">
                        <h2>系统维护</h2>
                    </div>
                    <div class="action-item">
                        <div class="action-info">
                            <h4>清理临时文件</h4>
                            <p>清理系统生成的临时文件和缓存</p>
                        </div>
                        <button class="btn btn-outline" onclick="cleanTempFiles()">立即清理</button>
                    </div>
                    
                    <div class="action-item">
                        <div class="action-info">
                            <h4>数据库优化</h4>
                            <p>优化数据库表结构和索引</p>
                        </div>
                        <button class="btn btn-outline" onclick="optimizeDatabase()">立即优化</button>
                    </div>
                    
                    <div class="action-item">
                        <div class="action-info">
                            <h4>系统日志</h4>
                            <p>查看和管理系统运行日志</p>
                        </div>
                        <button class="btn btn-outline" onclick="viewSystemLogs()">查看日志</button>
                    </div>
                </div>
                
            </div>
        </main>
    </div>
    
    <div id="toast" class="toast"></div>
    
    <script th:src="@{/static/js/common.js}"></script>
    <script>
        // 切换用户菜单
        function toggleUserMenu() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');
        }
        
        // 点击页面其他地方关闭菜单
        document.addEventListener('click', function(event) {
            const userDropdown = document.getElementById('userDropdown');
            const userAvatar = document.querySelector('.user-avatar');
            
            if (!userAvatar.contains(event.target)) {
                userDropdown.classList.remove('show');
            }
        });
        
        // 保存存储设置
        function saveStorageSettings() {
            Toast.info('存储设置保存功能开发中...');
        }
        
        // 重置存储设置
        function resetStorageSettings() {
            Toast.info('存储设置重置功能开发中...');
        }
        
        // 保存安全设置
        function saveSecuritySettings() {
            Toast.info('安全设置保存功能开发中...');
        }
        
        // 重置安全设置
        function resetSecuritySettings() {
            Toast.info('安全设置重置功能开发中...');
        }
        
        // 清理临时文件
        function cleanTempFiles() {
            Toast.info('临时文件清理功能开发中...');
        }
        
        // 优化数据库
        function optimizeDatabase() {
            Toast.info('数据库优化功能开发中...');
        }
        
        // 查看系统日志
        function viewSystemLogs() {
            Toast.info('系统日志查看功能开发中...');
        }
    </script>
</body>
</html>