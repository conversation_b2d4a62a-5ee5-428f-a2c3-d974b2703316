<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="'用户文件管理 - ' + ${user.username} + ' - LLB文件管理系统'">用户文件管理 - LLB文件管理系统</title>
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <link rel="stylesheet" th:href="@{/static/css/dashboard.css}">
    <link rel="stylesheet" th:href="@{/static/css/admin.css}">
    <link rel="stylesheet" th:href="@{/static/css/files.css}">
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>LLB管理面板</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="/admin" class="nav-link">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">管理面板</span>
                    </a></li>
                    <li><a href="/admin/users" class="nav-link active">
                        <span class="nav-icon">👥</span>
                        <span class="nav-text">用户管理</span>
                    </a></li>
                    <li><a href="/admin/stats" class="nav-link">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">统计分析</span>
                    </a></li>
                    <li><a href="/admin/ip-management" class="nav-link">
                        <span class="nav-icon">🛡️</span>
                        <span class="nav-text">IP管理</span>
                    </a></li>
                    <li><a href="/admin/system" class="nav-link">
                        <span class="nav-icon">⚙️</span>
                        <span class="nav-text">系统设置</span>
                    </a></li>
                    <li class="nav-divider"></li>
                    <li><a href="/dashboard" class="nav-link">
                        <span class="nav-icon">🏠</span>
                        <span class="nav-text">返回用户界面</span>
                    </a></li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <div class="breadcrumb">
                        <a href="/admin/users">用户管理</a>
                        <span class="breadcrumb-separator">›</span>
                        <span th:text="${user.username} + '的文件'">用户文件</span>
                    </div>
                    <h1 th:text="${user.nickname != null ? user.nickname : user.username} + '的文件管理'">用户文件管理</h1>
                    <p class="page-subtitle">管理用户上传的文件</p>
                </div>
                <div class="top-bar-right">
                    <div class="user-info">
                        <div class="user-stats">
                            <span class="stat-item">文件数量: <span id="fileCount" th:text="${userFiles != null ? userFiles.size() : 0}">0</span></span>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 管理面板内容区 -->
            <div class="admin-dashboard-content">
                <!-- 用户信息区域 -->
                <div class="content-section">
                    <div class="section-header">
                        <h2>用户信息</h2>
                    </div>
                    <div class="user-profile-card">
                        <div class="user-avatar">
                            <img th:src="${user.avatar != null ? user.avatar : '/static/images/default-avatar.svg'}" alt="用户头像">
                        </div>
                        <div class="user-details">
                            <h3 th:text="${user.nickname != null ? user.nickname : user.username}">用户名</h3>
                            <p class="user-meta">
                                <span th:text="'用户名: ' + ${user.username}">用户名</span> |
                                <span th:text="'邮箱: ' + (${user.email != null ? user.email : '未设置'})">邮箱</span> |
                                <span th:text="'角色: ' + (${user.role == 'ADMIN' ? '管理员' : '普通用户'})">角色</span> |
                                <span th:text="'状态: ' + (${user.status == 'ACTIVE' ? '正常' : user.status == 'DISABLED' ? '禁用' : '锁定'})">状态</span>
                            </p>
                            <div class="storage-info">
                                <div class="storage-text">
                                    存储使用量: <span th:text="${#strings.replace(user.storageUsed, 'B', '')} + ' / ' + ${#strings.replace(user.storageLimit, 'B', '')}">0 B / 0 B</span>
                                </div>
                                <div class="storage-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" th:style="'width: ' + ${user.storageUsed != null and user.storageLimit != null ? (user.storageUsed * 100.0 / user.storageLimit) : 0} + '%'"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 搜索和筛选 -->
                <div class="content-section">
                    <div class="section-header">
                        <h2>文件搜索</h2>
                    </div>
                    <div class="search-filters">
                        <div class="search-input-group">
                            <input type="text" id="searchFileName" placeholder="搜索文件名..." class="search-input">
                            <button class="btn btn-outline" onclick="searchFiles()">搜索</button>
                            <button class="btn btn-outline" onclick="resetSearch()">重置</button>
                        </div>
                    </div>
                </div>
                
                <!-- 文件列表 -->
                <div class="content-section">
                    <div class="section-header">
                        <h2>文件列表</h2>
                        <div class="list-controls">
                            <span id="totalFileCount">总计: <span th:text="${userFiles != null ? userFiles.size() : 0}">0</span> 个文件</span>
                            <div class="pagination-info">
                                <span id="pageInfo">第 1 页，共 1 页</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="files-table">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>文件名</th>
                                    <th>类型</th>
                                    <th>大小</th>
                                    <th>上传时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="filesTableBody">
                                <tr th:if="${userFiles == null or userFiles.isEmpty()}">
                                    <td colspan="5" class="empty-cell">
                                        <div class="empty-state">
                                            <div class="empty-icon">📁</div>
                                            <p>该用户暂无上传文件</p>
                                        </div>
                                    </td>
                                </tr>
                                <tr th:each="file : ${userFiles}" th:data-file-id="${file.id}">
                                    <td>
                                        <div class="file-info">
                                            <span class="file-icon" th:text="${file.fileType != null ? (file.fileType.startsWith('image') ? '🖼️' : file.fileType.startsWith('video') ? '🎬' : file.fileType.startsWith('audio') ? '🎵' : '📄') : '📄'}">📄</span>
                                            <div class="file-details">
                                                <div class="file-name" th:text="${file.name}">文件名</div>
                                                <div class="file-meta" th:text="${file.originalName}">原始文件名</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="file-type" th:text="${file.fileType ?: '未知'}">类型</span>
                                    </td>
                                    <td>
                                        <span class="file-size" th:text="${file.fileSize != null ? file.fileSize : '0 B'}">大小</span>
                                    </td>
                                    <td>
                                        <span class="upload-time" th:text="${#temporals.format(file.createdTime, 'yyyy-MM-dd HH:mm')}">上传时间</span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="action-btn download-btn" th:data-file-id="${file.id}" onclick="downloadFile(this.dataset.fileId)">下载</button>
                                            <button class="action-btn delete-btn" th:data-file-id="${file.id}" th:data-file-name="${file.name}" onclick="deleteFile(this.dataset.fileId, this.dataset.fileName)">删除</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="pagination" id="pagination">
                        <!-- 分页按钮将通过JavaScript生成 -->
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Toast 提示 -->
    <div id="toast" class="toast"></div>
    
    <script th:src="@{/static/js/common.js}"></script>
    <script th:inline="javascript">
        // 用户ID
        const userId = /*[[${userId}]]*/ 0;
        let currentPage = 1;
        let pageSize = 20;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadFiles();
        });
        
        /**
         * 加载文件列表
         */
        function loadFiles() {
            const fileName = document.getElementById('searchFileName').value.trim();
            
            const params = new URLSearchParams({
                page: currentPage,
                size: pageSize
            });
            
            if (fileName) params.append('fileName', fileName);
            
            fetch(`/api/admin/users/${userId}/files?${params}`)
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        displayFiles(result.data.files);
                        updatePagination(result.data.total);
                        document.getElementById('totalFileCount').innerHTML = `总计: ${result.data.total} 个文件`;
                    } else {
                        Toast.error(result.message || '加载文件列表失败');
                    }
                })
                .catch(error => {
                    console.error('加载文件列表出错:', error);
                    Toast.error('加载失败，请重试');
                });
        }
        
        /**
         * 显示文件列表
         */
        function displayFiles(files) {
            const tbody = document.getElementById('filesTableBody');
            
            if (!files || files.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="empty-cell">
                            <div class="empty-state">
                                <div class="empty-icon">📁</div>
                                <p>暂无文件数据</p>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = files.map(file => `
                <tr data-file-id="${file.id}">
                    <td>
                        <div class="file-info">
                            <span class="file-icon">${Utils.getFileIcon(file.name || file.originalName)}</span>
                            <div class="file-details">
                                <div class="file-name">${file.name || file.originalName}</div>
                                <div class="file-meta">${file.originalName || ''}</div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="file-type">${file.fileType || '未知'}</span>
                    </td>
                    <td>
                        <span class="file-size">${Utils.formatFileSize(file.fileSize || 0)}</span>
                    </td>
                    <td>
                        <span class="upload-time">${Utils.formatDateTime(file.createdTime)}</span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn download-btn" onclick="downloadFile(${file.id})">下载</button>
                            <button class="action-btn delete-btn" onclick="deleteFile(${file.id}, '${file.name || file.originalName}')">删除</button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }
        
        /**
         * 更新分页
         */
        function updatePagination(total) {
            const totalPages = Math.ceil(total / pageSize);
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }
            
            let paginationHTML = '';
            
            // 上一页
            if (currentPage > 1) {
                paginationHTML += `<button class="page-btn" onclick="goToPage(${currentPage - 1})">上一页</button>`;
            }
            
            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                paginationHTML += `<button class="page-btn ${i === currentPage ? 'active' : ''}" onclick="goToPage(${i})">${i}</button>`;
            }
            
            // 下一页
            if (currentPage < totalPages) {
                paginationHTML += `<button class="page-btn" onclick="goToPage(${currentPage + 1})">下一页</button>`;
            }
            
            pagination.innerHTML = paginationHTML;
            
            // 更新页面信息
            document.getElementById('pageInfo').textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
        }
        
        /**
         * 跳转到指定页面
         */
        function goToPage(page) {
            currentPage = page;
            loadFiles();
        }
        
        /**
         * 搜索文件
         */
        function searchFiles() {
            currentPage = 1;
            loadFiles();
        }
        
        /**
         * 重置搜索
         */
        function resetSearch() {
            document.getElementById('searchFileName').value = '';
            currentPage = 1;
            loadFiles();
        }
        
        /**
         * 下载文件
         */
        function downloadFile(fileId) {
            window.open(`/api/files/${fileId}/download`, '_blank');
        }
        
        /**
         * 删除文件
         */
        function deleteFile(fileId, fileName) {
            if (!confirm(`确定要删除文件 "${fileName}" 吗？此操作不可恢复！`)) {
                return;
            }
            
            fetch(`/api/admin/users/${userId}/files/${fileId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    Toast.success('文件删除成功');
                    loadFiles();
                } else {
                    Toast.error(result.message || '删除失败');
                }
            })
            .catch(error => {
                console.error('删除文件出错:', error);
                Toast.error('删除失败，请重试');
            });
        }
        
        // 搜索框回车事件
        document.getElementById('searchFileName').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchFiles();
            }
        });
    </script>
</body>
</html>