<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析 - LLB文件管理系统</title>
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <link rel="stylesheet" th:href="@{/static/css/dashboard.css}">
    <link rel="stylesheet" th:href="@{/static/css/admin.css}">
    <link rel="stylesheet" th:href="@{/static/css/analytics.css}">
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>LLB管理面板</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul>  
                    <li><a href="/admin" class="nav-link">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">管理面板</span>
                    </a></li>
                    <li><a href="/admin/users" class="nav-link">
                        <span class="nav-icon">👥</span>
                        <span class="nav-text">用户管理</span>
                    </a></li>
                    <li><a href="/admin/analytics" class="nav-link active">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">数据分析</span>
                    </a></li>
                    <li><a href="/admin/stats" class="nav-link">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">统计分析</span>
                    </a></li>
                    <li><a href="/admin/permissions" class="nav-link">
                        <span class="nav-icon">🔐</span>
                        <span class="nav-text">权限管理</span>
                    </a></li>
                    <li><a href="/admin/ip-management" class="nav-link">
                        <span class="nav-icon">🛡️</span>
                        <span class="nav-text">IP管理</span>
                    </a></li>
                    <li><a href="/admin/system" class="nav-link">
                        <span class="nav-icon">⚙️</span>
                        <span class="nav-text">系统设置</span>
                    </a></li>
                    <li class="nav-divider"></li>
                    <li><a href="/dashboard" class="nav-link">
                        <span class="nav-icon">🏠</span>
                        <span class="nav-text">返回用户界面</span>
                    </a></li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>数据分析</h1>
                    <p class="page-subtitle">系统数据统计与分析报告</p>
                </div>
                <div class="top-bar-right">
                    <div class="user-info">
                        <div class="user-menu">
                            <button class="user-avatar" onclick="toggleUserMenu()">
                                <img th:src="${user != null and user.avatar != null ? user.avatar : '/static/images/default-avatar.svg'}" alt="头像">
                            </button>
                            <div class="user-dropdown" id="userDropdown">
                                <a href="/profile">个人设置</a>
                                <a href="#" onclick="logout()">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 管理面板内容区 -->
            <div class="admin-dashboard-content">
                
                <!-- 时间范围选择器 -->
                <div class="time-range-selector">
                    <div class="form-group">
                        <label for="startTime">开始时间:</label>
                        <input type="datetime-local" id="startTime" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="endTime">结束时间:</label>
                        <input type="datetime-local" id="endTime" class="form-control">
                    </div>
                    <button type="button" class="btn btn-primary" onclick="refreshData()">刷新数据</button>
                    <button type="button" class="btn btn-secondary" onclick="setQuickRange('today')">今天</button>
                    <button type="button" class="btn btn-secondary" onclick="setQuickRange('week')">本周</button>
                    <button type="button" class="btn btn-secondary" onclick="setQuickRange('month')">本月</button>
                </div>
                
                <!-- 概览卡片 -->
                <div class="overview-cards">
                    <div class="card">
                        <div class="card-header">
                            <h3>下载统计</h3>
                        </div>
                        <div class="card-body">
                            <div class="stat-item">
                                <span class="stat-label">总下载次数:</span>
                                <span class="stat-value" id="totalDownloads">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">今日下载:</span>
                                <span class="stat-value" id="todayDownloads">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">增长率:</span>
                                <span class="stat-value" id="growthRate">-</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3>访问统计</h3>
                        </div>
                        <div class="card-body">
                            <div class="stat-item">
                                <span class="stat-label">总访问次数:</span>
                                <span class="stat-value" id="totalAccess">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">今日访问:</span>
                                <span class="stat-value" id="todayAccess">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">独立IP:</span>
                                <span class="stat-value" id="uniqueIps">-</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3>存储统计</h3>
                        </div>
                        <div class="card-body">
                            <div class="stat-item">
                                <span class="stat-label">总文件数:</span>
                                <span class="stat-value" id="totalFiles">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">存储大小:</span>
                                <span class="stat-value" id="totalSize">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">已删除文件:</span>
                                <span class="stat-value" id="deletedFiles">-</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3>用户活跃度</h3>
                        </div>
                        <div class="card-body">
                            <div class="stat-item">
                                <span class="stat-label">总操作次数:</span>
                                <span class="stat-value" id="totalActions">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">活跃用户:</span>
                                <span class="stat-value" id="activeUsers">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">平均操作:</span>
                                <span class="stat-value" id="avgActions">-</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 图表区域 -->
                <div class="charts-container">
                    <div class="chart-row">
                        <div class="chart-card">
                            <div class="card-header">
                                <h3>下载趋势</h3>
                            </div>
                            <div class="card-body">
                                <canvas id="downloadTrendChart"></canvas>
                            </div>
                        </div>
                        
                        <div class="chart-card">
                            <div class="card-header">
                                <h3>访问趋势</h3>
                            </div>
                            <div class="card-body">
                                <canvas id="accessTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="chart-row">
                        <div class="chart-card">
                            <div class="card-header">
                                <h3>热门文件</h3>
                            </div>
                            <div class="card-body">
                                <div class="table-container">
                                    <table class="table" id="popularFilesTable">
                                        <thead>
                                            <tr>
                                                <th>文件名</th>
                                                <th>下载次数</th>
                                                <th>文件大小</th>
                                                <th>上传时间</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="chart-card">
                            <div class="card-header">
                                <h3>活跃用户</h3>
                            </div>
                            <div class="card-body">
                                <div class="table-container">
                                    <table class="table" id="activeUsersTable">
                                        <thead>
                                            <tr>
                                                <th>用户名</th>
                                                <th>操作次数</th>
                                                <th>下载次数</th>
                                                <th>最后活跃</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 详细分析 -->
                <div class="detailed-analysis">
                    <div class="card">
                        <div class="card-header">
                            <h3>操作类型分布</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="actionTypeChart"></canvas>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3>文件类型分布</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="fileTypeChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- 导出报告 -->
                <div class="export-section">
                    <button type="button" class="btn btn-success" onclick="exportReport()">导出报告</button>
                    <button type="button" class="btn btn-info" onclick="generateReport()">生成详细报告</button>
                </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- 加载提示 -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
        <p>正在加载数据...</p>
    </div>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script th:src="@{/static/js/analytics.js}"></script>
</body>
</html>
