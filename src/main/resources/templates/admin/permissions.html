<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限管理 - LLB文件管理系统</title>
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <link rel="stylesheet" th:href="@{/static/css/dashboard.css}">
    <link rel="stylesheet" th:href="@{/static/css/admin.css}">
    <link rel="stylesheet" th:href="@{/static/css/permissions.css}">
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>LLB管理面板</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="/admin" class="nav-link">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">管理面板</span>
                    </a></li>
                    <li><a href="/admin/users" class="nav-link">
                        <span class="nav-icon">👥</span>
                        <span class="nav-text">用户管理</span>
                    </a></li>
                    <li><a href="/admin/stats" class="nav-link">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">统计分析</span>
                    </a></li>
                    <li><a href="/admin/permissions" class="nav-link active">
                        <span class="nav-icon">🔐</span>
                        <span class="nav-text">权限管理</span>
                    </a></li>
                    <li><a href="/admin/ip-management" class="nav-link">
                        <span class="nav-icon">🛡️</span>
                        <span class="nav-text">IP管理</span>
                    </a></li>
                    <li><a href="/admin/system" class="nav-link">
                        <span class="nav-icon">⚙️</span>
                        <span class="nav-text">系统设置</span>
                    </a></li>
                    <li class="nav-divider"></li>
                    <li><a href="/dashboard" class="nav-link">
                        <span class="nav-icon">🏠</span>
                        <span class="nav-text">返回用户界面</span>
                    </a></li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>权限管理</h1>
                    <p class="page-subtitle">管理用户对文件和文件夹的访问权限</p>
                </div>
                <div class="top-bar-right">
                    <div class="user-info">
                        <div class="user-menu">
                            <button class="user-avatar" onclick="toggleUserMenu()">
                                <img th:src="${user != null and user.avatar != null ? user.avatar : '/static/images/default-avatar.svg'}" alt="头像">
                            </button>
                            <div class="user-dropdown" id="userDropdown">
                                <a href="/profile">个人设置</a>
                                <a href="#" onclick="logout()">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 管理面板内容区 -->
            <div class="admin-dashboard-content">
                
                <!-- 权限统计 -->
                <div class="permission-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="icon-permission"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalPermissions">-</h3>
                            <p>总权限数</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="icon-user"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="usersWithPermissions">-</h3>
                            <p>有权限用户</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="icon-file"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="filesWithPermissions">-</h3>
                            <p>有权限文件</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="icon-folder"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="foldersWithPermissions">-</h3>
                            <p>有权限文件夹</p>
                        </div>
                    </div>
                </div>
                
                <!-- 操作工具栏 -->
                <div class="toolbar">
                    <button type="button" class="btn btn-primary" onclick="showGrantPermissionModal()">
                        <i class="icon-plus"></i> 授予权限
                    </button>
                    <button type="button" class="btn btn-warning" onclick="showBatchGrantModal()">
                        <i class="icon-batch"></i> 批量授权
                    </button>
                    <button type="button" class="btn btn-info" onclick="showPermissionReportModal()">
                        <i class="icon-report"></i> 权限报告
                    </button>
                    <button type="button" class="btn btn-danger" onclick="checkPermissionConflicts()">
                        <i class="icon-warning"></i> 检查冲突
                    </button>
                </div>
                
                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="search-group">
                        <input type="text" id="searchUser" placeholder="搜索用户..." class="form-control">
                        <button type="button" class="btn btn-secondary" onclick="searchPermissions()">搜索</button>
                    </div>
                    
                    <div class="filter-group">
                        <select id="resourceTypeFilter" class="form-control">
                            <option value="">所有资源类型</option>
                            <option value="FILE">文件</option>
                            <option value="FOLDER">文件夹</option>
                        </select>
                        
                        <select id="permissionTypeFilter" class="form-control">
                            <option value="">所有权限类型</option>
                            <option value="read">读取</option>
                            <option value="write">写入</option>
                            <option value="delete">删除</option>
                            <option value="share">分享</option>
                        </select>
                        
                        <button type="button" class="btn btn-secondary" onclick="applyFilters()">应用筛选</button>
                        <button type="button" class="btn btn-light" onclick="clearFilters()">清除筛选</button>
                    </div>
                </div>
                
                <!-- 权限列表 -->
                <div class="permissions-table-container">
                    <table class="table permissions-table" id="permissionsTable">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th>用户</th>
                                <th>资源类型</th>
                                <th>资源名称</th>
                                <th>权限类型</th>
                                <th>授权者</th>
                                <th>授权时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        <span>共 <span id="totalCount">0</span> 条记录</span>
                    </div>
                    <div class="pagination" id="pagination">
                    </div>
                </div>
                
                <!-- 批量操作 -->
                <div class="batch-operations" id="batchOperations" style="display: none;">
                    <button type="button" class="btn btn-danger" onclick="batchRevokePermissions()">
                        <i class="icon-delete"></i> 批量撤销
                    </button>
                    <button type="button" class="btn btn-info" onclick="batchCopyPermissions()">
                        <i class="icon-copy"></i> 批量复制
                    </button>
                    <span class="selected-count">已选择 <span id="selectedCount">0</span> 项</span>
                </div>
            </div>
        </main>
    </div>
    
    <!-- 授予权限模态框 -->
    <div id="grantPermissionModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>授予权限</h3>
                <button type="button" class="close" onclick="closeModal('grantPermissionModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="grantPermissionForm">
                    <div class="form-group">
                        <label for="grantUserId">用户:</label>
                        <select id="grantUserId" class="form-control" required>
                            <option value="">请选择用户</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="grantResourceType">资源类型:</label>
                        <select id="grantResourceType" class="form-control" required onchange="loadResources()">
                            <option value="">请选择资源类型</option>
                            <option value="FILE">文件</option>
                            <option value="FOLDER">文件夹</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="grantResourceId">资源:</label>
                        <select id="grantResourceId" class="form-control" required>
                            <option value="">请先选择资源类型</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="grantPermissionType">权限类型:</label>
                        <select id="grantPermissionType" class="form-control" required>
                            <option value="">请选择权限类型</option>
                            <option value="read">读取</option>
                            <option value="write">写入</option>
                            <option value="delete">删除</option>
                            <option value="share">分享</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('grantPermissionModal')">取消</button>
                <button type="button" class="btn btn-primary" onclick="grantPermission()">授予权限</button>
            </div>
        </div>
    </div>
    
    <!-- 批量授权模态框 -->
    <div id="batchGrantModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>批量授权</h3>
                <button type="button" class="close" onclick="closeModal('batchGrantModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="batchGrantForm">
                    <div class="form-group">
                        <label for="batchUserIds">用户 (可多选):</label>
                        <select id="batchUserIds" class="form-control" multiple required>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="batchResourceType">资源类型:</label>
                        <select id="batchResourceType" class="form-control" required onchange="loadBatchResources()">
                            <option value="">请选择资源类型</option>
                            <option value="FILE">文件</option>
                            <option value="FOLDER">文件夹</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="batchResourceId">资源:</label>
                        <select id="batchResourceId" class="form-control" required>
                            <option value="">请先选择资源类型</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="batchPermissionTypes">权限类型 (可多选):</label>
                        <div class="checkbox-group">
                            <label><input type="checkbox" value="read"> 读取</label>
                            <label><input type="checkbox" value="write"> 写入</label>
                            <label><input type="checkbox" value="delete"> 删除</label>
                            <label><input type="checkbox" value="share"> 分享</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('batchGrantModal')">取消</button>
                <button type="button" class="btn btn-primary" onclick="batchGrantPermissions()">批量授权</button>
            </div>
        </div>
    </div>
    
    <!-- 权限报告模态框 -->
    <div id="permissionReportModal" class="modal" style="display: none;">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>权限报告</h3>
                <button type="button" class="close" onclick="closeModal('permissionReportModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div id="permissionReportContent">
                    <!-- 报告内容将通过JavaScript动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('permissionReportModal')">关闭</button>
                <button type="button" class="btn btn-success" onclick="exportPermissionReport()">导出报告</button>
            </div>
        </div>
    </div>
    
    <script th:src="@{/static/js/permissions.js}"></script>
</body>
</html>
