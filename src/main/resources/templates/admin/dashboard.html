<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员面板 - LLB文件管理系统</title>
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <link rel="stylesheet" th:href="@{/static/css/dashboard.css}">
    <link rel="stylesheet" th:href="@{/static/css/admin.css}">
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>LLB管理面板</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="/admin" class="nav-link active">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">管理面板</span>
                    </a></li>
                    <li><a href="/admin/users" class="nav-link">
                        <span class="nav-icon">👥</span>
                        <span class="nav-text">用户管理</span>
                    </a></li>
                    <li><a href="/admin/stats" class="nav-link">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">统计分析</span>
                    </a></li>
                    <li><a href="/admin/ip-management" class="nav-link">
                        <span class="nav-icon">🛡️</span>
                        <span class="nav-text">IP管理</span>
                    </a></li>
                    <li><a href="/admin/system" class="nav-link">
                        <span class="nav-icon">⚙️</span>
                        <span class="nav-text">系统设置</span>
                    </a></li>
                    <li class="nav-divider"></li>
                    <li><a href="/dashboard" class="nav-link">
                        <span class="nav-icon">🏠</span>
                        <span class="nav-text">返回用户界面</span>
                    </a></li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>管理员面板</h1>
                    <p class="page-subtitle">系统概览与管理</p>
                </div>
                <div class="top-bar-right">
                    <div class="user-info">
                        <div class="user-menu">
                            <button class="user-avatar" onclick="toggleUserMenu()">
                                <img th:src="${user != null and user.avatar != null ? user.avatar : '/static/images/default-avatar.svg'}" alt="头像">
                            </button>
                            <div class="user-dropdown" id="userDropdown">
                                <a href="/profile">个人设置</a>
                                <a href="#" onclick="logout()">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 管理面板内容区 -->
            <div class="admin-dashboard-content">
                <!-- 统计卡片 -->
                <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-content">
                        <h3 th:text="${stats.totalUsers}">0</h3>
                        <p>总用户数</p>
                        <span class="stat-change positive">
                            +<span th:text="${stats.todayRegistrations}">0</span> 今日新增
                        </span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📁</div>
                    <div class="stat-content">
                        <h3 th:text="${stats.totalFiles}">0</h3>
                        <p>总文件数</p>
                        <span class="stat-change positive">
                            +<span th:text="${stats.todayUploads}">0</span> 今日上传
                        </span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">💾</div>
                    <div class="stat-content">
                        <h3 id="totalSizeText">0 GB</h3>
                        <p>总存储空间</p>
                        <span class="stat-change neutral">
                            <span th:text="${stats.deletedFiles}">0</span> 回收站文件
                        </span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <h3 th:text="${stats.activeUsers}">0</h3>
                        <p>活跃用户</p>
                        <span class="stat-change negative">
                            <span th:text="${stats.disabledUsers}">0</span> 已禁用
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="admin-content">
                <!-- 最近文件 -->
                <div class="content-section">
                    <div class="section-header">
                        <h2>最近上传文件</h2>
                        <a href="/admin/files" class="btn btn-outline">查看全部</a>
                    </div>
                    <div class="recent-files-table">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>文件名</th>
                                    <th>用户</th>
                                    <th>大小</th>
                                    <th>上传时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="file : ${recentFiles}">
                                    <td>
                                        <div class="file-info">
                                            <span class="file-icon" th:text="${@fileIconUtil.getIcon(file.originalName)}">📄</span>
                                            <span th:text="${file.name}">文件名</span>
                                        </div>
                                    </td>
                                    <td th:text="${file.userId}">用户ID</td>
                                    <td th:text="${@fileSizeUtil.format(file.fileSize)}">文件大小</td>
                                    <td th:text="${#temporals.format(file.createdTime, 'MM-dd HH:mm')}">上传时间</td>
                                    <td>
                                        <button class="action-btn" th:onclick="'viewFile(' + ${file.id} + ')'">查看</button>
                                        <button class="action-btn delete-btn" th:onclick="'deleteFile(' + ${file.id} + ')'">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 存储使用排行 -->
                <div class="content-section">
                    <div class="section-header">
                        <h2>存储使用排行</h2>
                        <a href="/admin/users" class="btn btn-outline">用户管理</a>
                    </div>
                    <div class="storage-ranking">
                        <div class="ranking-item" th:each="user, iterStat : ${storageRanking}">
                            <div class="ranking-number" th:text="${iterStat.index + 1}">1</div>
                            <div class="user-info">
                                <div class="user-name" th:text="${user.nickname}">用户昵称</div>
                                <div class="user-meta" th:text="${user.username}">用户名</div>
                            </div>
                            <div class="storage-info">
                                <div class="storage-used" th:text="${@fileSizeUtil.format(user.storageUsed)}">已使用</div>
                                <div class="storage-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" th:style="'width: ' + ${user.storageUsed * 100 / user.storageLimit} + '%'"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Toast 提示 -->
    <div id="toast" class="toast"></div>
    
    <script th:src="@{/static/js/common.js}"></script>
    <script th:src="@{/static/js/admin-dashboard.js}"></script>
    <script th:inline="javascript">
        // 传递统计数据到前端
        window.adminStats = /*[[${stats}]]*/ {};
    </script>
</body>
</html>
