<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计分析 - LLB文件管理系统</title>
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <link rel="stylesheet" th:href="@{/static/css/dashboard.css}">
    <link rel="stylesheet" th:href="@{/static/css/admin.css}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>LLB管理面板</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="/admin" class="nav-link">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">管理面板</span>
                    </a></li>
                    <li><a href="/admin/users" class="nav-link">
                        <span class="nav-icon">👥</span>
                        <span class="nav-text">用户管理</span>
                    </a></li>
                    <li><a href="/admin/stats" class="nav-link active">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">统计分析</span>
                    </a></li>
                    <li><a href="/admin/ip-management" class="nav-link">
                        <span class="nav-icon">🛡️</span>
                        <span class="nav-text">IP管理</span>
                    </a></li>
                    <li><a href="/admin/system" class="nav-link">
                        <span class="nav-icon">⚙️</span>
                        <span class="nav-text">系统设置</span>
                    </a></li>
                    <li class="nav-divider"></li>
                    <li><a href="/dashboard" class="nav-link">
                        <span class="nav-icon">🏠</span>
                        <span class="nav-text">返回用户界面</span>
                    </a></li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>统计分析</h1>
                    <p class="page-subtitle">系统数据统计与分析报告</p>
                </div>
                <div class="top-bar-right">
                    <div class="toolbar">
                        <select id="timeRange" class="filter-select" onchange="changeTimeRange()">
                            <option value="7">最近7天</option>
                            <option value="30" selected>最近30天</option>
                            <option value="90">最近90天</option>
                            <option value="365">最近一年</option>
                        </select>
                        <button class="btn btn-outline" onclick="exportReport()">
                            <span class="nav-icon">📤</span>
                            导出报告
                        </button>
                        <button class="btn btn-outline" onclick="refreshStats()">
                            <span class="nav-icon">🔄</span>
                            刷新数据
                        </button>
                    </div>
                    <div class="user-info">
                        <div class="user-menu">
                            <button class="user-avatar" onclick="toggleUserMenu()">
                                <img th:src="${user != null and user.avatar != null ? user.avatar : '/static/images/default-avatar.svg'}" alt="头像">
                            </button>
                            <div class="user-dropdown" id="userDropdown">
                                <a href="/profile">个人设置</a>
                                <a href="#" onclick="logout()">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 管理面板内容区 -->
            <div class="admin-dashboard-content">
                <!-- 概览统计 -->
            <div class="stats-overview">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📈</div>
                        <div class="stat-content">
                            <h3 id="totalUploads">0</h3>
                            <p>总上传量</p>
                            <span class="stat-change positive" id="uploadsChange">+0 今日</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">📥</div>
                        <div class="stat-content">
                            <h3 id="totalDownloads">0</h3>
                            <p>总下载量</p>
                            <span class="stat-change positive" id="downloadsChange">+0 今日</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">👁️</div>
                        <div class="stat-content">
                            <h3 id="totalViews">0</h3>
                            <p>总访问量</p>
                            <span class="stat-change positive" id="viewsChange">+0 今日</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">🔗</div>
                        <div class="stat-content">
                            <h3 id="totalShares">0</h3>
                            <p>分享链接</p>
                            <span class="stat-change neutral" id="sharesChange">0 活跃</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 图表区域 -->
            <div class="charts-container">
                <!-- 上传下载趋势 -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h2>上传下载趋势</h2>
                        <div class="chart-legend">
                            <span class="legend-item">
                                <span class="legend-color upload"></span>
                                上传量
                            </span>
                            <span class="legend-item">
                                <span class="legend-color download"></span>
                                下载量
                            </span>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="uploadDownloadChart"></canvas>
                    </div>
                </div>
                
                <!-- 文件类型分布 -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h2>文件类型分布</h2>
                    </div>
                    <div class="chart-container">
                        <canvas id="fileTypeChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 详细统计 -->
            <div class="detailed-stats">
                <!-- 用户活跃度 -->
                <div class="content-section">
                    <div class="section-header">
                        <h2>用户活跃度</h2>
                        <button class="btn btn-outline" onclick="viewUserDetails()">查看详情</button>
                    </div>
                    <div class="activity-stats">
                        <div class="activity-item">
                            <div class="activity-label">日活跃用户</div>
                            <div class="activity-value" id="dailyActiveUsers">0</div>
                            <div class="activity-trend positive" id="dailyActiveTrend">+0%</div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-label">周活跃用户</div>
                            <div class="activity-value" id="weeklyActiveUsers">0</div>
                            <div class="activity-trend positive" id="weeklyActiveTrend">+0%</div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-label">月活跃用户</div>
                            <div class="activity-value" id="monthlyActiveUsers">0</div>
                            <div class="activity-trend positive" id="monthlyActiveTrend">+0%</div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-label">平均会话时长</div>
                            <div class="activity-value" id="avgSessionTime">0分钟</div>
                            <div class="activity-trend neutral" id="sessionTimeTrend">-</div>
                        </div>
                    </div>
                </div>
                
                <!-- 存储统计 -->
                <div class="content-section">
                    <div class="section-header">
                        <h2>存储统计</h2>
                        <button class="btn btn-outline" onclick="viewStorageDetails()">查看详情</button>
                    </div>
                    <div class="storage-stats">
                        <div class="storage-overview">
                            <div class="storage-item">
                                <div class="storage-label">总存储空间</div>
                                <div class="storage-value" id="totalStorage">0 GB</div>
                                <div class="storage-bar">
                                    <div class="storage-used" id="storageUsedBar" style="width: 0%"></div>
                                </div>
                                <div class="storage-info">
                                    已使用 <span id="usedStorage">0 GB</span> / 
                                    可用 <span id="availableStorage">∞</span>
                                </div>
                            </div>
                        </div>
                        <div class="storage-breakdown">
                            <div class="breakdown-item">
                                <span class="breakdown-type">图片</span>
                                <span class="breakdown-size" id="imageStorage">0 GB</span>
                                <span class="breakdown-percent" id="imagePercent">0%</span>
                            </div>
                            <div class="breakdown-item">
                                <span class="breakdown-type">视频</span>
                                <span class="breakdown-size" id="videoStorage">0 GB</span>
                                <span class="breakdown-percent" id="videoPercent">0%</span>
                            </div>
                            <div class="breakdown-item">
                                <span class="breakdown-type">文档</span>
                                <span class="breakdown-size" id="documentStorage">0 GB</span>
                                <span class="breakdown-percent" id="documentPercent">0%</span>
                            </div>
                            <div class="breakdown-item">
                                <span class="breakdown-type">其他</span>
                                <span class="breakdown-size" id="otherStorage">0 GB</span>
                                <span class="breakdown-percent" id="otherPercent">0%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 热门文件排行 -->
            <div class="content-section">
                <div class="section-header">
                    <h2>热门文件排行</h2>
                    <div class="ranking-tabs">
                        <button class="ranking-tab active" onclick="switchRanking('downloads')">下载排行</button>
                        <button class="ranking-tab" onclick="switchRanking('views')">浏览排行</button>
                        <button class="ranking-tab" onclick="switchRanking('shares')">分享排行</button>
                    </div>
                </div>
                <div class="ranking-list" id="rankingList">
                    <!-- 排行榜内容将通过JavaScript加载 -->
                </div>
                </div>  
            </div>
        </main>
    </div>
    
    <!-- Toast 提示 -->
    <div id="toast" class="toast"></div>
    
    <script th:src="@{/static/js/common.js}"></script>
    <script th:src="@{/static/js/admin-stats.js}"></script>
</body>
</html>
