<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP管理 - LLB文件管理系统</title>
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <link rel="stylesheet" th:href="@{/static/css/dashboard.css}">
    <link rel="stylesheet" th:href="@{/static/css/admin.css}">
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>LLB管理面板</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="/admin" class="nav-link">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">管理面板</span>
                    </a></li>
                    <li><a href="/admin/users" class="nav-link">
                        <span class="nav-icon">👥</span>
                        <span class="nav-text">用户管理</span>
                    </a></li>
                    <li><a href="/admin/stats" class="nav-link">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">统计分析</span>
                    </a></li>
                    <li><a href="/admin/ip-management" class="nav-link active">
                        <span class="nav-icon">🛡️</span>
                        <span class="nav-text">IP管理</span>
                    </a></li>
                    <li><a href="/admin/system" class="nav-link">
                        <span class="nav-icon">⚙️</span>
                        <span class="nav-text">系统设置</span>
                    </a></li>
                    <li class="nav-divider"></li>
                    <li><a href="/dashboard" class="nav-link">
                        <span class="nav-icon">🏠</span>
                        <span class="nav-text">返回用户界面</span>
                    </a></li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>IP管理</h1>
                    <p class="page-subtitle">管理IP黑白名单，控制访问权限</p>
                </div>
                <div class="top-bar-right">
                    <div class="user-info">
                        <div class="user-menu">
                            <button class="user-avatar" onclick="toggleUserMenu()">
                                <img th:src="${user != null and user.avatar != null ? user.avatar : '/static/images/default-avatar.svg'}" alt="头像">
                            </button>
                            <div class="user-dropdown" id="userDropdown">
                                <a href="/profile">个人设置</a>
                                <a href="#" onclick="logout()">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 管理面板内容区 -->
            <div class="admin-dashboard-content">
                <!-- IP统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">🚫</div>
                    <div class="stat-content">
                        <h3 th:text="${ipStats.blacklistCount}">0</h3>
                        <p>黑名单IP</p>
                        <span class="stat-change neutral">访问被拒绝</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <h3 th:text="${ipStats.whitelistCount}">0</h3>
                        <p>白名单IP</p>
                        <span class="stat-change positive">优先通过</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">🛡️</div>
                    <div class="stat-content">
                        <h3 id="blockedToday">0</h3>
                        <p>今日拦截</p>
                        <span class="stat-change negative">恶意访问</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <h3 id="totalRequests">0</h3>
                        <p>总访问量</p>
                        <span class="stat-change neutral">今日统计</span>
                    </div>
                </div>
            </div>
            
            <!-- IP管理标签页 -->
            <div class="ip-management-tabs">
                <div class="tab-headers">
                    <button class="tab-header active" onclick="switchTab('blacklist')">
                        <span class="nav-icon">🚫</span>
                        IP黑名单
                    </button>
                    <button class="tab-header" onclick="switchTab('whitelist')">
                        <span class="nav-icon">✅</span>
                        IP白名单
                    </button>
                </div>
                
                <!-- 黑名单标签页 -->
                <div class="tab-content active" id="blacklistTab">
                    <div class="content-section">
                        <div class="section-header">
                            <h2>IP黑名单管理</h2>
                            <div class="toolbar">
                                <button class="btn btn-primary" onclick="showAddBlacklistModal()">
                                    <span class="nav-icon">➕</span>
                                    添加黑名单
                                </button>
                                <button class="btn btn-outline" onclick="batchDeleteBlacklist()">
                                    <span class="nav-icon">🗑️</span>
                                    批量删除
                                </button>
                                <button class="btn btn-outline" onclick="batchQueryIpLocations()">
                                    <span class="nav-icon">🌍</span>
                                    查询位置
                                </button>
                                <button class="btn btn-outline" onclick="clearLocationCache()">
                                    <span class="nav-icon">🔄</span>
                                    清理缓存
                                </button>
                            </div>
                        </div>
                        
                        <!-- 搜索筛选 -->
                        <div class="search-filters">
                            <div class="search-input-group">
                                <input type="text" id="blacklistIpSearch" placeholder="搜索IP地址..." class="search-input">
                                <input type="text" id="blacklistReasonSearch" placeholder="搜索原因..." class="search-input">
                                <button class="btn btn-outline" onclick="searchBlacklist()">搜索</button>
                                <button class="btn btn-outline" onclick="resetBlacklistSearch()">重置</button>
                            </div>
                        </div>
                        
                        <!-- 黑名单列表 -->
                        <div class="ip-table">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="selectAllBlacklist" onchange="toggleSelectAllBlacklist()">
                                        </th>
                                        <th>IP地址/网段</th>
                                        <th>封禁原因</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>过期时间</th>
                                        <th>操作员</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="blacklistTableBody">
                                    <tr>
                                        <td colspan="8" class="loading-cell">
                                            <div class="loading">加载中...</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <div class="pagination" id="blacklistPagination"></div>
                    </div>
                </div>
                
                <!-- 白名单标签页 -->
                <div class="tab-content" id="whitelistTab">
                    <div class="content-section">
                        <div class="section-header">
                            <h2>IP白名单管理</h2>
                            <div class="toolbar">
                                <button class="btn btn-primary" onclick="showAddWhitelistModal()">
                                    <span class="nav-icon">➕</span>
                                    添加白名单
                                </button>
                                <button class="btn btn-outline" onclick="batchDeleteWhitelist()">
                                    <span class="nav-icon">🗑️</span>
                                    批量删除
                                </button>
                                <button class="btn btn-outline" onclick="batchQueryIpLocations()">
                                    <span class="nav-icon">🌍</span>
                                    查询位置
                                </button>
                                <button class="btn btn-outline" onclick="clearLocationCache()">
                                    <span class="nav-icon">🔄</span>
                                    清理缓存
                                </button>
                            </div>
                        </div>
                        
                        <!-- 搜索筛选 -->
                        <div class="search-filters">
                            <div class="search-input-group">
                                <input type="text" id="whitelistIpSearch" placeholder="搜索IP地址..." class="search-input">
                                <input type="text" id="whitelistDescSearch" placeholder="搜索描述..." class="search-input">
                                <button class="btn btn-outline" onclick="searchWhitelist()">搜索</button>
                                <button class="btn btn-outline" onclick="resetWhitelistSearch()">重置</button>
                            </div>
                        </div>
                        
                        <!-- 白名单列表 -->
                        <div class="ip-table">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="selectAllWhitelist" onchange="toggleSelectAllWhitelist()">
                                        </th>
                                        <th>IP地址/网段</th>
                                        <th>描述说明</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>过期时间</th>
                                        <th>操作员</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="whitelistTableBody">
                                    <tr>
                                        <td colspan="8" class="loading-cell">
                                            <div class="loading">加载中...</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <div class="pagination" id="whitelistPagination"></div>
                    </div>
                </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- 添加黑名单模态框 -->
    <div class="modal" id="addBlacklistModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加IP黑名单</h3>
                <button class="modal-close" onclick="hideAddBlacklistModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addBlacklistForm">
                    <div class="form-group">
                        <label for="blacklistIpAddress">IP地址</label>
                        <input type="text" id="blacklistIpAddress" name="ipAddress" required 
                               placeholder="例如: *********** 或 ***********/24">
                        <small class="form-help">支持单个IP或CIDR格式的IP段</small>
                    </div>
                    <div class="form-group">
                        <label for="blacklistReason">原因</label>
                        <textarea id="blacklistReason" name="reason" required 
                                  placeholder="请输入加入黑名单的原因..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="hideAddBlacklistModal()">取消</button>
                <button class="btn btn-primary" onclick="addToBlacklist()">添加</button>
            </div>
        </div>
    </div>
    
    <!-- 添加白名单模态框 -->
    <div class="modal" id="addWhitelistModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加IP白名单</h3>
                <button class="modal-close" onclick="hideAddWhitelistModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addWhitelistForm">
                    <div class="form-group">
                        <label for="whitelistIpAddress">IP地址</label>
                        <input type="text" id="whitelistIpAddress" name="ipAddress" required 
                               placeholder="例如: *********** 或 ***********/24">
                        <small class="form-help">支持单个IP或CIDR格式的IP段</small>
                    </div>
                    <div class="form-group">
                        <label for="whitelistDescription">描述</label>
                        <textarea id="whitelistDescription" name="description" 
                                  placeholder="请输入白名单描述..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="hideAddWhitelistModal()">取消</button>
                <button class="btn btn-primary" onclick="addToWhitelist()">添加</button>
            </div>
        </div>
    </div>
    
    <!-- Toast 提示 -->
    <div id="toast" class="toast"></div>
    
    <script th:src="@{/static/js/common.js}"></script>
    <script th:src="@{/static/js/admin-ip.js}"></script>
    <script th:inline="javascript">
        // 传递IP统计数据到前端
        window.ipStats = /*[[${ipStats}]]*/ {};
    </script>
</body>
</html>
