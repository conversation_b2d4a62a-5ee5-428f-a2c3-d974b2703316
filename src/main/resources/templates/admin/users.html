<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - LLB文件管理系统</title>
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <link rel="stylesheet" th:href="@{/static/css/dashboard.css}">
    <link rel="stylesheet" th:href="@{/static/css/admin.css}">
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>LLB管理面板</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="/admin" class="nav-link">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">管理面板</span>
                    </a></li>
                    <li><a href="/admin/users" class="nav-link active">
                        <span class="nav-icon">👥</span>
                        <span class="nav-text">用户管理</span>
                    </a></li>
                    <li><a href="/admin/stats" class="nav-link">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">统计分析</span>
                    </a></li>
                    <li><a href="/admin/ip-management" class="nav-link">
                        <span class="nav-icon">🛡️</span>
                        <span class="nav-text">IP管理</span>
                    </a></li>
                    <li><a href="/admin/system" class="nav-link">
                        <span class="nav-icon">⚙️</span>
                        <span class="nav-text">系统设置</span>
                    </a></li>
                    <li class="nav-divider"></li>
                    <li><a href="/dashboard" class="nav-link">
                        <span class="nav-icon">🏠</span>
                        <span class="nav-text">返回用户界面</span>
                    </a></li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>用户管理</h1>
                    <p class="page-subtitle">管理系统用户和权限</p>
                </div>
                <div class="top-bar-right">
                    <div class="toolbar">
                        <button class="btn btn-primary" onclick="showCreateUserModal()">
                            <span class="nav-icon">➕</span>
                            新建用户
                        </button>
                        <button class="btn btn-outline" onclick="exportUsers()">
                            <span class="nav-icon">📤</span>
                            导出用户
                        </button>
                    </div>
                    <div class="user-info">
                        <div class="user-menu">
                            <button class="user-avatar" onclick="toggleUserMenu()">
                                <img th:src="${user != null and user.avatar != null ? user.avatar : '/static/images/default-avatar.svg'}" alt="头像">
                            </button>
                            <div class="user-dropdown" id="userDropdown">
                                <a href="/profile">个人设置</a>
                                <a href="#" onclick="logout()">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 管理面板内容区 -->
            <div class="admin-dashboard-content">
                <!-- 搜索和筛选 -->
            <div class="search-filters">
                <div class="search-input-group">
                    <input type="text" id="searchUsername" placeholder="搜索用户名..." class="search-input">
                    <input type="text" id="searchEmail" placeholder="搜索邮箱..." class="search-input">
                    <button class="btn btn-outline" onclick="searchUsers()">搜索</button>
                    <button class="btn btn-outline" onclick="resetSearch()">重置</button>
                </div>
                <div class="filter-group">
                    <select id="roleFilter" class="filter-select">
                        <option value="">所有角色</option>
                        <option value="ADMIN">管理员</option>
                        <option value="USER">普通用户</option>
                    </select>
                    <select id="statusFilter" class="filter-select">
                        <option value="">所有状态</option>
                        <option value="ACTIVE">正常</option>
                        <option value="DISABLED">禁用</option>
                        <option value="LOCKED">锁定</option>
                    </select>
                </div>
            </div>
            
            <!-- 用户列表 -->
            <div class="content-section">
                <div class="section-header">
                    <h2>用户列表</h2>
                    <div class="list-controls">
                        <span id="userCount">总计: 0 个用户</span>
                        <div class="pagination-info">
                            <span id="pageInfo">第 1 页，共 1 页</span>
                        </div>
                    </div>
                </div>
                
                <div class="users-table">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th>用户信息</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>存储使用</th>
                                <th>注册时间</th>
                                <th>最后登录</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <tr>
                                <td colspan="8" class="loading-cell">
                                    <div class="loading">加载中...</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="pagination" id="pagination">
                    <!-- 分页按钮将通过JavaScript生成 -->
                </div>
            </div>
            
            <!-- 批量操作 -->
            <div class="batch-actions" id="batchActions" style="display: none;">
                <div class="batch-info">
                    已选择 <span id="selectedCount">0</span> 个用户
                </div>
                <div class="batch-buttons">
                    <button class="btn btn-outline" onclick="batchUpdateStatus('ACTIVE')">批量启用</button>
                    <button class="btn btn-outline" onclick="batchUpdateStatus('DISABLED')">批量禁用</button>
                    <button class="btn btn-danger" onclick="batchDeleteUsers()">批量删除</button>
                    <button class="btn btn-outline" onclick="clearSelection()">取消选择</button>
                </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- 创建用户模态框 -->
    <div class="modal" id="createUserModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新建用户</h3>
                <button class="modal-close" onclick="hideCreateUserModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="createUserForm">
                    <div class="form-group">
                        <label for="newUsername">用户名</label>
                        <input type="text" id="newUsername" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="newPassword">密码</label>
                        <input type="password" id="newPassword" name="password" required>
                    </div>
                    <div class="form-group">
                        <label for="newEmail">邮箱</label>
                        <input type="email" id="newEmail" name="email">
                    </div>
                    <div class="form-group">
                        <label for="newNickname">昵称</label>
                        <input type="text" id="newNickname" name="nickname">
                    </div>
                    <div class="form-group">
                        <label for="newRole">角色</label>
                        <select id="newRole" name="role" required>
                            <option value="USER">普通用户</option>
                            <option value="ADMIN">管理员</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="newStorageLimit">存储限制 (GB)</label>
                        <input type="number" id="newStorageLimit" name="storageLimit" value="1.5" step="0.1" min="0">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="hideCreateUserModal()">取消</button>
                <button class="btn btn-primary" onclick="createUser()">创建</button>
            </div>
        </div>
    </div>
    
    <!-- 编辑用户模态框 -->
    <div class="modal" id="editUserModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑用户</h3>
                <button class="modal-close" onclick="hideEditUserModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId" name="userId">
                    <div class="form-group">
                        <label for="editUsername">用户名</label>
                        <input type="text" id="editUsername" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="editEmail">邮箱</label>
                        <input type="email" id="editEmail" name="email">
                    </div>
                    <div class="form-group">
                        <label for="editNickname">昵称</label>
                        <input type="text" id="editNickname" name="nickname">
                    </div>
                    <div class="form-group">
                        <label for="editRole">角色</label>
                        <select id="editRole" name="role" required>
                            <option value="USER">普通用户</option>
                            <option value="ADMIN">管理员</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editStatus">状态</label>
                        <select id="editStatus" name="status" required>
                            <option value="ACTIVE">正常</option>
                            <option value="DISABLED">禁用</option>
                            <option value="LOCKED">锁定</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editStorageLimit">存储限制 (GB)</label>
                        <input type="number" id="editStorageLimit" name="storageLimit" step="0.1" min="0">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="hideEditUserModal()">取消</button>
                <button class="btn btn-primary" onclick="updateUser()">保存</button>
            </div>
        </div>
    </div>
    
    <!-- Toast 提示 -->
    <div id="toast" class="toast"></div>
    
    <script th:src="@{/static/js/common.js}"></script>
    <script th:src="@{/static/js/admin-users.js}"></script>
</body>
</html>
