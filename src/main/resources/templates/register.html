<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - LLB文件管理系统</title>
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <link rel="stylesheet" th:href="@{/static/css/auth.css}">
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>LLB文件管理系统</h1>
                <p>创建您的账户，开始使用文件管理服务</p>
            </div>
            
            <form id="registerForm" class="auth-form">
                <div class="form-group">
                    <label for="username">用户名 *</label>
                    <input type="text" id="username" name="username" required>
                    <small class="form-help">用户名长度为3-20个字符</small>
                </div>
                
                <div class="form-group">
                    <label for="email">邮箱</label>
                    <input type="email" id="email" name="email">
                </div>
                
                <div class="form-group">
                    <label for="nickname">昵称</label>
                    <input type="text" id="nickname" name="nickname">
                    <small class="form-help">不填写将使用用户名作为昵称</small>
                </div>
                
                <div class="form-group">
                    <label for="password">密码 *</label>
                    <input type="password" id="password" name="password" required>
                    <small class="form-help">密码长度至少6个字符</small>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">确认密码 *</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required>
                </div>
                
                <button type="submit" class="btn btn-primary btn-full" id="registerBtn">
                    <span class="btn-text">注册</span>
                    <span class="btn-loading" style="display: none;">注册中...</span>
                </button>
            </form>
            
            <div class="auth-footer">
                <p>已有账户？ <a th:href="@{/login}">立即登录</a></p>
            </div>
        </div>
    </div>
    
    <!-- Toast 提示 -->
    <div id="toast" class="toast"></div>
    
    <script th:src="@{/static/js/common.js}"></script>
    <script th:src="@{/static/js/auth.js}"></script>
</body>
</html>
