<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分享管理 - LLB文件管理系统</title>
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <link rel="stylesheet" th:href="@{/static/css/dashboard.css}">
    <link rel="stylesheet" th:href="@{/static/css/files.css}">
    <link rel="stylesheet" th:href="@{/static/css/shares.css}">
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>LLB文件系统</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="/dashboard" class="nav-link">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">仪表板</span>
                    </a></li>
                    <li><a href="/files" class="nav-link">
                        <span class="nav-icon">📁</span>
                        <span class="nav-text">文件管理</span>
                    </a></li>
                    <li><a href="/upload" class="nav-link">
                        <span class="nav-icon">⬆️</span>
                        <span class="nav-text">文件上传</span>
                    </a></li>
                    <li><a href="/shares" class="nav-link active">
                        <span class="nav-icon">🔗</span>
                        <span class="nav-text">分享管理</span>
                    </a></li>
                    <li><a href="/recycle" class="nav-link">
                        <span class="nav-icon">🗑️</span>
                        <span class="nav-text">回收站</span>
                    </a></li>
                    <li><a href="/profile" class="nav-link">
                        <span class="nav-icon">👤</span>
                        <span class="nav-text">个人中心</span>
                    </a></li>
                    <li th:if="${user != null && user.role.name() == 'ADMIN'}">
                        <a href="/admin" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            <span class="nav-text">系统管理</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>分享管理</h1>
                </div>
                <div class="top-bar-right">
                    <div class="user-info">
                        <div class="user-menu">
                            <button class="user-avatar" onclick="toggleUserMenu()">
                                <img th:src="${user != null and user.avatar != null ? user.avatar : '/static/images/default-avatar.svg'}" alt="头像">
                            </button>
                            <div class="user-dropdown" id="userDropdown">
                                <a href="/profile">个人设置</a>
                                <a href="#" onclick="logout()">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 分享管理内容 -->
            <div class="shares-content">
                <!-- 分享统计 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">🔗</div>
                        <div class="stat-info">
                            <h3 th:text="${stats.totalShares}">0</h3>
                            <p>总分享数</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">✅</div>
                        <div class="stat-info">
                            <h3 th:text="${stats.activeShares}">0</h3>
                            <p>有效分享</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">⬇️</div>
                        <div class="stat-info">
                            <h3 th:text="${stats.totalDownloads}">0</h3>
                            <p>总下载次数</p>
                        </div>
                    </div>
                </div>
                
                <!-- 分享列表 -->
                <div class="shares-list">
                    <div class="shares-header">
                        <div class="shares-header-left">
                            <h3>我的分享</h3>
                            <div class="batch-controls" id="batchControls" style="display: none;">
                                <span class="selected-count">已选择 <span id="selectedCount">0</span> 项</span>
                                <button class="batch-btn" onclick="batchOperation('cancel')">批量取消</button>
                                <button class="batch-btn" onclick="batchOperation('enable')">批量启用</button>
                                <button class="batch-btn danger" onclick="batchOperation('delete')">批量删除</button>
                                <button class="batch-btn outline" onclick="clearSelection()">取消选择</button>
                            </div>
                        </div>
                        <div class="shares-header-right">
                            <div class="selection-controls" id="selectionControls" style="display: none;">
                                <label class="select-all-label">
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    <span>全选</span>
                                </label>
                            </div>
                            <div class="shares-filter">
                                <select id="statusFilter" onchange="filterShares()">
                                    <option value="">全部状态</option>
                                    <option value="active">有效</option>
                                    <option value="expired">已过期</option>
                                    <option value="disabled">已禁用</option>
                                </select>
                            </div>
                            <button class="toggle-select-btn" onclick="toggleSelectMode()" id="toggleSelectBtn">
                                <span class="icon">☑️</span>
                                <span class="text">批量选择</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="shares-items">
                        <div th:each="share : ${shares}" class="share-item" th:classappend="${share.isAvailable() ? 'active' : 'inactive'}" th:data-share-id="${share.id}">
                            <div class="share-checkbox" style="display: none;">
                                <input type="checkbox" th:id="'share-' + ${share.id}" th:value="${share.id}" onchange="updateSelection()">
                                <label th:for="'share-' + ${share.id}"></label>
                            </div>
                            <div class="share-info">
                                <div class="share-file">
                                    <span class="file-icon" th:text="${@fileIconUtil.getIcon(share.fileName)}">📄</span>
                                    <span class="file-name" th:text="${share.fileName}" th:title="${share.originalFileName}">文件名</span>
                                    <span class="file-size" th:text="'(' + ${@fileSizeUtil.format(share.fileSize)} + ')'" th:if="${share.fileSize != null}"></span>
                                </div>
                                <div class="share-details">
                                    <div class="share-code">
                                        <label>分享码:</label>
                                        <span th:text="${share.shareCode}">ABC12345</span>
                                        <button class="copy-btn" th:data-share-code="${share.shareCode}" onclick="copyShareLink(this.dataset.shareCode)">复制链接</button>
                                    </div>
                                    <div class="share-meta">
                                        <span th:if="${share.fileDeleted}" class="meta-item error">⚠️ 文件已删除</span>
                                        <span th:if="${share.hasPassword()}" class="meta-item">🔒 有密码</span>
                                        <span th:if="${share.expireTime != null}" class="meta-item" th:text="'过期时间: ' + ${#temporals.format(share.expireTime, 'yyyy-MM-dd HH:mm')}">过期时间</span>
                                        <span th:if="${share.downloadLimit != null && share.downloadLimit != -1}" class="meta-item" th:text="'下载限制: ' + ${share.downloadCount} + '/' + ${share.downloadLimit}">下载限制</span>
                                        <span class="meta-item" th:text="'创建时间: ' + ${#temporals.format(share.createdTime, 'yyyy-MM-dd HH:mm')}">创建时间</span>
                                    </div>
                                </div>
                            </div>
                            <div class="share-item-right">
                                <div class="share-actions">
                                    <button class="action-btn" th:data-share-code="${share.shareCode}" onclick="viewShare(this.dataset.shareCode)">查看</button>
                                    <button class="action-btn" th:data-share-id="${share.id}" onclick="editShare(this.dataset.shareId)">编辑</button>
                                    <button class="action-btn delete-btn" th:data-share-id="${share.id}" onclick="cancelShare(this.dataset.shareId)">取消</button>
                                </div>
                                <div class="share-status">
                                    <span th:if="${share.isAvailable()}" class="status-badge active">有效</span>
                                    <span th:if="${share.isExpired()}" class="status-badge expired">已过期</span>
                                    <span th:if="${!share.isActive}" class="status-badge disabled">已禁用</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 空状态 -->
                    <div class="empty-state" th:if="${#lists.isEmpty(shares)}">
                        <div class="empty-icon">🔗</div>
                        <h3>暂无分享</h3>
                        <p>在文件管理页面分享文件后，会在这里显示</p>
                        <div class="empty-actions">
                            <a href="/files" class="btn btn-primary">去分享文件</a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- 编辑分享模态框 -->
    <div class="modal" id="editShareModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑分享</h3>
                <button class="modal-close" onclick="hideEditShareModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editShareForm">
                    <div class="form-group">
                        <label for="sharePassword">访问密码</label>
                        <input type="text" id="sharePassword" name="password" placeholder="留空表示无密码">
                        <div class="form-help">设置访问密码可以保护您的分享文件</div>
                    </div>
                    <div class="form-group">
                        <label for="expireTime">过期时间</label>
                        <input type="datetime-local" id="expireTime" name="expireTime" step="1">
                        <div class="form-help">留空表示永不过期，格式：YYYY-MM-DD HH:MM</div>
                    </div>
                    <div class="form-group">
                        <label for="downloadLimit">下载次数限制</label>
                        <input type="number" id="downloadLimit" name="downloadLimit" min="-1" placeholder="-1表示无限制">
                        <div class="form-help">-1表示无限制，0表示禁止下载</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="hideEditShareModal()">取消</button>
                <button class="btn btn-primary" onclick="updateShare()">保存</button>
            </div>
        </div>
    </div>
    
    <!-- Toast 提示 -->
    <div id="toast" class="toast"></div>
    
    <script th:src="@{/static/js/common.js}"></script>
    <script th:src="@{/static/js/shares.js}"></script>
</body>
</html>
