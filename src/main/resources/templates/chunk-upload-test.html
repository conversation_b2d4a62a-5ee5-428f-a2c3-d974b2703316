<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分片上传测试 - LLB文件管理系统</title>
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: var(--shadow-md);
        }
        
        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--bg-light);
        }
        
        .progress-container {
            margin: 20px 0;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .status-info {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background-color: var(--bg-secondary);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>分片上传功能测试</h1>
        <p>此页面用于测试大文件的分片上传功能。选择一个大于10MB的文件来测试分片上传。</p>
        
        <div class="upload-area" id="uploadArea">
            <p>点击选择文件或拖拽文件到此处</p>
            <input type="file" id="fileInput" style="display: none;">
            <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                选择文件
            </button>
        </div>
        
        <div class="progress-container" id="progressContainer">
            <div class="status-info" id="statusInfo">准备上传...</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">0%</div>
        </div>
        
        <div class="mt-16">
            <button class="btn btn-secondary" onclick="window.location.href='/upload'">
                返回上传页面
            </button>
        </div>
    </div>
    
    <!-- Toast 提示 -->
    <div id="toast" class="toast"></div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <script th:src="@{/static/js/common.js}"></script>
    <script>
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const progressContainer = document.getElementById('progressContainer');
        const statusInfo = document.getElementById('statusInfo');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        
        // 文件选择事件
        fileInput.addEventListener('change', handleFileSelect);
        
        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = 'var(--primary-color)';
            uploadArea.style.backgroundColor = 'var(--bg-light)';
        });
        
        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = 'var(--border-color)';
            uploadArea.style.backgroundColor = 'transparent';
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = 'var(--border-color)';
            uploadArea.style.backgroundColor = 'transparent';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });
        
        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }
        
        async function handleFile(file) {
            console.log('Selected file:', file.name, 'Size:', file.size);
            
            progressContainer.style.display = 'block';
            updateStatus('准备上传文件: ' + file.name);
            updateProgress(0);
            
            try {
                const CHUNK_THRESHOLD = 10 * 1024 * 1024; // 10MB
                
                if (file.size > CHUNK_THRESHOLD) {
                    await uploadFileWithChunks(file);
                } else {
                    Toast.info('文件小于10MB，将使用普通上传方式');
                    await uploadFileNormal(file);
                }
                
                updateStatus('上传完成！');
                updateProgress(100);
                Toast.success('文件上传成功！');
                
            } catch (error) {
                console.error('Upload error:', error);
                updateStatus('上传失败: ' + error.message);
                Toast.error('上传失败: ' + error.message);
            }
        }
        
        async function uploadFileWithChunks(file) {
            const CHUNK_SIZE = 2 * 1024 * 1024; // 2MB per chunk
            
            // 计算文件MD5
            updateStatus('计算文件校验码...');
            const fileMd5 = await calculateFileMD5(file);
            
            // 初始化分片上传
            updateStatus('初始化分片上传...');

            // 创建FormData对象发送参数
            const initFormData = new FormData();
            initFormData.append('fileName', file.name);
            initFormData.append('fileSize', file.size.toString());
            initFormData.append('fileMd5', fileMd5);
            initFormData.append('chunkSize', CHUNK_SIZE.toString());
            initFormData.append('folderId', '0');

            const initResponse = await Http.postForm('/api/chunk-upload/init', initFormData);
            
            if (initResponse.code !== 200) {
                throw new Error(initResponse.message || '初始化上传失败');
            }
            
            const uploadId = initResponse.data.uploadId;
            const totalChunks = initResponse.data.totalChunks;
            
            updateStatus(`开始上传 ${totalChunks} 个分片...`);
            
            // 上传分片
            for (let chunkNumber = 0; chunkNumber < totalChunks; chunkNumber++) {
                const start = chunkNumber * CHUNK_SIZE;
                const end = Math.min(start + CHUNK_SIZE, file.size);
                const chunk = file.slice(start, end);
                
                updateStatus(`上传分片 ${chunkNumber + 1}/${totalChunks}`);
                
                // 上传分片
                const chunkFormData = new FormData();
                chunkFormData.append('uploadId', uploadId);
                chunkFormData.append('chunkNumber', chunkNumber);
                chunkFormData.append('chunk', chunk);
                
                const chunkResponse = await Http.postForm('/api/chunk-upload/chunk', chunkFormData);
                
                if (chunkResponse.code !== 200) {
                    // 取消上传
                    await Http.delete(`/api/chunk-upload/${uploadId}`);
                    throw new Error(`分片 ${chunkNumber + 1} 上传失败: ${chunkResponse.message}`);
                }
                
                // 更新进度
                const progress = Math.round(((chunkNumber + 1) / totalChunks) * 100);
                updateProgress(progress);
                
                // 检查是否完成
                if (chunkResponse.data.status === 'COMPLETED') {
                    break;
                }
            }
        }
        
        async function uploadFileNormal(file) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('folderId', 0);
            
            updateStatus('上传文件...');
            const response = await Http.post('/api/files/upload', formData);
            
            if (response.code !== 200) {
                throw new Error(response.message || '上传失败');
            }
        }
        
        function calculateFileMD5(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const arrayBuffer = e.target.result;
                    const hash = CryptoJS.MD5(CryptoJS.lib.WordArray.create(arrayBuffer));
                    resolve(hash.toString());
                };
                reader.onerror = reject;
                reader.readAsArrayBuffer(file);
            });
        }
        
        function updateStatus(message) {
            statusInfo.textContent = message;
        }
        
        function updateProgress(percent) {
            progressFill.style.width = percent + '%';
            progressText.textContent = percent + '%';
        }
    </script>
</body>
</html>
