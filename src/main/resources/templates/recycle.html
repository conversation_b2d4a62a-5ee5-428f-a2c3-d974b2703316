<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回收站 - LLB文件管理系统</title>
    <link rel="stylesheet" th:href="@{/static/css/common.css}">
    <link rel="stylesheet" th:href="@{/static/css/dashboard.css}">
    <link rel="stylesheet" th:href="@{/static/css/files.css}">
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>LLB文件系统</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="/dashboard" class="nav-link">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">仪表板</span>
                    </a></li>
                    <li><a href="/files" class="nav-link">
                        <span class="nav-icon">📁</span>
                        <span class="nav-text">文件管理</span>
                    </a></li>
                    <li><a href="/upload" class="nav-link">
                        <span class="nav-icon">⬆️</span>
                        <span class="nav-text">文件上传</span>
                    </a></li>
                    <li><a href="/shares" class="nav-link">
                        <span class="nav-icon">🔗</span>
                        <span class="nav-text">分享管理</span>
                    </a></li>
                    <li><a href="/recycle" class="nav-link active">
                        <span class="nav-icon">🗑️</span>
                        <span class="nav-text">回收站</span>
                    </a></li>
                    <li><a href="/profile" class="nav-link">
                        <span class="nav-icon">👤</span>
                        <span class="nav-text">个人中心</span>
                    </a></li>
                    <li th:if="${user != null && user.role.name() == 'ADMIN'}">
                        <a href="/admin" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            <span class="nav-text">系统管理</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>回收站</h1>
                    <p class="page-desc">已删除的文件将在此保留30天</p>
                </div>
                <div class="top-bar-right">
                    <div class="toolbar">
                        <div class="selection-controls">
                            <label class="select-all-label">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                <span>全选</span>
                            </label>
                        </div>
                        <button class="btn btn-outline" onclick="clearRecycle()" id="clearBtn">
                            <span class="nav-icon">🗑️</span>
                            清空回收站
                        </button>
                    </div>
                    <div class="user-info">
                        <div class="user-menu">
                            <button class="user-avatar" onclick="toggleUserMenu()">
                                <img th:src="${user != null and user.avatar != null ? user.avatar : '/static/images/default-avatar.svg'}" alt="头像">
                            </button>
                            <div class="user-dropdown" id="userDropdown">
                                <a href="/profile">个人设置</a>
                                <a href="#" onclick="logout()">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 回收站内容 -->
            <div class="files-content">
                <!-- 批量操作栏 -->
                <div class="batch-actions" id="batchActions" style="display: none;">
                    <div class="batch-info">
                        已选择 <span id="selectedCount">0</span> 项
                    </div>
                    <div class="batch-buttons">
                        <button class="btn btn-primary" onclick="batchRestore()">恢复</button>
                        <button class="btn btn-outline" onclick="batchPermanentDelete()">永久删除</button>
                        <button class="btn btn-outline" onclick="clearSelection()">取消选择</button>
                    </div>
                </div>
                
                <!-- 文件和文件夹列表 -->
                <div class="file-grid" id="fileGrid">
                    <!-- 已删除的文件夹 -->
                    <div th:each="folder : ${deletedFolders}" class="file-item deleted-folder" th:data-id="${folder.id}" th:data-type="folder">
                        <div class="file-checkbox">
                            <input type="checkbox" th:value="${folder.id}" data-type="folder" onchange="updateSelection()">
                        </div>
                        <div class="file-icon">📁</div>
                        <div class="file-info">
                            <div class="file-name" th:text="${folder.name}">文件夹名称</div>
                            <div class="file-meta">
                                <span>文件夹</span> •
                                <span th:text="${#temporals.format(folder.deletedTime, 'yyyy-MM-dd HH:mm')}">删除时间</span>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="action-btn restore-btn" th:onclick="'restoreFolder(' + ${folder.id} + ')'">恢复</button>
                            <button class="action-btn delete-btn" th:onclick="'permanentDeleteFolder(' + ${folder.id} + ')'">永久删除</button>
                        </div>
                    </div>

                    <!-- 已删除的文件 -->
                    <div th:each="file : ${deletedFiles}" class="file-item deleted-file" th:data-id="${file.id}" th:data-type="file">
                        <div class="file-checkbox">
                            <input type="checkbox" th:value="${file.id}" data-type="file" onchange="updateSelection()">
                        </div>
                        <div class="file-icon" th:text="${@fileIconUtil.getIcon(file.originalName)}">📄</div>
                        <div class="file-info">
                            <div class="file-name" th:text="${file.name}">文件名称</div>
                            <div class="file-meta">
                                <span th:text="${@fileSizeUtil.format(file.fileSize)}">文件大小</span> •
                                <span th:text="${#temporals.format(file.deletedTime, 'yyyy-MM-dd HH:mm')}">删除时间</span>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="action-btn restore-btn" th:onclick="'restoreFile(' + ${file.id} + ')'">恢复</button>
                            <button class="action-btn delete-btn" th:onclick="'permanentDeleteFile(' + ${file.id} + ')'">永久删除</button>
                        </div>
                    </div>
                </div>

                <!-- 空状态 -->
                <div class="empty-state" th:if="${#lists.isEmpty(deletedFiles) && #lists.isEmpty(deletedFolders)}">
                    <div class="empty-icon">🗑️</div>
                    <h3>回收站为空</h3>
                    <p>删除的文件和文件夹会出现在这里</p>
                    <div class="empty-actions">
                        <a href="/files" class="btn btn-primary">返回文件管理</a>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Toast 提示 -->
    <div id="toast" class="toast"></div>
    
    <script th:src="@{/static/js/common.js}"></script>
    <script th:src="@{/static/js/recycle.js?v=2}"></script>
</body>
</html>
