/**
 * 认证页面JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // 登录表单处理
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // 注册表单处理
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
        
        // 密码确认验证
        const password = document.getElementById('password');
        const confirmPassword = document.getElementById('confirmPassword');
        if (password && confirmPassword) {
            confirmPassword.addEventListener('input', validatePasswordMatch);
            password.addEventListener('input', updatePasswordStrength);
        }
    }
});

/**
 * 处理登录
 */
async function handleLogin(e) {
    e.preventDefault();
    
    const form = e.target;
    const btn = document.getElementById('loginBtn');
    const btnText = btn.querySelector('.btn-text');
    const btnLoading = btn.querySelector('.btn-loading');
    
    // 表单验证
    if (!Form.validate(form)) {
        return;
    }
    
    // 设置加载状态
    btn.disabled = true;
    btn.classList.add('loading');
    btnText.style.display = 'none';
    btnLoading.style.display = 'inline-block';
    
    try {
        const formData = Form.serialize(form);
        
        // 发送登录请求
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(formData)
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            Toast.success('登录成功，正在跳转...');
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 1000);
        } else {
            Toast.error(result.message || '登录失败');
        }
        
    } catch (error) {
        console.error('Login error:', error);
        Toast.error('网络错误，请稍后重试');
    } finally {
        // 恢复按钮状态
        btn.disabled = false;
        btn.classList.remove('loading');
        btnText.style.display = 'inline-block';
        btnLoading.style.display = 'none';
    }
}

/**
 * 处理注册
 */
async function handleRegister(e) {
    e.preventDefault();
    
    const form = e.target;
    const btn = document.getElementById('registerBtn');
    const btnText = btn.querySelector('.btn-text');
    const btnLoading = btn.querySelector('.btn-loading');
    
    // 表单验证
    if (!Form.validate(form)) {
        return;
    }
    
    // 密码确认验证
    if (!validatePasswordMatch()) {
        return;
    }
    
    // 设置加载状态
    btn.disabled = true;
    btn.classList.add('loading');
    btnText.style.display = 'none';
    btnLoading.style.display = 'inline-block';
    
    try {
        const formData = Form.serialize(form);
        
        // 发送注册请求
        const response = await fetch('/api/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(formData)
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            Toast.success('注册成功，正在跳转到登录页...');
            setTimeout(() => {
                window.location.href = '/login';
            }, 1500);
        } else {
            Toast.error(result.message || '注册失败');
        }
        
    } catch (error) {
        console.error('Register error:', error);
        Toast.error('网络错误，请稍后重试');
    } finally {
        // 恢复按钮状态
        btn.disabled = false;
        btn.classList.remove('loading');
        btnText.style.display = 'inline-block';
        btnLoading.style.display = 'none';
    }
}

/**
 * 验证密码匹配
 */
function validatePasswordMatch() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirmPassword');
    
    if (!password || !confirmPassword) return true;
    
    const group = confirmPassword.closest('.form-group');
    if (!group) return true;
    
    // 清除之前的错误状态
    group.classList.remove('error', 'success');
    const errorMsg = group.querySelector('.form-error-message');
    if (errorMsg) errorMsg.remove();
    
    if (password.value !== confirmPassword.value) {
        Form.showFieldError(group, '两次输入的密码不一致');
        return false;
    } else if (confirmPassword.value) {
        group.classList.add('success');
    }
    
    return true;
}

/**
 * 更新密码强度指示器
 */
function updatePasswordStrength() {
    const password = document.getElementById('password');
    if (!password) return;
    
    const value = password.value;
    const group = password.closest('.form-group');
    if (!group) return;
    
    // 创建或获取密码强度指示器
    let strengthIndicator = group.querySelector('.password-strength');
    if (!strengthIndicator) {
        strengthIndicator = document.createElement('div');
        strengthIndicator.className = 'password-strength';
        strengthIndicator.innerHTML = '<div class="password-strength-bar"></div>';
        group.appendChild(strengthIndicator);
    }
    
    const strengthBar = strengthIndicator.querySelector('.password-strength-bar');
    const strength = calculatePasswordStrength(value);
    
    // 更新强度指示器
    strengthBar.className = 'password-strength-bar';
    if (strength.score >= 3) {
        strengthBar.classList.add('strong');
    } else if (strength.score >= 2) {
        strengthBar.classList.add('medium');
    } else if (strength.score >= 1) {
        strengthBar.classList.add('weak');
    }
}

/**
 * 计算密码强度
 */
function calculatePasswordStrength(password) {
    let score = 0;
    const checks = {
        length: password.length >= 8,
        lowercase: /[a-z]/.test(password),
        uppercase: /[A-Z]/.test(password),
        numbers: /\d/.test(password),
        symbols: /[^A-Za-z0-9]/.test(password)
    };
    
    // 基础长度检查
    if (password.length >= 6) score += 1;
    if (password.length >= 8) score += 1;
    
    // 字符类型检查
    if (checks.lowercase) score += 1;
    if (checks.uppercase) score += 1;
    if (checks.numbers) score += 1;
    if (checks.symbols) score += 1;
    
    return {
        score: Math.min(score, 4),
        checks: checks
    };
}

/**
 * 表单输入增强
 */
document.addEventListener('DOMContentLoaded', function() {
    // 用户名输入验证
    const usernameInput = document.getElementById('username');
    if (usernameInput) {
        usernameInput.addEventListener('input', function() {
            const value = this.value;
            const group = this.closest('.form-group');
            if (!group) return;
            
            // 清除之前的状态
            group.classList.remove('error', 'success');
            const errorMsg = group.querySelector('.form-error-message');
            if (errorMsg) errorMsg.remove();
            
            if (value.length > 0) {
                if (value.length < 3) {
                    Form.showFieldError(group, '用户名至少需要3个字符');
                } else if (value.length > 20) {
                    Form.showFieldError(group, '用户名不能超过20个字符');
                } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
                    Form.showFieldError(group, '用户名只能包含字母、数字和下划线');
                } else {
                    group.classList.add('success');
                }
            }
        });
    }
    
    // 邮箱输入验证
    const emailInput = document.getElementById('email');
    if (emailInput) {
        emailInput.addEventListener('input', function() {
            const value = this.value;
            const group = this.closest('.form-group');
            if (!group) return;
            
            // 清除之前的状态
            group.classList.remove('error', 'success');
            const errorMsg = group.querySelector('.form-error-message');
            if (errorMsg) errorMsg.remove();
            
            if (value.length > 0) {
                if (!Form.isValidEmail(value)) {
                    Form.showFieldError(group, '请输入有效的邮箱地址');
                } else {
                    group.classList.add('success');
                }
            }
        });
    }
});
