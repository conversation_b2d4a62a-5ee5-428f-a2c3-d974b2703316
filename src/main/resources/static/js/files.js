/**
 * 文件管理页面JavaScript
 */

let selectedItems = new Set();
let currentView = 'grid';
let isSearchMode = false;
let originalContent = null;
let isRefreshing = false;
let isCreatingShare = false; // 防止重复创建分享

document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initFilesPage();
    
    // 绑定事件
    bindEvents();
});

/**
 * 初始化文件页面
 */
function initFilesPage() {
    // 设置默认视图
    switchView('grid');
    
    // 初始化拖拽上传
    initDragUpload();
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 搜索框回车事件
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchFiles();
            }
        });
    }
    
    // 全选/取消全选
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'a') {
            e.preventDefault();
            selectAll();
        }
        if (e.key === 'Escape') {
            clearSelection();
        }
    });
}

/**
 * 切换视图模式
 */
function switchView(view) {
    currentView = view;
    const fileGrid = document.getElementById('fileGrid');
    const viewBtns = document.querySelectorAll('.view-btn');
    
    // 更新按钮状态
    viewBtns.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.view === view) {
            btn.classList.add('active');
        }
    });
    
    // 更新网格样式
    if (view === 'list') {
        fileGrid.classList.add('list-view');
    } else {
        fileGrid.classList.remove('list-view');
    }
}

/**
 * 更新选择状态
 */
function updateSelection() {
    const checkboxes = document.querySelectorAll('.file-item input[type="checkbox"]');
    selectedItems.clear();
    
    checkboxes.forEach(checkbox => {
        const item = checkbox.closest('.file-item');
        if (checkbox.checked) {
            selectedItems.add({
                id: checkbox.value,
                type: item.dataset.type
            });
            item.classList.add('selected');
        } else {
            item.classList.remove('selected');
        }
    });
    
    // 更新批量操作栏
    const batchActions = document.getElementById('batchActions');
    const selectedCount = document.getElementById('selectedCount');

    if (selectedItems.size > 0) {
        batchActions.style.display = 'flex';
        selectedCount.textContent = selectedItems.size;
    } else {
        batchActions.style.display = 'none';
    }

    // 更新全选状态
    updateSelectAllState();
}

/**
 * 更新全选按钮状态
 */
function updateSelectAllState() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.file-item input[type="checkbox"]');

    if (!selectAll || checkboxes.length === 0) return;

    const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;

    if (checkedCount === 0) {
        selectAll.checked = false;
        selectAll.indeterminate = false;
    } else if (checkedCount === checkboxes.length) {
        selectAll.checked = true;
        selectAll.indeterminate = false;
    } else {
        selectAll.checked = false;
        selectAll.indeterminate = true;
    }
}

/**
 * 全选/取消全选
 */
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.file-item input[type="checkbox"]');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateSelection();
}

/**
 * 全选
 */
function selectAll() {
    const checkboxes = document.querySelectorAll('.file-item input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelection();
}

/**
 * 清除选择
 */
function clearSelection() {
    const checkboxes = document.querySelectorAll('.file-item input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelection();
}

/**
 * 搜索文件
 */
async function searchFiles() {
    const keyword = document.getElementById('searchInput').value.trim();
    if (!keyword) {
        Toast.warning('请输入搜索关键词');
        return;
    }

    // 保存原始内容（如果还没保存）
    if (!isSearchMode) {
        const fileGrid = document.getElementById('fileGrid');
        originalContent = fileGrid.innerHTML;
    }

    // 显示加载状态
    showSearchLoading();

    try {
        const response = await Http.get('/api/files/search', { keyword });
        if (response.code === 200) {
            // 更新文件列表显示
            updateFileGrid(response.data, keyword);
            isSearchMode = true;

            // 显示清除搜索按钮
            const clearBtn = document.getElementById('clearSearchBtn');
            if (clearBtn) {
                clearBtn.style.display = 'inline-block';
            }
        } else {
            Toast.error(response.message || '搜索失败');
            hideSearchLoading();
        }
    } catch (error) {
        console.error('Search error:', error);
        Toast.error('搜索失败');
        hideSearchLoading();
    }
}

/**
 * 显示创建文件夹模态框
 */
function showCreateFolderModal() {
    const modal = document.getElementById('createFolderModal');
    modal.classList.add('show');
    
    // 聚焦到输入框
    setTimeout(() => {
        document.getElementById('folderName').focus();
    }, 100);
}

/**
 * 隐藏创建文件夹模态框
 */
function hideCreateFolderModal() {
    const modal = document.getElementById('createFolderModal');
    modal.classList.remove('show');
    
    // 清空表单
    document.getElementById('createFolderForm').reset();
}

/**
 * 创建文件夹
 */
async function createFolder() {
    const folderName = document.getElementById('folderName').value.trim();
    if (!folderName) {
        Toast.error('请输入文件夹名称');
        return;
    }
    
    try {
        const response = await Http.post('/api/folders', {
            name: folderName,
            parentId: window.currentFolderId || 0
        });
        
        if (response.code === 200) {
            Toast.success('文件夹创建成功');
            hideCreateFolderModal();
            
            // 动态添加新文件夹到页面，而不是刷新整个页面
            const newFolder = response.data;
            addFolderToPage(newFolder);
            
        } else {
            Toast.error(response.message || '创建失败');
        }
    } catch (error) {
        console.error('Create folder error:', error);
        Toast.error('创建失败');
    }
}

/**
 * 动态添加文件夹到页面
 */
function addFolderToPage(folder) {
    const fileGrid = document.getElementById('fileGrid');
    if (!fileGrid) return;
    
    // 检查是否已存在该文件夹
    const existingFolder = fileGrid.querySelector(`[data-id="${folder.id}"][data-type="folder"]`);
    if (existingFolder) return;
    
    // 创建文件夹元素
    const folderElement = document.createElement('div');
    folderElement.className = 'file-item';
    folderElement.setAttribute('data-id', folder.id);
    folderElement.setAttribute('data-type', 'folder');
    
    folderElement.innerHTML = `
        <div class="file-checkbox">
            <input type="checkbox" value="${folder.id}" onchange="updateSelection()">
        </div>
        <div class="file-icon">📁</div>
        <div class="file-info">
            <div class="file-name">${folder.name}</div>
            <div class="file-meta">
                <span>${formatDateTime(folder.createdTime)}</span>
            </div>
        </div>
        <div class="file-actions">
            <button class="action-btn" onclick="downloadFolder(${folder.id})">下载</button>
            <button class="action-btn" onclick="shareFolderModal(${folder.id})" title="分享文件夹">分享</button>
            <button class="action-btn" onclick="openFolder(${folder.id})">打开</button>
            <button class="action-btn" onclick="renameFolder(${folder.id})">重命名</button>
            <button class="action-btn" onclick="deleteFolder(${folder.id})">删除</button>
        </div>
    `;
    
    // 插入到文件列表的开头（文件夹优先显示）
    const firstFileItem = fileGrid.querySelector('.file-item[data-type="file"]');
    if (firstFileItem) {
        fileGrid.insertBefore(folderElement, firstFileItem);
    } else {
        fileGrid.appendChild(folderElement);
    }
    
    // 如果之前显示的是空状态，需要隐藏空状态提示
    const emptyState = document.querySelector('.empty-state');
    if (emptyState) {
        emptyState.style.display = 'none';
    }
}

/**
 * 刷新文件列表（简化版，直接重载当前页面内容）
 */
async function refreshFilesSimple() {
    try {
        // 获取当前文件夹ID
        const urlParams = new URLSearchParams(window.location.search);
        const currentFolderId = parseInt(urlParams.get('folderId')) || 0;
        
        // 并行获取文件夹和文件列表
        const [foldersResponse, filesResponse] = await Promise.all([
            Http.get(`/api/folders?parentId=${currentFolderId}`),
            Http.get(`/api/files/list?folderId=${currentFolderId}`)
        ]);
        
        if (foldersResponse.code === 200 && filesResponse.code === 200) {
            const folders = foldersResponse.data || [];
            const files = filesResponse.data || [];
            
            // 重新渲染文件列表
            renderFileList(folders, files);
            Toast.success('刷新完成');
        } else {
            Toast.error('刷新失败');
        }
    } catch (error) {
        console.error('Refresh files error:', error);
        Toast.error('刷新失败');
    }
}

/**
 * 渲染文件列表
 */
function renderFileList(folders, files) {
    const fileGrid = document.getElementById('fileGrid');
    if (!fileGrid) return;
    
    // 清空当前列表
    fileGrid.innerHTML = '';
    
    const hasContent = folders.length > 0 || files.length > 0;
    
    if (!hasContent) {
        // 显示空状态
        const currentFolderId = new URLSearchParams(window.location.search).get('folderId') || 0;
        fileGrid.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">📂</div>
                <h3>文件夹为空</h3>
                <p>开始上传文件或创建文件夹吧</p>
                <div class="empty-actions">
                    <a href="/upload?folderId=${currentFolderId}" class="btn btn-primary">上传文件</a>
                    <button class="btn btn-outline" onclick="showCreateFolderModal()">新建文件夹</button>
                </div>
            </div>
        `;
        return;
    }
    
    // 先添加文件夹
    folders.forEach(folder => {
        const folderElement = createFolderElement(folder);
        fileGrid.appendChild(folderElement);
    });
    
    // 再添加文件
    files.forEach(file => {
        const fileElement = createFileElement(file);
        fileGrid.appendChild(fileElement);
    });
}

/**
 * 创建文件夹元素
 */
function createFolderElement(folder) {
    const folderElement = document.createElement('div');
    folderElement.className = 'file-item';
    folderElement.setAttribute('data-id', folder.id);
    folderElement.setAttribute('data-type', 'folder');
    
    folderElement.innerHTML = `
        <div class="file-checkbox">
            <input type="checkbox" value="${folder.id}" onchange="updateSelection()">
        </div>
        <div class="file-icon">📁</div>
        <div class="file-info">
            <div class="file-name">${folder.name}</div>
            <div class="file-meta">
                <span>${formatDateTime(folder.createdTime)}</span>
            </div>
        </div>
        <div class="file-actions">
            <button class="action-btn" onclick="downloadFolder(${folder.id})">下载</button>
            <button class="action-btn" onclick="shareFolderModal(${folder.id})" title="分享文件夹">分享</button>
            <button class="action-btn" onclick="openFolder(${folder.id})">打开</button>
            <button class="action-btn" onclick="renameFolder(${folder.id})">重命名</button>
            <button class="action-btn" onclick="deleteFolder(${folder.id})">删除</button>
        </div>
    `;
    
    return folderElement;
}

/**
 * 创建文件元素
 */
function createFileElement(file) {
    const fileElement = document.createElement('div');
    fileElement.className = 'file-item';
    fileElement.setAttribute('data-id', file.id);
    fileElement.setAttribute('data-type', 'file');
    
    fileElement.innerHTML = `
        <div class="file-checkbox">
            <input type="checkbox" value="${file.id}" onchange="updateSelection()">
        </div>
        <div class="file-icon">${getFileIcon(file.originalName || file.name)}</div>
        <div class="file-info">
            <div class="file-name">${file.name}</div>
            <div class="file-meta">
                <span>${formatFileSize(file.fileSize)}</span> • 
                <span>${formatDateTime(file.createdTime)}</span>
            </div>
        </div>
        <div class="file-actions">
            <button class="action-btn" onclick="downloadFile(${file.id})">下载</button>
            <button class="action-btn" onclick="shareFile(${file.id})">分享</button>
            <button class="action-btn" onclick="copyFile(${file.id})">复制</button>
            <button class="action-btn" onclick="renameFile(${file.id})">重命名</button>
            <button class="action-btn" onclick="deleteFile(${file.id})">删除</button>
        </div>
    `;
    
    return fileElement;
}

/**
 * 简单的文件图标获取（占位符实现）
 */
function getFileIcon(fileName) {
    if (!fileName) return '📄';
    
    const ext = fileName.split('.').pop()?.toLowerCase();
    const iconMap = {
        'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️', 'svg': '🖼️',
        'mp4': '🎥', 'avi': '🎥', 'mov': '🎥', 'mkv': '🎥',
        'mp3': '🎵', 'wav': '🎵', 'flac': '🎵',
        'pdf': '📕', 'doc': '📘', 'docx': '📘', 'txt': '📝',
        'zip': '📦', 'rar': '📦', '7z': '📦',
        'js': '💻', 'html': '💻', 'css': '💻', 'java': '💻'
    };
    
    return iconMap[ext] || '📄';
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (!bytes || bytes === 0) return '0 B';
    
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTimeString) {
    if (!dateTimeString) return '';
    
    try {
        const date = new Date(dateTimeString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        }).replace(/\//g, '-');
    } catch (e) {
        return dateTimeString;
    }
}

/**
 * 打开文件夹
 */
function openFolder(folderId) {
    window.location.href = `/files?folderId=${folderId}`;
}

/**
 * 返回上一级文件夹
 */
function goToParentFolder(parentId) {
    if (parentId === null || parentId === 0) {
        window.location.href = '/files';
    } else {
        window.location.href = `/files?folderId=${parentId}`;
    }
}

/**
 * 重命名文件夹
 */
async function renameFolder(folderId) {
    const newName = prompt('请输入新的文件夹名称:');
    if (!newName || !newName.trim()) {
        return;
    }
    
    try {
        const response = await Http.put(`/api/folders/${folderId}/rename`, {
            newName: newName.trim()
        });
        
        if (response.code === 200) {
            Toast.success('重命名成功');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            Toast.error(response.message || '重命名失败');
        }
    } catch (error) {
        console.error('Rename folder error:', error);
        Toast.error('重命名失败');
    }
}

/**
 * 删除文件夹
 */
async function deleteFolder(folderId) {
    if (!confirm('确定要删除这个文件夹吗？')) {
        return;
    }
    
    try {
        const response = await Http.delete(`/api/folders/${folderId}`);
        
        if (response.code === 200) {
            Toast.success('删除成功');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            Toast.error(response.message || '删除失败');
        }
    } catch (error) {
        console.error('Delete folder error:', error);
        Toast.error('删除失败');
    }
}

/**
 * 下载文件
 */
function downloadFile(fileId) {
    window.open(`/api/files/${fileId}/download`, '_blank');
}

/**
 * 下载文件夹
 */
function downloadFolder(folderId) {
    window.open(`/api/folders/${folderId}/download`, '_blank');
}

/**
 * 分享文件
 */
async function shareFile(fileId) {
    showShareModal(fileId, 'file');
}

/**
 * 分享文件夹模态框
 */
async function shareFolderModal(folderId) {
    showShareModal(folderId, 'folder');
}

/**
 * 重命名文件
 */
async function renameFile(fileId) {
    const newName = prompt('请输入新的文件名称:');
    if (!newName || !newName.trim()) {
        return;
    }
    
    try {
        const response = await Http.put(`/api/files/${fileId}/rename`, {
            newName: newName.trim()
        });
        
        if (response.code === 200) {
            Toast.success('重命名成功');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            Toast.error(response.message || '重命名失败');
        }
    } catch (error) {
        console.error('Rename file error:', error);
        Toast.error('重命名失败');
    }
}

/**
 * 复制文件
 */
async function copyFile(fileId) {
    let targetFolderId = window.currentFolderId || 0;

    // 如果当前不在根目录，询问用户是否复制到当前目录
    if (targetFolderId !== 0) {
        const copyToCurrent = confirm(`是否复制到当前文件夹？\n点击"确定"复制到当前文件夹，点击"取消"复制到根目录。`);
        if (!copyToCurrent) {
            targetFolderId = 0;
        }
    }

    try {
        const response = await Http.post(`/api/files/${fileId}/copy`, {
            targetFolderId: parseInt(targetFolderId)
        });

        if (response.code === 200) {
            Toast.success('复制成功');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            Toast.error(response.message || '复制失败');
        }
    } catch (error) {
        console.error('Copy file error:', error);
        Toast.error('复制失败');
    }
}

/**
 * 删除文件
 */
async function deleteFile(fileId) {
    if (!confirm('确定要删除这个文件吗？文件将被移动到回收站。')) {
        return;
    }

    try {
        const response = await Http.delete(`/api/files/${fileId}`);

        if (response.code === 200) {
            Toast.success('删除成功');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            Toast.error(response.message || '删除失败');
        }
    } catch (error) {
        console.error('Delete file error:', error);
        Toast.error('删除失败');
    }
}

/**
 * 批量删除
 */
async function batchDelete() {
    if (selectedItems.size === 0) {
        Toast.warning('请先选择要删除的项目');
        return;
    }

    if (!confirm(`确定要删除选中的 ${selectedItems.size} 个项目吗？`)) {
        return;
    }

    try {
        // 分别获取文件和文件夹
        const fileIds = Array.from(selectedItems)
            .filter(item => item.type === 'file')
            .map(item => parseInt(item.id));

        const folderIds = Array.from(selectedItems)
            .filter(item => item.type === 'folder')
            .map(item => parseInt(item.id));

        if (fileIds.length === 0 && folderIds.length === 0) {
            Toast.warning('请选择要删除的项目');
            return;
        }

        let successCount = 0;
        let errorCount = 0;
        const errors = [];

        // 删除文件
        if (fileIds.length > 0) {
            try {
                const response = await Http.post('/api/files/batch', {
                    operation: 'delete',
                    fileIds: fileIds
                });

                if (response.code === 200) {
                    successCount += fileIds.length;
                } else {
                    errorCount += fileIds.length;
                    errors.push(`文件删除失败: ${response.message}`);
                }
            } catch (error) {
                errorCount += fileIds.length;
                errors.push(`文件删除失败: ${error.message}`);
            }
        }

        // 删除文件夹
        for (const folderId of folderIds) {
            try {
                const response = await Http.delete(`/api/folders/${folderId}`);

                if (response.code === 200) {
                    successCount++;
                } else {
                    errorCount++;
                    errors.push(`文件夹删除失败: ${response.message}`);
                }
            } catch (error) {
                errorCount++;
                errors.push(`文件夹删除失败: ${error.message}`);
            }
        }

        // 显示结果
        if (successCount > 0 && errorCount === 0) {
            Toast.success(`成功删除 ${successCount} 个项目`);
        } else if (successCount > 0 && errorCount > 0) {
            Toast.warning(`成功删除 ${successCount} 个项目，${errorCount} 个项目删除失败`);
            console.error('删除错误:', errors);
        } else {
            Toast.error('删除失败');
            console.error('删除错误:', errors);
        }

        setTimeout(() => {
            window.location.reload();
        }, 1000);

    } catch (error) {
        console.error('Batch delete error:', error);
        Toast.error('批量删除失败');
    }
}

/**
 * 批量复制
 */
async function batchCopy() {
    if (selectedItems.size === 0) {
        Toast.warning('请先选择要复制的项目');
        return;
    }

    let targetFolderId = window.currentFolderId || 0;

    // 如果当前不在根目录，询问用户是否复制到当前目录
    if (targetFolderId !== 0) {
        const copyToCurrent = confirm(`是否复制到当前文件夹？\n点击"确定"复制到当前文件夹，点击"取消"复制到根目录。`);
        if (!copyToCurrent) {
            targetFolderId = 0;
        }
    }

    try {
        const fileIds = Array.from(selectedItems)
            .filter(item => item.type === 'file')
            .map(item => parseInt(item.id));

        if (fileIds.length === 0) {
            Toast.warning('请选择要复制的文件');
            return;
        }

        const response = await Http.post('/api/files/batch', {
            operation: 'copy',
            fileIds: fileIds,
            targetFolderId: parseInt(targetFolderId)
        });

        if (response.code === 200) {
            Toast.success('批量复制成功');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            Toast.error(response.message || '批量复制失败');
        }
    } catch (error) {
        console.error('Batch copy error:', error);
        Toast.error('批量复制失败');
    }
}

/**
 * 批量下载
 */
async function batchDownload() {
    if (selectedItems.size === 0) {
        Toast.warning('请先选择要下载的文件');
        return;
    }

    try {
        const fileIds = Array.from(selectedItems)
            .filter(item => item.type === 'file')
            .map(item => parseInt(item.id));

        if (fileIds.length === 0) {
            Toast.warning('请选择要下载的文件');
            return;
        }

        // 创建表单并提交下载请求
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/api/files/batch-download';
        form.style.display = 'none';

        fileIds.forEach(fileId => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'fileIds';
            input.value = fileId;
            form.appendChild(input);
        });

        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);

        Toast.success('正在准备下载...');

    } catch (error) {
        console.error('Batch download error:', error);
        Toast.error('批量下载失败');
    }
}

/**
 * 初始化拖拽上传
 */
function initDragUpload() {
    const fileGrid = document.getElementById('fileGrid');
    
    // 防止默认拖拽行为
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        fileGrid.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });
    
    // 高亮拖拽区域
    ['dragenter', 'dragover'].forEach(eventName => {
        fileGrid.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        fileGrid.addEventListener(eventName, unhighlight, false);
    });
    
    // 处理文件拖拽
    fileGrid.addEventListener('drop', handleDrop, false);
}

function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

function highlight(e) {
    document.getElementById('fileGrid').classList.add('drag-over');
}

function unhighlight(e) {
    document.getElementById('fileGrid').classList.remove('drag-over');
}

function handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;
    
    if (files.length > 0) {
        // 跳转到上传页面
        const folderId = window.currentFolderId || 0;
        window.location.href = `/upload?folderId=${folderId}`;
    }
}

/**
 * 用户菜单切换
 */
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

/**
 * 用户登出
 */
async function logout() {
    try {
        const response = await Http.post('/api/auth/logout');
        
        if (response.code === 200) {
            Toast.success('登出成功');
            setTimeout(() => {
                window.location.href = '/login';
            }, 1000);
        } else {
            Toast.error(response.message || '登出失败');
        }
    } catch (error) {
        console.error('Logout error:', error);
        Toast.error('网络错误，请稍后重试');
    }
}

// 点击其他地方关闭用户菜单
document.addEventListener('click', function(e) {
    const userMenu = document.querySelector('.user-menu');
    const dropdown = document.getElementById('userDropdown');
    
    if (userMenu && dropdown && !userMenu.contains(e.target)) {
        dropdown.classList.remove('show');
    }
});

// 模态框点击外部关闭
document.addEventListener('click', function(e) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (e.target === modal) {
            modal.classList.remove('show');
        }
    });
});

/**
 * 更新文件网格显示
 */
function updateFileGrid(files, keyword = '') {
    const fileGrid = document.getElementById('fileGrid');

    if (!files || files.length === 0) {
        fileGrid.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">🔍</div>
                <h3>未找到相关文件</h3>
                <p>没有找到包含"${keyword}"的文件</p>
                <button class="btn btn-outline" onclick="clearSearch()">清除搜索</button>
            </div>
        `;
        return;
    }

    // 生成文件项HTML
    const fileItems = files.map(file => createFileItemHTML(file)).join('');

    fileGrid.innerHTML = `
        <div class="search-results-header">
            <div class="search-info">
                <span>搜索"${keyword}"找到 ${files.length} 个文件</span>
                <button class="btn btn-outline btn-small" onclick="clearSearch()">清除搜索</button>
            </div>
        </div>
        ${fileItems}
    `;

    // 重新绑定事件
    rebindEvents();

    hideSearchLoading();
}

/**
 * 创建文件项HTML
 */
function createFileItemHTML(file) {
    const fileIcon = Utils.getFileIcon(file.name || file.originalName);
    const fileSize = Utils.formatFileSize(file.fileSize);
    const createdTime = Utils.formatDate(file.createdTime);

    return `
        <div class="file-item" data-id="${file.id}" data-type="file">
            <div class="file-checkbox">
                <input type="checkbox" value="${file.id}" onchange="updateSelection()">
            </div>
            <div class="file-icon">${fileIcon}</div>
            <div class="file-info">
                <div class="file-name">${file.name || file.originalName}</div>
                <div class="file-meta">
                    <span>${fileSize}</span> •
                    <span>${createdTime}</span>
                </div>
            </div>
            <div class="file-actions">
                <button class="action-btn" onclick="downloadFile(${file.id})">下载</button>
                <button class="action-btn" onclick="shareFile(${file.id})">分享</button>
                <button class="action-btn" onclick="copyFile(${file.id})">复制</button>
                <button class="action-btn" onclick="renameFile(${file.id})">重命名</button>
                <button class="action-btn" onclick="deleteFile(${file.id})">删除</button>
            </div>
        </div>
    `;
}

/**
 * 清除搜索结果
 */
function clearSearch() {
    if (isSearchMode && originalContent) {
        const fileGrid = document.getElementById('fileGrid');
        fileGrid.innerHTML = originalContent;
        isSearchMode = false;

        // 清空搜索框
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = '';
        }

        // 隐藏清除搜索按钮
        const clearBtn = document.getElementById('clearSearchBtn');
        if (clearBtn) {
            clearBtn.style.display = 'none';
        }

        // 重新绑定事件
        rebindEvents();

        Toast.info('已清除搜索结果');
    }
}

/**
 * 显示搜索加载状态
 */
function showSearchLoading() {
    const fileGrid = document.getElementById('fileGrid');
    fileGrid.innerHTML = `
        <div class="loading-state">
            <div class="loading-spinner"></div>
            <p>正在搜索...</p>
        </div>
    `;
}

/**
 * 隐藏搜索加载状态
 */
function hideSearchLoading() {
    // 加载状态会被搜索结果替换，这里不需要特别处理
}

/**
 * 重新绑定事件
 */
function rebindEvents() {
    // 重新绑定文件项的点击事件等
    // 由于使用了内联事件处理器，大部分事件不需要重新绑定
    // 但需要重新绑定选择相关的事件
    updateSelection();
}

/**
 * 显示移动模态框
 */
async function showMoveModal() {
    if (selectedItems.size === 0) {
        Toast.warning('请先选择要移动的项目');
        return;
    }

    try {
        // 获取用户的文件夹列表
        const response = await Http.get('/api/folders/all');
        if (response.code !== 200) {
            Toast.error('获取文件夹列表失败');
            return;
        }

        const folders = response.data || [];
        showMoveModalDialog(folders);

    } catch (error) {
        console.error('获取文件夹列表失败:', error);
        Toast.error('获取文件夹列表失败');
    }
}

/**
 * 显示移动模态框对话框
 */
function showMoveModalDialog(folders) {
    // 创建模态框HTML
    const modalHtml = `
        <div class="modal show" id="moveModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>移动文件</h3>
                    <button class="modal-close" onclick="closeMoveModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <p>选择目标文件夹：</p>
                    <div class="folder-list">
                        <div class="folder-item" data-folder-id="0">
                            <span class="folder-icon">📁</span>
                            <span class="folder-name">根目录</span>
                        </div>
                        ${folders.map(folder => `
                            <div class="folder-item" data-folder-id="${folder.id}">
                                <span class="folder-icon">📁</span>
                                <span class="folder-name">${folder.name}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="closeMoveModal()">取消</button>
                    <button class="btn btn-primary" onclick="confirmMove()">移动</button>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 绑定文件夹选择事件
    bindFolderSelection();
}

/**
 * 绑定文件夹选择事件
 */
function bindFolderSelection() {
    const folderItems = document.querySelectorAll('#moveModal .folder-item');
    folderItems.forEach(item => {
        item.addEventListener('click', function() {
            // 移除其他选中状态
            folderItems.forEach(f => f.classList.remove('selected'));
            // 添加选中状态
            this.classList.add('selected');
        });
    });
}

/**
 * 关闭移动模态框
 */
function closeMoveModal() {
    const modal = document.getElementById('moveModal');
    if (modal) {
        modal.remove();
    }
}

/**
 * 确认移动
 */
async function confirmMove() {
    const selectedFolder = document.querySelector('#moveModal .folder-item.selected');
    if (!selectedFolder) {
        Toast.warning('请选择目标文件夹');
        return;
    }

    const targetFolderId = parseInt(selectedFolder.dataset.folderId);
    const folderName = selectedFolder.querySelector('.folder-name').textContent;

    if (!confirm(`确定要将选中的 ${selectedItems.size} 个项目移动到"${folderName}"吗？`)) {
        return;
    }

    try {
        // 分别获取文件和文件夹
        const fileIds = Array.from(selectedItems)
            .filter(item => item.type === 'file')
            .map(item => parseInt(item.id));

        const folderIds = Array.from(selectedItems)
            .filter(item => item.type === 'folder')
            .map(item => parseInt(item.id));

        console.log('移动操作 - 文件:', fileIds.length, '个, 文件夹:', folderIds.length, '个, 目标:', targetFolderId);

        if (fileIds.length === 0 && folderIds.length === 0) {
            Toast.warning('请选择要移动的项目');
            return;
        }

        let successCount = 0;
        let errorCount = 0;
        const errors = [];

        // 移动文件
        if (fileIds.length > 0) {
            try {
                const response = await Http.post('/api/files/batch', {
                    operation: 'move',
                    fileIds: fileIds,
                    targetFolderId: targetFolderId
                });

                if (response.code === 200) {
                    successCount += fileIds.length;
                } else {
                    errorCount += fileIds.length;
                    errors.push(`文件移动失败: ${response.message}`);
                }
            } catch (error) {
                errorCount += fileIds.length;
                errors.push(`文件移动失败: ${error.message}`);
            }
        }

        // 移动文件夹
        for (const folderId of folderIds) {
            try {
                const response = await Http.put(`/api/folders/${folderId}/move?targetParentId=${targetFolderId}`);

                if (response.code === 200) {
                    successCount++;
                } else {
                    errorCount++;
                    errors.push(`文件夹移动失败: ${response.message}`);
                    console.error(`文件夹 ${folderId} 移动失败:`, response.message);
                }
            } catch (error) {
                errorCount++;
                errors.push(`文件夹移动失败: ${error.message}`);
                console.error(`文件夹 ${folderId} 移动异常:`, error);
            }
        }

        // 显示结果
        if (successCount > 0 && errorCount === 0) {
            Toast.success(`成功移动 ${successCount} 个项目`);
        } else if (successCount > 0 && errorCount > 0) {
            Toast.warning(`成功移动 ${successCount} 个项目，${errorCount} 个项目移动失败`);
            console.error('移动错误:', errors);
        } else {
            Toast.error('移动失败');
            console.error('移动错误:', errors);
        }

        closeMoveModal();
        setTimeout(() => {
            window.location.reload();
        }, 1000);

    } catch (error) {
        console.error('Move error:', error);
        Toast.error('移动失败');
    }
}

/**
 * 刷新文件列表
 * 检查文件状态并同步显示
 */
async function refreshFiles() {
    if (isRefreshing) {
        Toast.info('正在刷新中，请稍候...');
        return;
    }

    isRefreshing = true;
    const refreshBtn = document.querySelector('button[onclick="refreshFiles()"]');
    const originalText = refreshBtn.innerHTML;

    try {
        // 更新按钮状态
        refreshBtn.innerHTML = '<span class="nav-icon">⏳</span>刷新中...';
        refreshBtn.disabled = true;

        // 获取当前文件夹ID
        const urlParams = new URLSearchParams(window.location.search);
        const currentFolderId = urlParams.get('folderId') || 0;

        Toast.info('正在检查文件状态...');

        // 调用后端刷新API
        const response = await Http.post(`/api/files/refresh?folderId=${currentFolderId}`);

        if (response.code === 200) {
            const result = response.data;

            // 显示刷新结果
            let message = '文件状态检查完成';
            if (result.removedCount > 0 || result.addedCount > 0) {
                message += `，清理了 ${result.removedCount} 个丢失文件`;
                if (result.addedCount > 0) {
                    message += `，发现 ${result.addedCount} 个新文件`;
                }

                // 刷新页面显示
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                message += '，所有文件状态正常';
            }

            Toast.success(message);
        } else {
            Toast.error(response.message || '刷新失败');
        }

    } catch (error) {
        console.error('Refresh files error:', error);
        Toast.error('刷新失败，请稍后重试');
    } finally {
        // 恢复按钮状态
        refreshBtn.innerHTML = originalText;
        refreshBtn.disabled = false;
        isRefreshing = false;
    }
}

/**
 * 显示分享模态框
 */
function showShareModal(contentId, type = 'file') {
    // 检查模态框是否已经显示，防止重复显示
    const modal = document.getElementById('shareModal');
    if (modal && modal.classList.contains('show')) {
        console.log('分享模态框已经显示，忽略重复请求');
        return;
    }

    // 检查是否正在创建分享，防止状态混乱
    if (isCreatingShare) {
        console.log('正在创建分享，忽略重复请求');
        return;
    }

    // 设置分享类型和内容ID
    window.currentShareType = type;
    window.currentShareContentId = contentId;
    
    // 根据类型设置模态框标题
    const titleElement = document.getElementById('shareModalTitle');
    if (titleElement) {
        titleElement.textContent = type === 'folder' ? '分享文件夹' : '分享文件';
    }

    // 检查是否切换了内容，只有切换内容时才重置分享状态
    const isNewContent = window.currentShareFileId !== contentId || window.currentShareType !== type;
    window.currentShareFileId = contentId; // 保持兼容性

    if (isNewContent) {
        // 切换到不同内容，重置分享相关状态
        window.currentShareId = null;
        window.currentShareCode = null;
        console.log('切换到新内容，重置分享状态，内容ID:', contentId, '类型:', type);
    } else {
        console.log('同一内容，保持现有分享状态 - ID:', window.currentShareId, 'Code:', window.currentShareCode);
    }

    // 重置表单
    const form = document.getElementById('shareForm');
    if (form) {
        form.reset();
    }

    // 确保显示分享表单，隐藏结果区域
    const formSection = document.querySelector('#shareModal .share-form-section');
    const resultSection = document.querySelector('#shareModal .share-result-section');
    if (formSection) formSection.style.display = 'block';
    if (resultSection) resultSection.style.display = 'none';

    // 切换按钮区域的显示状态
    const formButtons = document.querySelector('#shareModal .modal-footer .share-form-section');
    const resultButtons = document.querySelector('#shareModal .modal-footer .share-result-section');
    if (formButtons) formButtons.style.display = 'flex';
    if (resultButtons) resultButtons.style.display = 'none';

    // 根据是否有现有分享来设置按钮文本
    const createBtn = document.querySelector('#shareModal .modal-footer .share-form-section .btn-primary');
    if (createBtn) {
        const buttonText = window.currentShareId ? '更新分享' : '创建分享';
        createBtn.textContent = buttonText;
        createBtn.disabled = false;
        console.log('设置按钮文本为:', buttonText);
    }

    // 显示模态框
    if (modal) {
        modal.classList.add('show');
        console.log('显示分享模态框，内容ID:', contentId, '类型:', type);
    }
}

/**
 * 隐藏分享模态框
 */
function hideShareModal() {
    const modal = document.getElementById('shareModal');
    if (modal) {
        modal.classList.remove('show');
    }

    // 清空表单
    const form = document.getElementById('shareForm');
    if (form) {
        form.reset();
    }

    // 只重置UI相关状态，保持分享状态以便后续更新
    isCreatingShare = false;
    // 注意：不重置 window.currentShareFileId, window.currentShareId, window.currentShareCode
    // 这样用户重新打开同一文件的分享模态框时，可以继续更新现有分享

    // 确保显示分享表单，隐藏结果区域
    const formSection = document.querySelector('#shareModal .share-form-section');
    const resultSection = document.querySelector('#shareModal .share-result-section');
    if (formSection) formSection.style.display = 'block';
    if (resultSection) resultSection.style.display = 'none';

    // 切换按钮区域的显示状态
    const formButtons = document.querySelector('#shareModal .modal-footer .share-form-section');
    const resultButtons = document.querySelector('#shareModal .modal-footer .share-result-section');
    if (formButtons) formButtons.style.display = 'flex';
    if (resultButtons) resultButtons.style.display = 'none';

    // 重置按钮状态为默认文本（实际文本会在下次打开时根据状态设置）
    const createBtn = document.querySelector('#shareModal .modal-footer .share-form-section .btn-primary');
    if (createBtn) {
        createBtn.textContent = '创建分享';
        createBtn.disabled = false;
    }

    console.log('分享模态框已隐藏，UI状态已重置，分享状态已保持');
}

/**
 * 完全重置分享状态（用于开始全新的分享流程）
 */
function resetShareState() {
    window.currentShareFileId = null;
    window.currentShareId = null;
    window.currentShareCode = null;
    isCreatingShare = false;
    console.log('分享状态已完全重置');
}

/**
 * 创建或更新分享链接
 */
async function createShare() {
    // 防止重复提交
    if (isCreatingShare) {
        console.log('正在处理分享，忽略重复请求');
        return;
    }

    const contentId = window.currentShareContentId || window.currentShareFileId;
    const shareType = window.currentShareType || 'file';
    
    if (!contentId) {
        Toast.error('无效的内容ID');
        return;
    }

    // 设置创建状态，防止重复提交
    isCreatingShare = true;

    // 更新按钮状态
    const createBtn = document.querySelector('#shareModal .btn-primary');
    const isUpdate = window.currentShareId !== null;
    if (createBtn) {
        createBtn.disabled = true;
        createBtn.textContent = isUpdate ? '更新中...' : '创建中...';
    }

    const form = document.getElementById('shareForm');
    const formData = new FormData(form);

    // 构建请求参数
    const params = new URLSearchParams();
    
    // 根据类型添加不同参数
    if (shareType === 'folder') {
        params.append('folderId', contentId);
    } else {
        params.append('fileId', contentId);
    }
    
    params.append('shareType', formData.get('shareType') || 'DOWNLOAD');

    const password = formData.get('password');
    if (password && password.trim()) {
        params.append('password', password.trim());
    }

    const expireTime = formData.get('expireTime');
    if (expireTime) {
        params.append('expireTime', expireTime);
    }

    const downloadLimit = formData.get('downloadLimit');
    if (downloadLimit && downloadLimit > 0) {
        params.append('downloadLimit', downloadLimit);
    }

    try {
        let response;
        let share;

        console.log('createShare - 检查当前状态:');
        console.log('createShare - window.currentShareId:', window.currentShareId);
        console.log('createShare - window.currentShareCode:', window.currentShareCode);

        if (window.currentShareId) {
            // 更新现有分享
            console.log('开始更新分享，分享ID:', window.currentShareId);

            // 将URLSearchParams转换为对象
            const updateData = {};
            for (const [key, value] of params.entries()) {
                updateData[key] = value;
            }

            response = await Http.putForm(`/api/shares/${window.currentShareId}`, updateData);

            if (response.code === 200) {
                // 更新成功，需要重新获取分享信息来显示
                // 由于更新API只返回成功消息，我们需要构造分享对象
                share = {
                    id: window.currentShareId,
                    shareCode: window.currentShareCode, // 需要保存分享码
                    shareType: params.get('shareType') || 'DOWNLOAD',
                    password: params.get('password') || null,
                    expireTime: params.get('expireTime') || null,
                    downloadLimit: params.get('downloadLimit') || null
                };
                
                // 根据分享类型设置对应的ID字段
                if (shareType === 'folder') {
                    share.folderId = contentId;
                } else {
                    share.fileId = contentId;
                }

                Toast.success('分享设置更新成功');
                console.log('分享更新成功:', share);
            } else {
                Toast.error(response.message || '更新分享失败');
                console.error('更新分享失败:', response);
                return;
            }
        } else {
            // 创建新分享
            console.log('开始创建分享，内容ID:', contentId, '类型:', shareType);
            response = await Http.postForm('/api/shares', params);

            if (response.code === 200) {
                share = response.data;

                // 保存分享ID和分享码，用于后续更新
                window.currentShareId = share.id;
                window.currentShareCode = share.shareCode;

                Toast.success('分享链接创建成功');
                console.log('分享创建成功:', share);
                console.log('已保存分享状态 - ID:', window.currentShareId, 'Code:', window.currentShareCode);
            } else {
                Toast.error(response.message || '创建分享失败');
                console.error('创建分享失败:', response);
                return;
            }
        }

        // 显示分享结果
        const shareUrl = `${window.location.origin}/share/${share.shareCode}`;
        showShareResult(shareUrl, share);

    } catch (error) {
        console.error('Share operation error:', error);
        Toast.error(window.currentShareId ? '更新分享失败' : '创建分享失败');
    } finally {
        // 恢复按钮状态
        isCreatingShare = false;
        if (createBtn) {
            createBtn.disabled = false;
            // 注意：这里不要重新设置按钮文本，因为在成功创建/更新后，
            // 用户会看到结果页面，不会再看到这个按钮
            console.log('createShare - 按钮状态已恢复');
        }
    }
}

/**
 * 显示分享结果
 */
function showShareResult(shareUrl, share) {
    // 隐藏分享表单，显示结果
    const formSection = document.querySelector('#shareModal .share-form-section');
    const resultSection = document.querySelector('#shareModal .share-result-section');

    if (formSection) formSection.style.display = 'none';
    if (resultSection) {
        resultSection.style.display = 'block';

        // 填充分享链接
        const linkInput = document.getElementById('shareLink');
        if (linkInput) {
            linkInput.value = shareUrl;
        }

        // 显示分享信息
        const infoDiv = document.getElementById('shareInfo');
        if (infoDiv) {
            let infoHtml = `
                <p><strong>分享链接:</strong> <a href="${shareUrl}" target="_blank" class="share-url-link">${shareUrl}</a></p>
                <p><strong>分享类型:</strong> ${share.shareType === 'DOWNLOAD' ? '下载' : '查看'}</p>
            `;

            if (share.password) {
                infoHtml += `
                    <p><strong>访问密码:</strong> 
                        <code class="share-password">${share.password}</code>
                        <button class="copy-password-btn" onclick="copySharePassword('${share.password}')">复制</button>
                    </p>
                `;
            }

            if (share.expireTime) {
                infoHtml += `<p><strong>过期时间:</strong> ${new Date(share.expireTime).toLocaleString()}</p>`;
            }

            if (share.downloadLimit && share.downloadLimit > 0) {
                infoHtml += `<p><strong>下载限制:</strong> ${share.downloadLimit} 次</p>`;
            }

            infoDiv.innerHTML = infoHtml;
        }
    }

    // 切换按钮区域的显示状态
    const formButtons = document.querySelector('#shareModal .modal-footer .share-form-section');
    const resultButtons = document.querySelector('#shareModal .modal-footer .share-result-section');

    if (formButtons) formButtons.style.display = 'none';
    if (resultButtons) resultButtons.style.display = 'flex';

    // 自动复制分享链接到剪切板 - 延迟显示，避免覆盖创建成功提示
    setTimeout(async () => {
        try {
            await Utils.copyToClipboard(shareUrl);
            Toast.success('分享链接已自动复制到剪贴板');
        } catch (error) {
            console.error('Auto copy share link error:', error);
            // 自动复制失败时不显示错误提示，用户仍可手动复制
        }
    }, 2000);

    console.log('分享结果已显示，按钮区域已切换，链接已自动复制');
}

/**
 * 复制分享密码
 */
async function copySharePassword(password) {
    try {
        await Utils.copyToClipboard(password);
        Toast.success('分享密码已复制到剪贴板');
    } catch (error) {
        console.error('Copy share password error:', error);
        Toast.error('复制失败');
    }
}

/**
 * 复制分享链接
 */
async function copyShareLink() {
    const linkInput = document.getElementById('shareLink');
    if (!linkInput) {
        Toast.error('分享链接不存在');
        return;
    }

    try {
        await Utils.copyToClipboard(linkInput.value);
        Toast.success('分享链接已复制到剪贴板');
    } catch (error) {
        console.error('Copy share link error:', error);
        Toast.error('复制失败');
    }
}

/**
 * 重新设置分享（更新现有分享）
 */
function createNewShare() {
    // 重置创建状态
    isCreatingShare = false;

    // 显示分享表单，隐藏结果
    const formSection = document.querySelector('#shareModal .share-form-section');
    const resultSection = document.querySelector('#shareModal .share-result-section');

    if (formSection) formSection.style.display = 'block';
    if (resultSection) resultSection.style.display = 'none';

    // 切换按钮区域的显示状态
    const formButtons = document.querySelector('#shareModal .modal-footer .share-form-section');
    const resultButtons = document.querySelector('#shareModal .modal-footer .share-result-section');

    if (formButtons) formButtons.style.display = 'flex';
    if (resultButtons) resultButtons.style.display = 'none';

    // 重置表单
    const form = document.getElementById('shareForm');
    if (form) {
        form.reset();
    }

    // 更新按钮文本，表明这是更新操作
    const createBtn = document.querySelector('#shareModal .modal-footer .share-form-section .btn-primary');
    console.log('createNewShare - 查找按钮:', createBtn);
    console.log('createNewShare - 当前分享ID:', window.currentShareId);

    if (createBtn) {
        createBtn.disabled = false;
        const buttonText = window.currentShareId ? '更新分享' : '创建分享';
        createBtn.textContent = buttonText;
        console.log('createNewShare - 按钮文本已设置为:', buttonText);
    } else {
        console.error('createNewShare - 未找到创建按钮');
    }

    console.log('重新设置分享，将更新现有分享ID:', window.currentShareId);
}
