/**
 * 分享访问页面JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initShareAccessPage();
    
    // 绑定事件
    bindEvents();
});

/**
 * 初始化分享访问页面
 */
function initShareAccessPage() {
    // 如果需要密码验证，聚焦到密码输入框
    if (window.needPassword) {
        const passwordInput = document.getElementById('sharePassword');
        if (passwordInput) {
            passwordInput.focus();
        }
    }
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 密码验证表单提交
    const verifyForm = document.getElementById('verifyForm');
    if (verifyForm) {
        verifyForm.addEventListener('submit', handlePasswordVerify);
    }
    
    // 密码输入框回车事件
    const passwordInput = document.getElementById('sharePassword');
    if (passwordInput) {
        passwordInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handlePasswordVerify(e);
            }
        });
    }
}

/**
 * 处理密码验证
 */
async function handlePasswordVerify(e) {
    e.preventDefault();
    
    const form = e.target.closest('form') || e.target;
    const btn = form.querySelector('button[type="submit"]');
    const btnText = btn.querySelector('.btn-text');
    const btnLoading = btn.querySelector('.btn-loading');
    const passwordInput = document.getElementById('sharePassword');
    
    const password = passwordInput.value.trim();
    if (!password) {
        Toast.error('请输入访问密码');
        passwordInput.focus();
        return;
    }
    
    // 设置加载状态
    btn.disabled = true;
    btnText.style.display = 'none';
    btnLoading.style.display = 'inline-block';
    
    try {
        const formData = new URLSearchParams();
        formData.append('password', password);
        
        const response = await Http.postForm(`/api/shares/${window.shareCode}/verify`, formData);
        
        if (response.code === 200) {
            Toast.success('验证成功，正在加载文件信息...');
            
            // 验证成功后重新加载页面或显示文件信息
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            Toast.error(response.message || '密码错误');
            passwordInput.focus();
            passwordInput.select();
        }
        
    } catch (error) {
        console.error('Password verify error:', error);
        Toast.error('验证失败，请稍后重试');
    } finally {
        // 恢复按钮状态
        btn.disabled = false;
        btnText.style.display = 'inline-block';
        btnLoading.style.display = 'none';
    }
}

/**
 * 预览文件
 */
async function previewFile() {
    try {
        const response = await Http.get(`/api/shares/${window.shareCode}/info`);
        
        if (response.code === 200) {
            const shareInfo = response.data;
            showFilePreview(shareInfo);
        } else {
            Toast.error(response.message || '预览失败');
        }
    } catch (error) {
        console.error('Preview file error:', error);
        Toast.error('预览失败');
    }
}

/**
 * 显示文件预览
 */
function showFilePreview(shareInfo) {
    const previewDiv = document.getElementById('filePreview');
    const previewContent = document.getElementById('previewContent');
    
    if (!previewDiv || !previewContent) return;
    
    let previewHtml = '';
    
    if (shareInfo.fileInfo) {
        // 文件预览
        const fileType = getFileType(shareInfo.fileInfo.name);
        
        switch (fileType) {
            case 'image':
                previewHtml = `<img src="/api/shares/${window.shareCode}/preview" alt="图片预览" style="max-width: 100%; max-height: 400px; object-fit: contain;">`;
                break;
            case 'text':
                previewHtml = `<div class="text-preview">正在加载文本内容...</div>`;
                loadTextPreview();
                break;
            default:
                previewHtml = `
                    <div class="preview-placeholder">
                        <div class="placeholder-icon">📄</div>
                        <p>此文件类型不支持在线预览</p>
                        <p>请下载后查看</p>
                    </div>
                `;
        }
    } else if (shareInfo.folderPreview) {
        // 文件夹预览
        previewHtml = `
            <div class="folder-preview">
                <div class="folder-header">
                    <h3>📁 ${shareInfo.folderPreview.folder.name}</h3>
                </div>
                <div class="folder-content">
        `;
        
        // 显示子文件夹
        if (shareInfo.folderPreview.subFolders && shareInfo.folderPreview.subFolders.length > 0) {
            previewHtml += '<div class="folder-section"><h4>📁 文件夹</h4><ul class="folder-list">';
            shareInfo.folderPreview.subFolders.forEach(folder => {
                previewHtml += `<li class="folder-item">📁 ${folder.name}</li>`;
            });
            previewHtml += '</ul></div>';
        }
        
        // 显示文件
        if (shareInfo.folderPreview.files && shareInfo.folderPreview.files.length > 0) {
            previewHtml += '<div class="files-section"><h4>📄 文件</h4><ul class="files-list">';
            shareInfo.folderPreview.files.forEach(file => {
                const fileIcon = getFileIcon(file.name);
                const fileSize = Utils.formatFileSize(file.fileSize);
                previewHtml += `
                    <li class="file-item">
                        <span class="file-icon">${fileIcon}</span>
                        <span class="file-name">${file.originalName}</span>
                        <span class="file-size">${fileSize}</span>
                    </li>
                `;
            });
            previewHtml += '</ul></div>';
        }
        
        if ((!shareInfo.folderPreview.subFolders || shareInfo.folderPreview.subFolders.length === 0) &&
            (!shareInfo.folderPreview.files || shareInfo.folderPreview.files.length === 0)) {
            previewHtml += '<div class="empty-folder">📂 此文件夹为空</div>';
        }
        
        previewHtml += '</div></div>';
    }
    
    previewContent.innerHTML = previewHtml;
    previewDiv.style.display = 'block';
    
    // 滚动到预览区域
    previewDiv.scrollIntoView({ behavior: 'smooth' });
}

/**
 * 关闭预览
 */
function closePreview() {
    const previewDiv = document.getElementById('filePreview');
    if (previewDiv) {
        previewDiv.style.display = 'none';
    }
}

/**
 * 下载文件
 */
async function downloadFile() {
    const downloadBtn = document.getElementById('downloadBtn');
    
    try {
        // 禁用下载按钮防止重复点击
        if (downloadBtn) {
            downloadBtn.disabled = true;
            const isFolder = window.contentType === 'folder';
            const loadingText = isFolder ? '下载中...' : '下载中...';
            downloadBtn.textContent = loadingText;
        }
        
        // 直接跳转到下载链接
        const downloadUrl = `/api/shares/${window.shareCode}/download`;
        const password = document.getElementById('sharePassword')?.value;
        
        if (password) {
            downloadUrl += `?password=${encodeURIComponent(password)}`;
        }
        
        // 创建隐藏的下载链接
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        Toast.success('下载开始');
        
    } catch (error) {
        console.error('Download file error:', error);
        Toast.error('下载失败');
    } finally {
        // 恢复下载按钮
        if (downloadBtn) {
            downloadBtn.disabled = false;
            const isFolder = window.contentType === 'folder';
            const iconText = isFolder ? '📦' : '⬇️';
            const buttonText = isFolder ? '下载文件夹' : '下载文件';
            downloadBtn.innerHTML = `<span class="btn-icon">${iconText}</span>${buttonText}`;
        }
    }
}

/**
 * 获取文件类型
 */
function getFileType(filename) {
    if (!filename) return 'unknown';
    
    const ext = filename.split('.').pop().toLowerCase();
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) {
        return 'image';
    } else if (['txt', 'md', 'json', 'xml', 'csv', 'log'].includes(ext)) {
        return 'text';
    } else if (['pdf'].includes(ext)) {
        return 'pdf';
    } else if (['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm'].includes(ext)) {
        return 'video';
    } else if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma'].includes(ext)) {
        return 'audio';
    }
    
    return 'unknown';
}

/**
 * 加载文本预览
 */
async function loadTextPreview() {
    try {
        const response = await fetch(`/api/shares/${window.shareCode}/preview`);
        const text = await response.text();
        
        const textPreview = document.querySelector('.text-preview');
        if (textPreview) {
            textPreview.innerHTML = `<pre style="white-space: pre-wrap; font-family: monospace; font-size: 14px; line-height: 1.5; max-height: 400px; overflow-y: auto;">${text}</pre>`;
        }
    } catch (error) {
        console.error('Load text preview error:', error);
        const textPreview = document.querySelector('.text-preview');
        if (textPreview) {
            textPreview.innerHTML = '<p style="color: var(--error-color);">文本预览加载失败</p>';
        }
    }
}

/**
 * 获取文件图标
 */
function getFileIcon(filename) {
    if (!filename) return '📄';
    
    const ext = filename.split('.').pop().toLowerCase();
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) {
        return '🖼️';
    } else if (['txt', 'md', 'json', 'xml', 'csv', 'log'].includes(ext)) {
        return '📝';
    } else if (['pdf'].includes(ext)) {
        return '📕';
    } else if (['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm'].includes(ext)) {
        return '🎬';
    } else if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma'].includes(ext)) {
        return '🎵';
    } else if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
        return '📦';
    } else if (['doc', 'docx'].includes(ext)) {
        return '📄';
    } else if (['xls', 'xlsx'].includes(ext)) {
        return '📊';
    } else if (['ppt', 'pptx'].includes(ext)) {
        return '📽️';
    }
    
    return '📄';
}

// formatFileSize函数已统一使用Utils.formatFileSize
// 添加预览样式
const previewStyles = `
    .preview-placeholder {
        text-align: center;
        padding: 40px;
        color: var(--text-muted);
    }
    
    .placeholder-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
    }
    
    .preview-placeholder p {
        margin: 8px 0;
        font-size: 14px;
    }
    
    .folder-preview {
        padding: 16px;
    }
    
    .folder-header h3 {
        margin: 0 0 16px 0;
        font-size: 18px;
        color: var(--primary-color);
    }
    
    .folder-section, .files-section {
        margin-bottom: 20px;
    }
    
    .folder-section h4, .files-section h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        color: var(--text-secondary);
        border-bottom: 1px solid var(--border-color);
        padding-bottom: 4px;
    }
    
    .folder-list, .files-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .folder-item, .file-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border-radius: 6px;
        margin-bottom: 4px;
        background: var(--bg-secondary);
        transition: background-color 0.2s;
    }
    
    .folder-item:hover, .file-item:hover {
        background: var(--bg-hover);
    }
    
    .file-item {
        justify-content: space-between;
    }
    
    .file-icon {
        margin-right: 8px;
        font-size: 16px;
    }
    
    .file-name {
        flex: 1;
        font-size: 14px;
        color: var(--text-primary);
    }
    
    .file-size {
        font-size: 12px;
        color: var(--text-muted);
        margin-left: 8px;
    }
    
    .empty-folder {
        text-align: center;
        padding: 40px;
        color: var(--text-muted);
        font-size: 16px;
    }
`;

// 动态添加样式
const styleSheet = document.createElement('style');
styleSheet.textContent = previewStyles;
document.head.appendChild(styleSheet);
