/**
 * 文件上传页面JavaScript
 */

let uploadQueue = [];
let uploadStats = {
    totalFiles: 0,
    completedFiles: 0,
    totalSize: 0,
    uploadedSize: 0,
    startTime: null
};

document.addEventListener('DOMContentLoaded', function() {
    console.log('上传页面开始初始化');

    try {
        // 初始化页面
        initUploadPage();
        console.log('initUploadPage 完成');

        // 绑定事件
        bindEvents();
        console.log('bindEvents 完成');

        console.log('上传页面初始化完成');
    } catch (error) {
        console.error('上传页面初始化失败:', error);
    }
});

/**
 * 初始化上传页面
 */
function initUploadPage() {
    // 初始化拖拽上传
    initDragUpload();

    // 绑定文件选择
    const fileInput = document.getElementById('fileInput');
    console.log('初始化时的 fileInput 元素:', fileInput);

    if (fileInput) {
        fileInput.addEventListener('change', handleFileSelect);
        console.log('fileInput change 事件已绑定');
    } else {
        console.error('初始化时找不到 fileInput 元素');
    }

    // 绑定文件夹选择
    const folderInput = document.getElementById('folderInput');
    console.log('初始化时的 folderInput 元素:', folderInput);

    if (folderInput) {
        folderInput.addEventListener('change', handleFolderSelect);
        console.log('folderInput change 事件已绑定');
    } else {
        console.error('初始化时找不到 folderInput 元素');
    }
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 使用label标签后，点击事件由浏览器原生处理，不需要额外的JavaScript
    console.log('使用label标签，点击事件由浏览器原生处理');
}

/**
 * 选择文件
 */
function selectFiles() {
    console.log('selectFiles() 被调用');
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        fileInput.click();
    }
}

/**
 * 选择文件夹
 */
function selectFolder() {
    console.log('selectFolder() 被调用');
    const folderInput = document.getElementById('folderInput');
    if (folderInput) {
        folderInput.click();
    }
}

/**
 * 处理文件选择
 */
function handleFileSelect(e) {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
        addFilesToQueue(files);
    }
}

/**
 * 处理文件夹选择
 */
function handleFolderSelect(e) {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
        console.log('选择了文件夹，包含文件数量:', files.length);
        addFolderToQueue(files);
    }
}

/**
 * 初始化拖拽上传
 */
function initDragUpload() {
    const uploadZone = document.getElementById('uploadZone');
    
    // 防止默认拖拽行为
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadZone.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });
    
    // 高亮拖拽区域
    ['dragenter', 'dragover'].forEach(eventName => {
        uploadZone.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        uploadZone.addEventListener(eventName, unhighlight, false);
    });
    
    // 处理文件拖拽
    uploadZone.addEventListener('drop', handleDrop, false);
}

function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

function highlight(e) {
    document.getElementById('uploadZone').classList.add('drag-over');
}

function unhighlight(e) {
    document.getElementById('uploadZone').classList.remove('drag-over');
}

async function handleDrop(e) {
    const dt = e.dataTransfer;

    // 检查是否有文件夹
    if (dt.items && dt.items.length > 0) {
        const items = Array.from(dt.items);
        const hasFolder = items.some(item => item.webkitGetAsEntry && item.webkitGetAsEntry().isDirectory);

        if (hasFolder) {
            // 处理文件夹拖拽
            const allFiles = [];
            for (const item of items) {
                const entry = item.webkitGetAsEntry();
                if (entry) {
                    if (entry.isDirectory) {
                        const folderFiles = await readDirectoryRecursively(entry);
                        allFiles.push(...folderFiles);
                    } else {
                        const file = item.getAsFile();
                        if (file) {
                            file.webkitRelativePath = entry.fullPath.substring(1); // 移除开头的 /
                            allFiles.push(file);
                        }
                    }
                }
            }

            if (allFiles.length > 0) {
                addFolderToQueue(allFiles);
            }
            return;
        }
    }

    // 普通文件拖拽
    const files = Array.from(dt.files);
    if (files.length > 0) {
        addFilesToQueue(files);
    }
}

/**
 * 递归读取文件夹内容
 */
async function readDirectoryRecursively(directoryEntry) {
    const files = [];

    return new Promise((resolve, reject) => {
        const reader = directoryEntry.createReader();

        function readEntries() {
            reader.readEntries(async (entries) => {
                if (entries.length === 0) {
                    resolve(files);
                    return;
                }

                for (const entry of entries) {
                    if (entry.isFile) {
                        const file = await new Promise((resolve) => {
                            entry.file((file) => {
                                file.webkitRelativePath = entry.fullPath.substring(1); // 移除开头的 /
                                resolve(file);
                            });
                        });
                        files.push(file);
                    } else if (entry.isDirectory) {
                        const subFiles = await readDirectoryRecursively(entry);
                        files.push(...subFiles);
                    }
                }

                readEntries(); // 继续读取更多条目
            }, reject);
        }

        readEntries();
    });
}

/**
 * 添加文件到上传队列
 */
function addFilesToQueue(files) {
    const validFiles = files.filter(file => validateFile(file));
    
    if (validFiles.length === 0) {
        return;
    }
    
    // 显示上传列表
    showUploadList();
    
    // 添加到队列
    validFiles.forEach(file => {
        const uploadItem = {
            id: generateId(),
            file: file,
            status: 'waiting',
            progress: 0,
            speed: 0,
            error: null
        };
        
        uploadQueue.push(uploadItem);
        addUploadItemToDOM(uploadItem);
    });
    
    // 更新统计
    updateUploadStats();
    
    // 开始上传
    startUpload();
}

/**
 * 添加文件夹到上传队列
 */
async function addFolderToQueue(files) {
    console.log('开始处理文件夹上传，文件数量:', files.length);

    const validFiles = files.filter(file => validateFile(file));

    if (validFiles.length === 0) {
        Toast.error('没有有效的文件可以上传');
        return;
    }

    // 收集所有需要创建的文件夹路径
    const folderPaths = new Set();
    const fileToFolderMap = new Map();

    validFiles.forEach(file => {
        const relativePath = file.webkitRelativePath || file.name;
        const pathParts = relativePath.split('/');

        if (pathParts.length > 1) {
            // 有文件夹结构，收集所有层级的文件夹路径
            const folderPath = pathParts.slice(0, -1).join('/');
            fileToFolderMap.set(file, folderPath);

            // 添加所有层级的路径（从最深到最浅）
            const parts = pathParts.slice(0, -1); // 去掉文件名
            for (let i = 1; i <= parts.length; i++) {
                const path = parts.slice(0, i).join('/');
                folderPaths.add(path);
            }
        } else {
            // 根目录文件
            fileToFolderMap.set(file, '');
        }
    });

    console.log('需要创建的文件夹路径:', Array.from(folderPaths));

    // 批量创建文件夹结构
    const folderIdMap = await createAllFolders(Array.from(folderPaths));

    // 显示上传列表
    showUploadList();

    // 添加到队列，使用预创建的文件夹ID
    validFiles.forEach(file => {
        const folderPath = fileToFolderMap.get(file);
        const targetFolderId = folderPath ? folderIdMap[folderPath] : (window.currentFolderId || 0);

        const uploadItem = {
            id: generateId(),
            file: file,
            status: 'waiting',
            progress: 0,
            speed: 0,
            error: null,
            targetFolderId: targetFolderId,
            folderPath: folderPath
        };

        uploadQueue.push(uploadItem);
        addUploadItemToDOM(uploadItem);
    });

    // 更新统计
    updateUploadStats();

    // 开始上传
    startUpload();
}

/**
 * 验证文件
 */
function validateFile(file) {
    // 检查文件大小（这里应该根据用户权限动态设置）
    const maxSize = 1024 * 1024 * 1024; // 1GB
    
    if (file.size > maxSize) {
        Toast.error(`文件 ${file.name} 超过大小限制`);
        return false;
    }
    
    return true;
}

/**
 * 显示上传列表
 */
function showUploadList() {
    const uploadList = document.getElementById('uploadList');
    const uploadStats = document.getElementById('uploadStats');
    
    if (uploadList) {
        uploadList.style.display = 'block';
    }
    
    if (uploadStats) {
        uploadStats.style.display = 'block';
    }
}

/**
 * 添加上传项目到DOM
 */
function addUploadItemToDOM(uploadItem) {
    const uploadItems = document.getElementById('uploadItems');
    if (!uploadItems) return;
    
    const itemHtml = `
        <div class="upload-item" data-id="${uploadItem.id}">
            <div class="upload-file-icon">${Utils.getFileIcon(uploadItem.file.name)}</div>
            <div class="upload-file-info">
                <div class="upload-file-name">${uploadItem.file.name}</div>
                <div class="upload-file-meta">
                    <span>${Utils.formatFileSize(uploadItem.file.size)}</span>
                    <span class="upload-speed">等待上传</span>
                </div>
            </div>
            <div class="upload-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
                <div class="progress-text">0%</div>
            </div>
            <div class="upload-status">
                <span class="status-badge uploading">等待</span>
            </div>
            <div class="upload-actions-item">
                <button class="action-btn-small" onclick="cancelUpload('${uploadItem.id}')">取消</button>
            </div>
        </div>
    `;
    
    uploadItems.insertAdjacentHTML('beforeend', itemHtml);
}

/**
 * 开始上传
 */
async function startUpload() {
    if (uploadStats.startTime === null) {
        uploadStats.startTime = Date.now();
    }
    
    // 并发上传（最多2个，减少数据库连接压力）
    const concurrentUploads = 2;
    const uploadPromises = [];
    
    for (let i = 0; i < Math.min(concurrentUploads, uploadQueue.length); i++) {
        const item = uploadQueue.find(item => item.status === 'waiting');
        if (item) {
            uploadPromises.push(uploadFile(item));
        }
    }
    
    await Promise.all(uploadPromises);
}

/**
 * 上传单个文件
 */
async function uploadFile(uploadItem) {
    updateUploadItemStatus(uploadItem.id, 'uploading', '上传中');

    try {
        // 使用预创建的文件夹ID
        let targetFolderId = uploadItem.targetFolderId || window.currentFolderId || 0;

        // 临时保存原始的 currentFolderId
        const originalFolderId = window.currentFolderId;
        window.currentFolderId = targetFolderId;

        // 检查文件大小，决定使用普通上传还是分片上传
        const CHUNK_THRESHOLD = 10 * 1024 * 1024; // 10MB
        let response;

        try {
            if (uploadItem.file.size > CHUNK_THRESHOLD) {
                response = await uploadFileWithChunks(uploadItem);
            } else {
                response = await uploadFileNormal(uploadItem);
            }
        } finally {
            // 恢复原始的 currentFolderId
            window.currentFolderId = originalFolderId;
        }

        updateUploadItemStatus(uploadItem.id, 'completed', '完成');
        uploadStats.completedFiles++;

        // 检查是否有返回的文件信息，显示实际保存的文件名
        const actualFileName = response && response.data && response.data.name ? response.data.name : uploadItem.file.name;
        if (actualFileName !== uploadItem.file.name) {
            Toast.success(`文件 ${uploadItem.file.name} 上传成功，保存为 ${actualFileName}`);
        } else {
            Toast.success(`文件 ${uploadItem.file.name} 上传成功`);
        }

    } catch (error) {
        console.error('Upload file error:', error);
        updateUploadItemStatus(uploadItem.id, 'error', error.message);
        uploadItem.error = error.message;
        Toast.error(`文件 ${uploadItem.file.name} 上传失败: ${error.message}`);
    }

    updateUploadStats();

    // 继续上传队列中的下一个文件
    const nextItem = uploadQueue.find(item => item.status === 'waiting');
    if (nextItem) {
        await uploadFile(nextItem);
    }
}

/**
 * 普通文件上传
 */
async function uploadFileNormal(uploadItem) {
    const formData = new FormData();
    formData.append('file', uploadItem.file);
    formData.append('folderId', window.currentFolderId || 0);

    const response = await uploadWithProgress(formData, uploadItem);

    if (response.code !== 200) {
        throw new Error(response.message || '上传失败');
    }

    return response;
}

/**
 * 分片文件上传
 */
async function uploadFileWithChunks(uploadItem) {
    const file = uploadItem.file;
    const CHUNK_SIZE = 2 * 1024 * 1024; // 2MB per chunk

    // 计算文件MD5
    updateUploadItemStatus(uploadItem.id, 'uploading', '计算文件校验码...');
    const fileMd5 = await calculateFileMD5(file);

    // 初始化分片上传
    updateUploadItemStatus(uploadItem.id, 'uploading', '初始化上传...');

    // 创建FormData对象发送参数
    const initFormData = new FormData();
    initFormData.append('fileName', file.name);
    initFormData.append('fileSize', file.size.toString());
    initFormData.append('fileMd5', fileMd5);
    initFormData.append('chunkSize', CHUNK_SIZE.toString());
    initFormData.append('folderId', (window.currentFolderId || 0).toString());

    const initResponse = await Http.postForm('/api/chunk-upload/init', initFormData);

    if (initResponse.code !== 200) {
        throw new Error(initResponse.message || '初始化上传失败');
    }

    const uploadId = initResponse.data.uploadId;
    const totalChunks = initResponse.data.totalChunks;

    // 上传分片
    for (let chunkNumber = 0; chunkNumber < totalChunks; chunkNumber++) {
        const start = chunkNumber * CHUNK_SIZE;
        const end = Math.min(start + CHUNK_SIZE, file.size);
        const chunk = file.slice(start, end);

        updateUploadItemStatus(uploadItem.id, 'uploading',
            `上传分片 ${chunkNumber + 1}/${totalChunks}`);

        // 检查分片是否已上传（断点续传）
        const checkResponse = await Http.get(`/api/chunk-upload/check/${uploadId}/${chunkNumber}`);
        if (checkResponse.code === 200 && checkResponse.data) {
            // 分片已存在，跳过
            const progress = Math.round(((chunkNumber + 1) / totalChunks) * 100);
            updateUploadProgress(uploadItem.id, progress);
            continue;
        }

        // 上传分片
        const chunkFormData = new FormData();
        chunkFormData.append('uploadId', uploadId);
        chunkFormData.append('chunkNumber', chunkNumber);
        chunkFormData.append('chunk', chunk);

        const chunkResponse = await uploadChunkWithRetry(chunkFormData, uploadItem, chunkNumber, totalChunks);

        if (chunkResponse.code !== 200) {
            // 取消上传
            await Http.delete(`/api/chunk-upload/${uploadId}`);
            throw new Error(`分片 ${chunkNumber + 1} 上传失败: ${chunkResponse.message}`);
        }

        // 检查是否完成
        if (chunkResponse.data.status === 'COMPLETED') {
            // 返回完成响应，包含文件信息
            return chunkResponse;
        }
    }

    // 如果循环结束但没有完成，返回最后的响应
    return { code: 200, data: { status: 'COMPLETED' } };
}

/**
 * 带进度的上传
 */
function uploadWithProgress(formData, uploadItem) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        
        // 上传进度
        xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable) {
                const progress = Math.round((e.loaded / e.total) * 100);
                updateUploadProgress(uploadItem.id, progress);
                
                // 计算上传速度
                const elapsed = Date.now() - uploadStats.startTime;
                const speed = e.loaded / (elapsed / 1000);
                updateUploadSpeed(uploadItem.id, speed);
            }
        });
        
        // 上传完成
        xhr.addEventListener('load', () => {
            try {
                const response = JSON.parse(xhr.responseText);
                resolve(response);
            } catch (error) {
                reject(new Error('响应解析失败'));
            }
        });
        
        // 上传错误
        xhr.addEventListener('error', () => {
            reject(new Error('网络错误'));
        });
        
        // 发送请求
        xhr.open('POST', '/api/files/upload');
        xhr.send(formData);
        
        // 保存xhr引用用于取消上传
        uploadItem.xhr = xhr;
    });
}

/**
 * 更新上传项目状态
 */
function updateUploadItemStatus(itemId, status, statusText) {
    const item = uploadQueue.find(item => item.id === itemId);
    if (item) {
        item.status = status;
    }
    
    const itemElement = document.querySelector(`[data-id="${itemId}"]`);
    if (itemElement) {
        const statusBadge = itemElement.querySelector('.status-badge');
        if (statusBadge) {
            statusBadge.textContent = statusText;
            statusBadge.className = `status-badge ${status}`;
        }
    }
}

/**
 * 更新上传进度
 */
function updateUploadProgress(itemId, progress) {
    const itemElement = document.querySelector(`[data-id="${itemId}"]`);
    if (itemElement) {
        const progressFill = itemElement.querySelector('.progress-fill');
        const progressText = itemElement.querySelector('.progress-text');
        
        if (progressFill) {
            progressFill.style.width = progress + '%';
        }
        
        if (progressText) {
            progressText.textContent = progress + '%';
        }
    }
}

/**
 * 更新上传速度
 */
function updateUploadSpeed(itemId, speed) {
    const itemElement = document.querySelector(`[data-id="${itemId}"]`);
    if (itemElement) {
        const speedElement = itemElement.querySelector('.upload-speed');
        if (speedElement) {
            speedElement.textContent = Utils.formatFileSize(speed) + '/s';
        }
    }
}

/**
 * 更新上传统计
 */
function updateUploadStats() {
    uploadStats.totalFiles = uploadQueue.length;
    uploadStats.totalSize = uploadQueue.reduce((sum, item) => sum + item.file.size, 0);
    
    // 更新DOM显示
    updateStatValue('totalFiles', uploadStats.totalFiles);
    updateStatValue('completedFiles', uploadStats.completedFiles);
    updateStatValue('totalSize', Utils.formatFileSize(uploadStats.totalSize));
    
    // 计算总体上传速度
    if (uploadStats.startTime) {
        const elapsed = Date.now() - uploadStats.startTime;
        const uploadedSize = uploadQueue
            .filter(item => item.status === 'completed')
            .reduce((sum, item) => sum + item.file.size, 0);
        const speed = uploadedSize / (elapsed / 1000);
        updateStatValue('uploadSpeed', Utils.formatFileSize(speed) + '/s');
    }
}

/**
 * 更新统计值
 */
function updateStatValue(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

/**
 * 取消上传
 */
function cancelUpload(itemId) {
    const item = uploadQueue.find(item => item.id === itemId);
    if (item) {
        if (item.xhr) {
            item.xhr.abort();
        }
        
        // 从队列中移除
        const index = uploadQueue.indexOf(item);
        if (index > -1) {
            uploadQueue.splice(index, 1);
        }
        
        // 从DOM中移除
        const itemElement = document.querySelector(`[data-id="${itemId}"]`);
        if (itemElement) {
            itemElement.remove();
        }
        
        updateUploadStats();
    }
}

/**
 * 清除已完成的上传
 */
function clearCompleted() {
    const completedItems = uploadQueue.filter(item => item.status === 'completed');

    completedItems.forEach(item => {
        const itemElement = document.querySelector(`[data-id="${item.id}"]`);
        if (itemElement) {
            itemElement.remove();
        }
    });

    uploadQueue = uploadQueue.filter(item => item.status !== 'completed');
    updateUploadStats();
}

/**
 * 计算文件MD5
 */
function calculateFileMD5(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = function(e) {
            const arrayBuffer = e.target.result;
            const hash = CryptoJS.MD5(CryptoJS.lib.WordArray.create(arrayBuffer));
            resolve(hash.toString());
        };
        reader.onerror = reject;
        reader.readAsArrayBuffer(file);
    });
}

/**
 * 上传分片（带重试）
 */
async function uploadChunkWithRetry(formData, uploadItem, chunkNumber, totalChunks, maxRetries = 3) {
    let lastError;

    for (let retry = 0; retry < maxRetries; retry++) {
        try {
            const response = await Http.postForm('/api/chunk-upload/chunk', formData);

            // 更新进度
            const progress = Math.round(((chunkNumber + 1) / totalChunks) * 100);
            updateUploadProgress(uploadItem.id, progress);

            return response;
        } catch (error) {
            lastError = error;
            if (retry < maxRetries - 1) {
                // 等待一段时间后重试
                await new Promise(resolve => setTimeout(resolve, 1000 * (retry + 1)));
            }
        }
    }

    throw lastError;
}

/**
 * 批量创建文件夹结构
 */
async function createAllFolders(folderPaths) {
    const folderIdMap = {};

    // 按路径深度排序，确保先创建父文件夹
    const sortedPaths = folderPaths.sort((a, b) => {
        const depthA = a.split('/').length;
        const depthB = b.split('/').length;
        return depthA - depthB;
    });

    console.log('按层级排序的文件夹路径:', sortedPaths);

    for (const folderPath of sortedPaths) {
        try {
            const folderId = await createFolderStructure(folderPath, window.currentFolderId || 0, folderIdMap);
            folderIdMap[folderPath] = folderId;
            console.log(`文件夹路径 "${folderPath}" 对应ID: ${folderId}`);
        } catch (error) {
            console.error(`创建文件夹路径 "${folderPath}" 失败:`, error);
            throw error;
        }
    }

    return folderIdMap;
}

/**
 * 创建文件夹结构（优化版，使用缓存）
 */
async function createFolderStructure(folderPath, parentId, folderIdMap = {}) {
    if (!folderPath || folderPath.trim() === '') {
        return parentId;
    }

    // 检查缓存
    if (folderIdMap[folderPath]) {
        return folderIdMap[folderPath];
    }

    const pathParts = folderPath.split('/').filter(part => part.trim() !== '');
    let currentParentId = parentId;
    let currentPath = '';

    for (const folderName of pathParts) {
        currentPath = currentPath ? `${currentPath}/${folderName}` : folderName;

        // 检查缓存中是否已有此路径
        if (folderIdMap[currentPath]) {
            currentParentId = folderIdMap[currentPath];
            continue;
        }

        try {
            // 检查文件夹是否已存在
            const existingFolders = await Http.get(`/api/folders?parentId=${currentParentId}`);
            let existingFolder = null;

            if (existingFolders.code === 200 && existingFolders.data) {
                existingFolder = existingFolders.data.find(folder => folder.name === folderName);
            }

            if (existingFolder) {
                // 文件夹已存在，使用现有的
                currentParentId = existingFolder.id;
                folderIdMap[currentPath] = existingFolder.id;
                console.log(`文件夹已存在: ${currentPath}, ID: ${existingFolder.id}`);
            } else {
                // 创建新文件夹
                const createResponse = await Http.post('/api/folders', {
                    name: folderName,
                    parentId: currentParentId
                });

                if (createResponse.code === 200) {
                    currentParentId = createResponse.data.id;
                    folderIdMap[currentPath] = createResponse.data.id;
                    console.log(`创建文件夹成功: ${currentPath}, ID: ${createResponse.data.id}`);
                } else {
                    throw new Error(`创建文件夹失败: ${createResponse.message}`);
                }
            }
        } catch (error) {
            console.error(`处理文件夹 ${currentPath} 时出错:`, error);
            throw error;
        }
    }

    return currentParentId;
}

/**
 * 取消全部上传
 */
function cancelAll() {
    if (!confirm('确定要取消所有上传吗？')) {
        return;
    }
    
    uploadQueue.forEach(item => {
        if (item.xhr) {
            item.xhr.abort();
        }
    });
    
    uploadQueue = [];
    document.getElementById('uploadItems').innerHTML = '';
    
    // 隐藏上传列表
    document.getElementById('uploadList').style.display = 'none';
    document.getElementById('uploadStats').style.display = 'none';
    
    // 重置统计
    uploadStats = {
        totalFiles: 0,
        completedFiles: 0,
        totalSize: 0,
        uploadedSize: 0,
        startTime: null
    };
}

/**
 * 生成唯一ID
 */
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * 用户菜单切换
 */
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

/**
 * 用户登出
 */
async function logout() {
    try {
        const response = await Http.post('/api/auth/logout');
        
        if (response.code === 200) {
            Toast.success('登出成功');
            setTimeout(() => {
                window.location.href = '/login';
            }, 1000);
        } else {
            Toast.error(response.message || '登出失败');
        }
    } catch (error) {
        console.error('Logout error:', error);
        Toast.error('网络错误，请稍后重试');
    }
}

// 点击其他地方关闭用户菜单
document.addEventListener('click', function(e) {
    const userMenu = document.querySelector('.user-menu');
    const dropdown = document.getElementById('userDropdown');
    
    if (userMenu && dropdown && !userMenu.contains(e.target)) {
        dropdown.classList.remove('show');
    }
});
