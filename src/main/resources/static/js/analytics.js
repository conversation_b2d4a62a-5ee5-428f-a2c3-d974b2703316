// 数据分析页面JavaScript

let downloadTrendChart, accessTrendChart, actionTypeChart, fileTypeChart;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    loadAnalyticsData();
});

// 初始化页面
function initializePage() {
    // 设置默认时间范围（最近30天）
    const endTime = new Date();
    const startTime = new Date();
    startTime.setDate(startTime.getDate() - 30);
    
    document.getElementById('startTime').value = formatDateTimeLocal(startTime);
    document.getElementById('endTime').value = formatDateTimeLocal(endTime);
    
    // 初始化图表
    initializeCharts();
}

// 格式化日期时间为本地格式
function formatDateTimeLocal(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

// 设置快速时间范围
function setQuickRange(range) {
    const endTime = new Date();
    const startTime = new Date();
    
    switch(range) {
        case 'today':
            startTime.setHours(0, 0, 0, 0);
            break;
        case 'week':
            startTime.setDate(startTime.getDate() - 7);
            break;
        case 'month':
            startTime.setDate(startTime.getDate() - 30);
            break;
    }
    
    document.getElementById('startTime').value = formatDateTimeLocal(startTime);
    document.getElementById('endTime').value = formatDateTimeLocal(endTime);
    
    refreshData();
}

// 刷新数据
function refreshData() {
    showLoading();
    loadAnalyticsData();
}

// 显示加载状态
function showLoading() {
    document.getElementById('loadingOverlay').style.display = 'flex';
}

// 隐藏加载状态
function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

// 加载分析数据
async function loadAnalyticsData() {
    try {
        const startTime = document.getElementById('startTime').value;
        const endTime = document.getElementById('endTime').value;
        
        // 并行加载所有数据
        const [
            downloadOverview,
            accessStats,
            storageStats,
            userActivity,
            downloadTrends,
            popularFiles,
            activeUsers
        ] = await Promise.all([
            fetchData('/api/admin/analytics/download-overview', { startTime, endTime }),
            fetchData('/api/admin/analytics/access-stats', { startTime, endTime }),
            fetchData('/api/admin/analytics/storage-stats'),
            fetchData('/api/admin/analytics/user-activity', { startTime, endTime }),
            fetchData('/api/admin/analytics/download-trends', { startTime, endTime }),
            fetchData('/api/admin/analytics/popular-files', { limit: 10 }),
            fetchData('/api/admin/analytics/active-users', { limit: 10 })
        ]);
        
        // 更新概览卡片
        updateOverviewCards(downloadOverview, accessStats, storageStats, userActivity);
        
        // 更新图表
        updateCharts(downloadTrends, accessStats, userActivity, storageStats);
        
        // 更新表格
        updateTables(popularFiles, activeUsers);
        
        hideLoading();
    } catch (error) {
        console.error('加载分析数据失败:', error);
        showToast('加载数据失败: ' + error.message, 'error');
        hideLoading();
    }
}

// 获取数据
async function fetchData(url, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const fullUrl = queryString ? `${url}?${queryString}` : url;
    
    const response = await fetch(fullUrl);
    const result = await response.json();
    
    if (result.success) {
        return result.data;
    } else {
        throw new Error(result.message || '请求失败');
    }
}

// 更新概览卡片
function updateOverviewCards(downloadOverview, accessStats, storageStats, userActivity) {
    // 下载统计
    document.getElementById('totalDownloads').textContent = formatNumber(downloadOverview.totalDownloads || 0);
    document.getElementById('todayDownloads').textContent = formatNumber(downloadOverview.todayDownloads || 0);
    
    const growthRate = downloadOverview.growthRate || 0;
    const growthElement = document.getElementById('growthRate');
    growthElement.textContent = formatPercentage(growthRate);
    growthElement.className = 'stat-value ' + (growthRate > 0 ? 'positive' : growthRate < 0 ? 'negative' : 'neutral');
    
    // 访问统计
    document.getElementById('totalAccess').textContent = formatNumber(accessStats.totalAccess || 0);
    document.getElementById('todayAccess').textContent = formatNumber(accessStats.todayAccess || 0);
    document.getElementById('uniqueIps').textContent = formatNumber(accessStats.frequentIps?.length || 0);
    
    // 存储统计
    document.getElementById('totalFiles').textContent = formatNumber(storageStats.totalFiles || 0);
    document.getElementById('totalSize').textContent = Utils.formatFileSize(storageStats.totalSize || 0);
    document.getElementById('deletedFiles').textContent = formatNumber(storageStats.deletedFiles || 0);
    
    // 用户活跃度
    document.getElementById('totalActions').textContent = formatNumber(userActivity.totalActions || 0);
    document.getElementById('activeUsers').textContent = formatNumber(userActivity.activeUsers?.length || 0);
    
    const avgActions = userActivity.totalActions && userActivity.activeUsers?.length 
        ? Math.round(userActivity.totalActions / userActivity.activeUsers.length) 
        : 0;
    document.getElementById('avgActions').textContent = formatNumber(avgActions);
}

// 初始化图表
function initializeCharts() {
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    };
    
    // 下载趋势图表
    const downloadCtx = document.getElementById('downloadTrendChart').getContext('2d');
    downloadTrendChart = new Chart(downloadCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '下载次数',
                data: [],
                borderColor: '#ff6b9d',
                backgroundColor: 'rgba(255, 107, 157, 0.1)',
                tension: 0.4
            }]
        },
        options: chartOptions
    });
    
    // 访问趋势图表
    const accessCtx = document.getElementById('accessTrendChart').getContext('2d');
    accessTrendChart = new Chart(accessCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '访问次数',
                data: [],
                borderColor: '#ff8fab',
                backgroundColor: 'rgba(255, 143, 171, 0.1)',
                tension: 0.4
            }]
        },
        options: chartOptions
    });
    
    // 操作类型分布图表
    const actionCtx = document.getElementById('actionTypeChart').getContext('2d');
    actionTypeChart = new Chart(actionCtx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#ff6b9d',
                    '#ff8fab',
                    '#ffb3d1',
                    '#ffc9e0',
                    '#ffe0f0'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // 文件类型分布图表
    const fileTypeCtx = document.getElementById('fileTypeChart').getContext('2d');
    fileTypeChart = new Chart(fileTypeCtx, {
        type: 'pie',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#ff6b9d',
                    '#ff8fab',
                    '#ffb3d1',
                    '#ffc9e0'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// 更新图表
function updateCharts(downloadTrends, accessStats, userActivity, storageStats) {
    // 更新下载趋势图表
    if (downloadTrends.dailyStats) {
        const labels = downloadTrends.dailyStats.map(item => item.date);
        const data = downloadTrends.dailyStats.map(item => item.count);
        
        downloadTrendChart.data.labels = labels;
        downloadTrendChart.data.datasets[0].data = data;
        downloadTrendChart.update();
    }
    
    // 更新访问趋势图表
    if (accessStats.dailyAccess) {
        const labels = accessStats.dailyAccess.map(item => item.date);
        const data = accessStats.dailyAccess.map(item => item.count);
        
        accessTrendChart.data.labels = labels;
        accessTrendChart.data.datasets[0].data = data;
        accessTrendChart.update();
    }
    
    // 更新操作类型分布图表
    if (userActivity.actionStats) {
        const labels = userActivity.actionStats.map(item => item.action);
        const data = userActivity.actionStats.map(item => item.count);
        
        actionTypeChart.data.labels = labels;
        actionTypeChart.data.datasets[0].data = data;
        actionTypeChart.update();
    }
    
    // 更新文件类型分布图表
    if (storageStats.typeStats) {
        const labels = Object.keys(storageStats.typeStats);
        const data = Object.values(storageStats.typeStats);
        
        fileTypeChart.data.labels = labels;
        fileTypeChart.data.datasets[0].data = data;
        fileTypeChart.update();
    }
}

// 更新表格
function updateTables(popularFiles, activeUsers) {
    // 更新热门文件表格
    const popularFilesTable = document.getElementById('popularFilesTable').getElementsByTagName('tbody')[0];
    popularFilesTable.innerHTML = '';
    
    if (popularFiles && popularFiles.length > 0) {
        popularFiles.forEach(file => {
            const row = popularFilesTable.insertRow();
            row.innerHTML = `
                <td>${file.fileName || '未知文件'}</td>
                <td class="number">${formatNumber(file.downloadCount || 0)}</td>
                <td class="number">${Utils.formatFileSize(file.fileSize || 0)}</td>
                <td>${formatDateTime(file.uploadTime)}</td>
            `;
        });
    } else {
        const row = popularFilesTable.insertRow();
        row.innerHTML = '<td colspan="4" style="text-align: center; color: #999;">暂无数据</td>';
    }
    
    // 更新活跃用户表格
    const activeUsersTable = document.getElementById('activeUsersTable').getElementsByTagName('tbody')[0];
    activeUsersTable.innerHTML = '';
    
    if (activeUsers && activeUsers.length > 0) {
        activeUsers.forEach(user => {
            const row = activeUsersTable.insertRow();
            row.innerHTML = `
                <td>${user.username || '未知用户'}</td>
                <td class="number">${formatNumber(user.actionCount || 0)}</td>
                <td class="number">${formatNumber(user.downloadCount || 0)}</td>
                <td>${formatDateTime(user.lastActive)}</td>
            `;
        });
    } else {
        const row = activeUsersTable.insertRow();
        row.innerHTML = '<td colspan="4" style="text-align: center; color: #999;">暂无数据</td>';
    }
}

// 导出报告
async function exportReport() {
    try {
        showLoading();
        
        const startTime = document.getElementById('startTime').value;
        const endTime = document.getElementById('endTime').value;
        
        const report = await fetchData('/api/admin/analytics/report', { startTime, endTime });
        
        // 创建并下载报告文件
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `analytics-report-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showToast('报告导出成功', 'success');
        hideLoading();
    } catch (error) {
        console.error('导出报告失败:', error);
        showToast('导出报告失败: ' + error.message, 'error');
        hideLoading();
    }
}

// 生成详细报告
async function generateReport() {
    try {
        showLoading();
        
        const startTime = document.getElementById('startTime').value;
        const endTime = document.getElementById('endTime').value;
        
        const report = await fetchData('/api/admin/analytics/report', { startTime, endTime });
        
        // 在新窗口中显示报告
        const reportWindow = window.open('', '_blank');
        reportWindow.document.write(`
            <html>
                <head>
                    <title>数据分析报告</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .section { margin-bottom: 20px; }
                        .section h3 { color: #ff6b9d; border-bottom: 2px solid #ff6b9d; padding-bottom: 5px; }
                        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; }
                        .number { text-align: right; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>数据分析报告</h1>
                        <p>报告期间: ${startTime} 至 ${endTime}</p>
                    </div>
                    <div class="section">
                        <h3>概要统计</h3>
                        <table>
                            <tr><td>总下载次数</td><td class="number">${formatNumber(report.summary.totalDownloads)}</td></tr>
                            <tr><td>总访问次数</td><td class="number">${formatNumber(report.summary.totalAccess)}</td></tr>
                            <tr><td>总操作次数</td><td class="number">${formatNumber(report.summary.totalActions)}</td></tr>
                            <tr><td>总文件数</td><td class="number">${formatNumber(report.summary.totalFiles)}</td></tr>
                            <tr><td>总用户数</td><td class="number">${formatNumber(report.summary.totalUsers)}</td></tr>
                        </table>
                    </div>
                    <div class="section">
                        <h3>热门文件</h3>
                        <table>
                            <thead>
                                <tr><th>文件名</th><th>下载次数</th></tr>
                            </thead>
                            <tbody>
                                ${report.topFiles.map(file => `
                                    <tr>
                                        <td>${file.fileName || '未知文件'}</td>
                                        <td class="number">${formatNumber(file.downloadCount)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </body>
            </html>
        `);
        
        hideLoading();
    } catch (error) {
        console.error('生成报告失败:', error);
        showToast('生成报告失败: ' + error.message, 'error');
        hideLoading();
    }
}

// 工具函数
function formatNumber(num) {
    return new Intl.NumberFormat('zh-CN').format(num);
}

function formatPercentage(num) {
    return (num >= 0 ? '+' : '') + num.toFixed(1) + '%';
}

// formatFileSize函数已统一使用Utils.formatFileSize

// formatDateTime函数已统一使用Utils.formatDateTime
function formatDateTime(dateString) {
    if (!dateString) return '-';
    return Utils.formatDateTime(dateString);
}

function showToast(message, type = 'info') {
    // 这里可以集成现有的toast组件
    console.log(`${type.toUpperCase()}: ${message}`);
}
