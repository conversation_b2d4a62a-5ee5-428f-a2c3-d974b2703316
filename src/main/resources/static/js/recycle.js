/**
 * 回收站页面JavaScript
 */

let selectedItems = new Set();

document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initRecyclePage();
    
    // 绑定事件
    bindEvents();
});

/**
 * 初始化回收站页面
 */
function initRecyclePage() {
    // 检查是否有文件
    const fileItems = document.querySelectorAll('.file-item');
    const clearBtn = document.getElementById('clearBtn');
    
    if (fileItems.length === 0 && clearBtn) {
        clearBtn.disabled = true;
        clearBtn.style.opacity = '0.5';
    }
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 全选/取消全选
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'a') {
            e.preventDefault();
            selectAll();
        }
        if (e.key === 'Escape') {
            clearSelection();
        }
    });
}

/**
 * 更新选择状态
 */
function updateSelection() {
    const checkboxes = document.querySelectorAll('.file-item input[type="checkbox"]');
    selectedItems.clear();
    
    checkboxes.forEach(checkbox => {
        const item = checkbox.closest('.file-item');
        if (checkbox.checked) {
            const itemType = checkbox.getAttribute('data-type') || 'file';
            const itemId = checkbox.value;
            selectedItems.add({
                id: itemId,
                type: itemType
            });
            item.classList.add('selected');
        } else {
            item.classList.remove('selected');
        }
    });
    
    // 更新批量操作栏
    const batchActions = document.getElementById('batchActions');
    const selectedCount = document.getElementById('selectedCount');
    
    if (selectedItems.size > 0) {
        batchActions.style.display = 'flex';
        selectedCount.textContent = selectedItems.size;
    } else {
        batchActions.style.display = 'none';
    }
    
    // 更新全选状态
    updateSelectAllState();
}

/**
 * 更新全选按钮状态
 */
function updateSelectAllState() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.file-item input[type="checkbox"]');
    
    if (!selectAll || checkboxes.length === 0) return;
    
    const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
    
    if (checkedCount === 0) {
        selectAll.checked = false;
        selectAll.indeterminate = false;
    } else if (checkedCount === checkboxes.length) {
        selectAll.checked = true;
        selectAll.indeterminate = false;
    } else {
        selectAll.checked = false;
        selectAll.indeterminate = true;
    }
}

/**
 * 全选/取消全选
 */
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.file-item input[type="checkbox"]');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelection();
}

/**
 * 全选
 */
function selectAll() {
    const checkboxes = document.querySelectorAll('.file-item input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelection();
}

/**
 * 清除选择
 */
function clearSelection() {
    const checkboxes = document.querySelectorAll('.file-item input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelection();
}

/**
 * 恢复文件
 */
async function restoreFile(fileId) {
    if (!confirm('确定要恢复这个文件吗？')) {
        return;
    }
    
    try {
        const response = await Http.post(`/api/recycle/${fileId}/restore`);
        if (response.code === 200) {
            Toast.success('文件恢复成功');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            Toast.error(response.message || '恢复失败');
        }
    } catch (error) {
        console.error('Restore file error:', error);
        Toast.error('恢复失败');
    }
}

/**
 * 永久删除文件
 */
async function permanentDeleteFile(fileId) {
    if (!confirm('确定要永久删除这个文件吗？此操作不可恢复！')) {
        return;
    }

    try {
        const response = await Http.delete(`/api/recycle/${fileId}`);
        if (response.code === 200) {
            Toast.success('文件已永久删除');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            Toast.error(response.message || '删除失败');
        }
    } catch (error) {
        console.error('Permanent delete file error:', error);
        Toast.error('删除失败');
    }
}

/**
 * 恢复文件夹
 */
async function restoreFolder(folderId) {
    if (!confirm('确定要恢复这个文件夹吗？文件夹内的所有内容也会被恢复。')) {
        return;
    }

    try {
        const response = await Http.post(`/api/recycle/folders/${folderId}/restore`);
        if (response.code === 200) {
            Toast.success('文件夹恢复成功');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            Toast.error(response.message || '恢复失败');
        }
    } catch (error) {
        console.error('Restore folder error:', error);
        Toast.error('恢复失败');
    }
}

/**
 * 永久删除文件夹
 */
async function permanentDeleteFolder(folderId) {
    if (!confirm('确定要永久删除这个文件夹吗？文件夹内的所有内容也会被永久删除，此操作不可恢复！')) {
        return;
    }

    try {
        const response = await Http.delete(`/api/recycle/folders/${folderId}`);
        if (response.code === 200) {
            Toast.success('文件夹已永久删除');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            Toast.error(response.message || '删除失败');
        }
    } catch (error) {
        console.error('Permanent delete folder error:', error);
        Toast.error('删除失败');
    }
}

/**
 * 批量恢复文件和文件夹
 */
async function batchRestore() {
    if (selectedItems.size === 0) {
        Toast.warning('请先选择要恢复的项目');
        return;
    }

    const itemsArray = Array.from(selectedItems);
    const fileCount = itemsArray.filter(item => item.type === 'file').length;
    const folderCount = itemsArray.filter(item => item.type === 'folder').length;

    let confirmMessage = `确定要恢复选中的 ${selectedItems.size} 个项目吗？`;
    if (fileCount > 0 && folderCount > 0) {
        confirmMessage = `确定要恢复选中的 ${fileCount} 个文件和 ${folderCount} 个文件夹吗？`;
    } else if (fileCount > 0) {
        confirmMessage = `确定要恢复选中的 ${fileCount} 个文件吗？`;
    } else if (folderCount > 0) {
        confirmMessage = `确定要恢复选中的 ${folderCount} 个文件夹吗？`;
    }

    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        let successCount = 0;
        let failCount = 0;

        // 分别处理文件和文件夹
        for (const item of itemsArray) {
            try {
                if (item.type === 'file') {
                    await Http.post(`/api/recycle/${item.id}/restore`);
                } else if (item.type === 'folder') {
                    await Http.post(`/api/recycle/folders/${item.id}/restore`);
                }
                successCount++;
            } catch (error) {
                console.error(`恢复${item.type === 'file' ? '文件' : '文件夹'}失败:`, item.id, error);
                failCount++;
            }
        }

        let message = `批量恢复完成，成功恢复 ${successCount} 个项目`;
        if (failCount > 0) {
            message += `，${failCount} 个项目恢复失败`;
        }

        Toast.success(message);
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    } catch (error) {
        console.error('Batch restore error:', error);
        Toast.error('批量恢复失败');
    }
}

/**
 * 批量永久删除文件和文件夹
 */
async function batchPermanentDelete() {
    if (selectedItems.size === 0) {
        Toast.warning('请先选择要删除的项目');
        return;
    }

    const itemsArray = Array.from(selectedItems);
    const fileCount = itemsArray.filter(item => item.type === 'file').length;
    const folderCount = itemsArray.filter(item => item.type === 'folder').length;

    let confirmMessage = `确定要永久删除选中的 ${selectedItems.size} 个项目吗？此操作不可恢复！`;
    if (fileCount > 0 && folderCount > 0) {
        confirmMessage = `确定要永久删除选中的 ${fileCount} 个文件和 ${folderCount} 个文件夹吗？此操作不可恢复！`;
    } else if (fileCount > 0) {
        confirmMessage = `确定要永久删除选中的 ${fileCount} 个文件吗？此操作不可恢复！`;
    } else if (folderCount > 0) {
        confirmMessage = `确定要永久删除选中的 ${folderCount} 个文件夹吗？此操作不可恢复！`;
    }

    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        let successCount = 0;
        let failCount = 0;

        // 分别处理文件和文件夹
        for (const item of itemsArray) {
            try {
                if (item.type === 'file') {
                    await Http.delete(`/api/recycle/${item.id}`);
                } else if (item.type === 'folder') {
                    await Http.delete(`/api/recycle/folders/${item.id}`);
                }
                successCount++;
            } catch (error) {
                console.error(`删除${item.type === 'file' ? '文件' : '文件夹'}失败:`, item.id, error);
                failCount++;
            }
        }

        let message = `批量删除完成，成功删除 ${successCount} 个项目`;
        if (failCount > 0) {
            message += `，${failCount} 个项目删除失败`;
        }

        Toast.success(message);
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    } catch (error) {
        console.error('Batch permanent delete error:', error);
        Toast.error('批量删除失败');
    }
}

/**
 * 清空回收站
 */
async function clearRecycle() {
    if (!confirm('确定要清空回收站吗？所有文件和文件夹将被永久删除，此操作不可恢复！')) {
        return;
    }

    try {
        const response = await Http.delete('/api/recycle/clear');
        if (response.code === 200) {
            Toast.success(response.data || '回收站已清空');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            Toast.error(response.message || '清空失败');
        }
    } catch (error) {
        console.error('Clear recycle error:', error);
        Toast.error('清空失败');
    }
}

/**
 * 用户菜单切换
 */
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

/**
 * 用户登出
 */
async function logout() {
    try {
        const response = await Http.post('/api/auth/logout');
        if (response.code === 200) {
            Toast.success('登出成功');
            setTimeout(() => {
                window.location.href = '/login';
            }, 1000);
        } else {
            Toast.error(response.message || '登出失败');
        }
    } catch (error) {
        console.error('Logout error:', error);
        Toast.error('网络错误，请稍后重试');
    }
}

// 点击其他地方关闭用户菜单
document.addEventListener('click', function(e) {
    const userMenu = document.querySelector('.user-menu');
    const dropdown = document.getElementById('userDropdown');
    if (userMenu && dropdown && !userMenu.contains(e.target)) {
        dropdown.classList.remove('show');
    }
});
