/**
 * 管理员用户管理JavaScript
 */

// 全局变量
let currentPage = 1;
let pageSize = 20;
let totalUsers = 0;
let selectedUsers = new Set();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initUserManagement();
    loadUsers();
});

/**
 * 初始化用户管理
 */
function initUserManagement() {
    console.log('用户管理初始化');
    
    // 绑定搜索事件
    document.getElementById('searchUsername').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchUsers();
        }
    });
    
    document.getElementById('searchEmail').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchUsers();
        }
    });
    
    // 绑定筛选事件
    document.getElementById('roleFilter').addEventListener('change', searchUsers);
    document.getElementById('statusFilter').addEventListener('change', searchUsers);
}

/**
 * 加载用户列表
 */
function loadUsers() {
    const username = document.getElementById('searchUsername').value.trim();
    const email = document.getElementById('searchEmail').value.trim();
    const role = document.getElementById('roleFilter').value;
    const status = document.getElementById('statusFilter').value;
    
    const params = new URLSearchParams({
        page: currentPage,
        size: pageSize
    });
    
    if (username) params.append('username', username);
    if (email) params.append('email', email);
    if (role) params.append('role', role);
    if (status) params.append('status', status);
    
    fetch(`/api/admin/users?${params}`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                displayUsers(result.data.users);
                updatePagination(result.data.total);
                updateUserCount(result.data.total);
            } else {
                Toast.error(result.message || '加载用户列表失败');
            }
        })
        .catch(error => {
            console.error('加载用户列表出错:', error);
            Toast.error('加载失败，请重试');
        });
}

/**
 * 显示用户列表
 */
function displayUsers(users) {
    const tbody = document.getElementById('usersTableBody');
    
    if (!users || users.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="empty-cell">
                    <div class="empty-state">
                        <div class="empty-icon">👥</div>
                        <p>暂无用户数据</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = users.map(user => `
        <tr data-user-id="${user.id}">
            <td>
                <input type="checkbox" value="${user.id}" onchange="updateSelection()">
            </td>
            <td>
                <div class="user-info">
                    <div class="user-avatar-small">
                        <img src="${user.avatar || '/static/images/default-avatar.svg'}" alt="头像">
                    </div>
                    <div class="user-details">
                        <div class="user-name">${user.nickname || user.username}</div>
                        <div class="user-meta">${user.username}</div>
                        <div class="user-meta">${user.email || '未设置邮箱'}</div>
                    </div>
                </div>
            </td>
            <td>
                <span class="role-badge ${user.role.toLowerCase()}">${getRoleText(user.role)}</span>
            </td>
            <td>
                <span class="status-badge ${user.status.toLowerCase()}">${getStatusText(user.status)}</span>
            </td>
            <td>
                <div class="storage-info">
                    <div class="storage-text">${Utils.formatFileSize(user.storageUsed)} / ${user.storageLimit && user.storageLimit > 0 ? Utils.formatFileSize(user.storageLimit) : '∞'}</div>
                    <div class="storage-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${user.storageLimit && user.storageLimit > 0 ? Math.min(100, (user.storageUsed / user.storageLimit) * 100) : 0}%"></div>
                        </div>
                    </div>
                </div>
            </td>
            <td>${Utils.formatDateTime(user.createdTime)}</td>
            <td>${user.lastLoginTime ? Utils.formatDateTime(user.lastLoginTime) : '从未登录'}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn" onclick="editUser(${user.id})">编辑</button>
                    <button class="action-btn" onclick="viewUserFiles(${user.id})">文件</button>
                    ${user.role !== 'ADMIN' ? `<button class="action-btn delete-btn" onclick="deleteUser(${user.id})">删除</button>` : ''}
                </div>
            </td>
        </tr>
    `).join('');
}

/**
 * 获取角色文本
 */
function getRoleText(role) {
    const roleMap = {
        'ADMIN': '管理员',
        'USER': '普通用户'
    };
    return roleMap[role] || role;
}

/**
 * 获取状态文本
 */
function getStatusText(status) {
    const statusMap = {
        'ACTIVE': '正常',
        'DISABLED': '禁用',
        'LOCKED': '锁定'
    };
    return statusMap[status] || status;
}

/**
 * 更新分页
 */
function updatePagination(total) {
    totalUsers = total;
    const totalPages = Math.ceil(total / pageSize);
    const pagination = document.getElementById('pagination');
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // 上一页
    if (currentPage > 1) {
        paginationHTML += `<button class="page-btn" onclick="goToPage(${currentPage - 1})">上一页</button>`;
    }
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    if (startPage > 1) {
        paginationHTML += `<button class="page-btn" onclick="goToPage(1)">1</button>`;
        if (startPage > 2) {
            paginationHTML += `<span class="page-ellipsis">...</span>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `<button class="page-btn ${i === currentPage ? 'active' : ''}" onclick="goToPage(${i})">${i}</button>`;
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHTML += `<span class="page-ellipsis">...</span>`;
        }
        paginationHTML += `<button class="page-btn" onclick="goToPage(${totalPages})">${totalPages}</button>`;
    }
    
    // 下一页
    if (currentPage < totalPages) {
        paginationHTML += `<button class="page-btn" onclick="goToPage(${currentPage + 1})">下一页</button>`;
    }
    
    pagination.innerHTML = paginationHTML;
    
    // 更新页面信息
    document.getElementById('pageInfo').textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
}

/**
 * 跳转到指定页面
 */
function goToPage(page) {
    currentPage = page;
    loadUsers();
}

/**
 * 更新用户数量显示
 */
function updateUserCount(total) {
    document.getElementById('userCount').textContent = `总计: ${total} 个用户`;
}

/**
 * 搜索用户
 */
function searchUsers() {
    currentPage = 1;
    loadUsers();
}

/**
 * 重置搜索
 */
function resetSearch() {
    document.getElementById('searchUsername').value = '';
    document.getElementById('searchEmail').value = '';
    document.getElementById('roleFilter').value = '';
    document.getElementById('statusFilter').value = '';
    currentPage = 1;
    loadUsers();
}

/**
 * 更新选择状态
 */
function updateSelection() {
    const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
    selectedUsers.clear();
    
    checkboxes.forEach(checkbox => {
        if (checkbox.checked) {
            selectedUsers.add(parseInt(checkbox.value));
        }
    });
    
    const batchActions = document.getElementById('batchActions');
    const selectedCount = document.getElementById('selectedCount');
    
    if (selectedUsers.size > 0) {
        batchActions.style.display = 'flex';
        selectedCount.textContent = selectedUsers.size;
    } else {
        batchActions.style.display = 'none';
    }
    
    // 更新全选状态
    const selectAll = document.getElementById('selectAll');
    selectAll.checked = checkboxes.length > 0 && selectedUsers.size === checkboxes.length;
    selectAll.indeterminate = selectedUsers.size > 0 && selectedUsers.size < checkboxes.length;
}

/**
 * 全选/取消全选
 */
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelection();
}

/**
 * 清除选择
 */
function clearSelection() {
    const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    
    document.getElementById('selectAll').checked = false;
    updateSelection();
}

/**
 * 显示创建用户模态框
 */
function showCreateUserModal() {
    document.getElementById('createUserModal').style.display = 'flex';
    document.getElementById('createUserForm').reset();
}

/**
 * 隐藏创建用户模态框
 */
function hideCreateUserModal() {
    document.getElementById('createUserModal').style.display = 'none';
}

/**
 * 创建用户
 */
function createUser() {
    const form = document.getElementById('createUserForm');
    const formData = new FormData(form);
    
    // 验证表单
    if (!form.checkValidity()) {
        Toast.error('请填写必填字段');
        return;
    }
    
    // 转换存储限制为字节
    const storageLimitGB = parseFloat(formData.get('storageLimit'));
    formData.set('storageLimit', (storageLimitGB * 1024 * 1024 * 1024).toString());
    
    fetch('/api/admin/users', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success('用户创建成功');
            hideCreateUserModal();
            loadUsers();
        } else {
            Toast.error(result.message || '创建失败');
        }
    })
    .catch(error => {
        console.error('创建用户出错:', error);
        Toast.error('创建失败，请重试');
    });
}

/**
 * 编辑用户
 */
function editUser(userId) {
    console.log('编辑用户:', userId);
    
    // 显示加载状态
    Toast.info('正在获取用户信息...');
    
    // 获取用户信息
    fetch(`/api/admin/users/${userId}`)
        .then(response => {
            console.log('获取用户信息响应状态:', response.status);
            return response.json();
        })
        .then(result => {
            console.log('获取用户信息结果:', result);
            if (result.success) {
                const user = result.data;
                
                // 填充表单
                document.getElementById('editUserId').value = user.id;
                document.getElementById('editUsername').value = user.username;
                document.getElementById('editEmail').value = user.email || '';
                document.getElementById('editNickname').value = user.nickname || '';
                document.getElementById('editRole').value = user.role;
                document.getElementById('editStatus').value = user.status;
                document.getElementById('editStorageLimit').value = (user.storageLimit / (1024 * 1024 * 1024)).toFixed(1);
                
                // 显示模态框
                const modal = document.getElementById('editUserModal');
                if (modal) {
                    modal.style.display = 'flex';
                    console.log('模态框已显示');
                    Toast.success('用户信息加载完成');
                } else {
                    console.error('未找到editUserModal元素');
                    Toast.error('无法显示编辑框');
                }
            } else {
                Toast.error(result.message || '获取用户信息失败');
            }
        })
        .catch(error => {
            console.error('获取用户信息出错:', error);
            Toast.error('获取用户信息失败: ' + error.message);
        });
}

/**
 * 隐藏编辑用户模态框
 */
function hideEditUserModal() {
    document.getElementById('editUserModal').style.display = 'none';
}

/**
 * 更新用户
 */
function updateUser() {
    const form = document.getElementById('editUserForm');
    const formData = new FormData(form);
    const userId = formData.get('userId');
    
    // 验证表单
    if (!form.checkValidity()) {
        Toast.error('请填写必填字段');
        return;
    }
    
    // 转换存储限制为字节
    const storageLimitGB = parseFloat(formData.get('storageLimit'));
    formData.set('storageLimit', (storageLimitGB * 1024 * 1024 * 1024).toString());
    
    fetch(`/api/admin/users/${userId}`, {
        method: 'PUT',
        body: formData
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success('用户更新成功');
            hideEditUserModal();
            loadUsers();
        } else {
            Toast.error(result.message || '更新失败');
        }
    })
    .catch(error => {
        console.error('更新用户出错:', error);
        Toast.error('更新失败，请重试');
    });
}

/**
 * 删除用户
 */
function deleteUser(userId) {
    if (!confirm('确定要删除这个用户吗？此操作将同时删除用户的所有文件，且不可恢复！')) {
        return;
    }
    
    fetch(`/api/admin/users/${userId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success('用户删除成功');
            loadUsers();
        } else {
            Toast.error(result.message || '删除失败');
        }
    })
    .catch(error => {
        console.error('删除用户出错:', error);
        Toast.error('删除失败，请重试');
    });
}

/**
 * 查看用户文件
 */
function viewUserFiles(userId) {
    window.open(`/admin/users/${userId}/files`, '_blank');
}

/**
 * 批量更新状态
 */
function batchUpdateStatus(status) {
    if (selectedUsers.size === 0) {
        Toast.error('请先选择用户');
        return;
    }
    
    const statusText = getStatusText(status);
    if (!confirm(`确定要将选中的 ${selectedUsers.size} 个用户状态设置为"${statusText}"吗？`)) {
        return;
    }
    
    const userIds = Array.from(selectedUsers);
    
    fetch('/api/admin/users/batch-status', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            userIds: userIds,
            status: status
        })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success(`批量更新状态成功`);
            clearSelection();
            loadUsers();
        } else {
            Toast.error(result.message || '批量更新失败');
        }
    })
    .catch(error => {
        console.error('批量更新状态出错:', error);
        Toast.error('批量更新失败，请重试');
    });
}

/**
 * 批量删除用户
 */
function batchDeleteUsers() {
    if (selectedUsers.size === 0) {
        Toast.error('请先选择用户');
        return;
    }
    
    if (!confirm(`确定要删除选中的 ${selectedUsers.size} 个用户吗？此操作将同时删除用户的所有文件，且不可恢复！`)) {
        return;
    }
    
    const userIds = Array.from(selectedUsers);
    
    fetch('/api/admin/users/batch-delete', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            userIds: userIds
        })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success(`批量删除成功`);
            clearSelection();
            loadUsers();
        } else {
            Toast.error(result.message || '批量删除失败');
        }
    })
    .catch(error => {
        console.error('批量删除出错:', error);
        Toast.error('批量删除失败，请重试');
    });
}

/**
 * 导出用户
 */
function exportUsers() {
    const link = document.createElement('a');
    link.href = '/api/admin/users/export';
    link.download = `users-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    Toast.success('用户数据导出中...');
}
