/**
 * 仪表板JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化仪表板
    initDashboard();
    
    // 加载统计数据
    loadStats();
    
    // 加载最近文件
    loadRecentFiles();
});

/**
 * 初始化仪表板
 */
function initDashboard() {
    // 点击其他地方关闭用户菜单
    document.addEventListener('click', function(e) {
        const userDropdown = document.getElementById('userDropdown');
        const userAvatar = document.querySelector('.user-avatar');
        
        if (userDropdown && !userAvatar.contains(e.target)) {
            userDropdown.classList.remove('show');
        }
    });
    
    // 移动端侧边栏切换
    const sidebar = document.querySelector('.sidebar');
    if (window.innerWidth <= 768) {
        // 可以添加移动端菜单按钮逻辑
    }
}

/**
 * 切换用户菜单
 */
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

/**
 * 用户登出
 */
async function logout() {
    try {
        const response = await Http.post('/api/auth/logout');
        
        if (response.code === 200) {
            Toast.success('登出成功');
            setTimeout(() => {
                window.location.href = '/login';
            }, 1000);
        } else {
            Toast.error(response.message || '登出失败');
        }
    } catch (error) {
        console.error('Logout error:', error);
        Toast.error('网络错误，请稍后重试');
    }
}

/**
 * 加载统计数据
 */
async function loadStats() {
    try {
        // 调用真实的API获取用户统计数据
        const response = await fetch('/api/profile/stats', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('获取统计数据失败');
        }

        const result = await response.json();
        if (result.code !== 200) {
            throw new Error(result.message || '获取统计数据失败');
        }

        const stats = result.data;

        // 更新统计卡片
        updateStatCard('fileCount', stats.fileCount || 0);
        updateStatCard('storageUsed', Utils.formatFileSize(stats.storageUsed || 0));

        // 计算存储使用率
        let storagePercent = 0;
        if (stats.storageLimit > 0) {
            storagePercent = Math.round((stats.storageUsed / stats.storageLimit) * 100);
        }
        updateStatCard('storagePercent', storagePercent + '%');
        updateStatCard('downloadCount', stats.downloadCount || 0);

        // 更新存储进度条
        updateStorageBar(stats.storageUsed || 0, stats.storageLimit || 1610612736);

    } catch (error) {
        console.error('Load stats error:', error);
        Toast.error('加载统计数据失败');

        // 显示默认值
        updateStatCard('fileCount', 0);
        updateStatCard('storageUsed', '0 MB');
        updateStatCard('storagePercent', '0%');
        updateStatCard('downloadCount', 0);
        updateStorageBar(0, 1610612736);
    }
}

/**
 * 更新统计卡片
 */
function updateStatCard(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

/**
 * 更新存储进度条
 */
function updateStorageBar(used, limit) {
    const storageBar = document.getElementById('storageBar');
    const storageText = document.getElementById('storageText');
    
    if (!storageBar || !storageText) return;
    
    let percentage = 0;
    let text = '';
    
    if (limit === -1) {
        // 无限制
        percentage = 0;
        text = `已使用 ${Utils.formatFileSize(used)} / 无限制`;
    } else {
        percentage = Math.min((used / limit) * 100, 100);
        text = `已使用 ${Utils.formatFileSize(used)} / 总共 ${Utils.formatFileSize(limit)}`;
    }
    
    storageBar.style.width = percentage + '%';
    storageText.textContent = text;
    
    // 根据使用率改变颜色
    if (percentage > 90) {
        storageBar.style.background = 'linear-gradient(90deg, #f44336, #d32f2f)';
    } else if (percentage > 70) {
        storageBar.style.background = 'linear-gradient(90deg, #ff9800, #f57c00)';
    } else {
        storageBar.style.background = 'linear-gradient(90deg, var(--primary-color), var(--accent-color))';
    }
}

/**
 * 加载最近文件
 */
async function loadRecentFiles() {
    const fileList = document.getElementById('recentFileList');
    if (!fileList) return;

    try {
        // 调用真实的API获取最近文件
        const response = await fetch('/api/files/recent?limit=10', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('获取最近文件失败');
        }

        const result = await response.json();
        if (result.code !== 200) {
            throw new Error(result.message || '获取最近文件失败');
        }

        const files = result.data || [];

        if (files.length === 0) {
            fileList.innerHTML = `
                <div class="empty-state">
                    <p>暂无文件</p>
                    <a href="/upload" class="btn btn-primary btn-small">上传文件</a>
                </div>
            `;
        } else {
            const fileItems = files.map(file => createFileItem(file)).join('');
            fileList.innerHTML = fileItems;
        }

    } catch (error) {
        console.error('Load recent files error:', error);
        fileList.innerHTML = `
            <div class="error-state">
                <p>加载文件失败</p>
                <button class="btn btn-outline btn-small" onclick="loadRecentFiles()">重试</button>
            </div>
        `;
    }
}

/**
 * 创建文件项HTML
 */
function createFileItem(file) {
    const icon = Utils.getFileIcon(file.name);
    const formattedSize = Utils.formatFileSize(file.fileSize);
    const formattedDate = Utils.formatDate(file.createdTime);
    
    return `
        <div class="file-item">
            <div class="file-icon">${icon}</div>
            <div class="file-info">
                <div class="file-name">${file.name}</div>
                <div class="file-meta">${formattedSize} • ${formattedDate}</div>
            </div>
        </div>
    `;
}

/**
 * 响应式处理
 */
function handleResize() {
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    
    if (window.innerWidth <= 768) {
        // 移动端处理
        if (sidebar) {
            sidebar.classList.remove('show');
        }
    }
}

// 监听窗口大小变化
window.addEventListener('resize', Utils.debounce(handleResize, 250));

/**
 * 添加空状态和错误状态的样式
 */
const additionalStyles = `
    .empty-state, .error-state {
        text-align: center;
        padding: 40px 20px;
        color: var(--text-muted);
    }
    
    .empty-state p, .error-state p {
        margin-bottom: 16px;
        font-size: 16px;
    }
`;

// 动态添加样式
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
