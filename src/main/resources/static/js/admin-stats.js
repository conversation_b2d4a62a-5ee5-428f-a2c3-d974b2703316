/**
 * 管理员统计分析JavaScript
 */

// 全局变量
let uploadDownloadChart = null;
let fileTypeChart = null;
let currentTimeRange = 30;
let currentRanking = 'downloads';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initStatsPage();
    loadStatsData();
    initCharts();
});

/**
 * 初始化统计页面
 */
function initStatsPage() {
    console.log('统计分析页面初始化');
    
    // 设置定时刷新
    setInterval(loadStatsData, 60000); // 每分钟刷新一次
}

/**
 * 加载统计数据
 */
function loadStatsData() {
    Promise.all([
        loadOverviewStats(),
        loadActivityStats(),
        loadStorageStats(),
        loadRankingData()
    ]).then(() => {
        console.log('统计数据加载完成');
    }).catch(error => {
        console.error('加载统计数据失败:', error);
    });
}

/**
 * 加载概览统计
 */
function loadOverviewStats() {
    return fetch(`/api/admin/stats/overview?days=${currentTimeRange}`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                updateOverviewStats(result.data);
            } else {
                console.error('获取概览统计失败:', result.message);
            }
        });
}

/**
 * 更新概览统计显示
 */
function updateOverviewStats(stats) {
    document.getElementById('totalUploads').textContent = stats.totalUploads || 0;
    document.getElementById('totalDownloads').textContent = stats.totalDownloads || 0;
    document.getElementById('totalViews').textContent = stats.totalViews || 0;
    document.getElementById('totalShares').textContent = stats.totalShares || 0;
    
    // 更新变化趋势
    updateStatChange('uploadsChange', stats.uploadsChange || 0, '今日');
    updateStatChange('downloadsChange', stats.downloadsChange || 0, '今日');
    updateStatChange('viewsChange', stats.viewsChange || 0, '今日');
    updateStatChange('sharesChange', stats.activeShares || 0, '活跃', 'neutral');
}

/**
 * 更新统计变化显示
 */
function updateStatChange(elementId, value, suffix, type = null) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    let changeType = type;
    if (!changeType) {
        changeType = value > 0 ? 'positive' : value < 0 ? 'negative' : 'neutral';
    }
    
    element.className = `stat-change ${changeType}`;
    element.textContent = `${value > 0 ? '+' : ''}${value} ${suffix}`;
}

/**
 * 加载活跃度统计
 */
function loadActivityStats() {
    return fetch(`/api/admin/stats/activity?days=${currentTimeRange}`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                updateActivityStats(result.data);
            } else {
                console.error('获取活跃度统计失败:', result.message);
            }
        });
}

/**
 * 更新活跃度统计显示
 */
function updateActivityStats(stats) {
    document.getElementById('dailyActiveUsers').textContent = stats.dailyActiveUsers || 0;
    document.getElementById('weeklyActiveUsers').textContent = stats.weeklyActiveUsers || 0;
    document.getElementById('monthlyActiveUsers').textContent = stats.monthlyActiveUsers || 0;
    document.getElementById('avgSessionTime').textContent = `${stats.avgSessionTime || 0}分钟`;
    
    // 更新趋势
    updateTrend('dailyActiveTrend', stats.dailyActiveTrend || 0);
    updateTrend('weeklyActiveTrend', stats.weeklyActiveTrend || 0);
    updateTrend('monthlyActiveTrend', stats.monthlyActiveTrend || 0);
    updateTrend('sessionTimeTrend', stats.sessionTimeTrend || 0);
}

/**
 * 更新趋势显示
 */
function updateTrend(elementId, value) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    if (value === 0) {
        element.className = 'activity-trend neutral';
        element.textContent = '-';
    } else {
        element.className = `activity-trend ${value > 0 ? 'positive' : 'negative'}`;
        element.textContent = `${value > 0 ? '+' : ''}${value}%`;
    }
}

/**
 * 加载存储统计
 */
function loadStorageStats() {
    return fetch('/api/admin/stats/storage')
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                updateStorageStats(result.data);
            } else {
                console.error('获取存储统计失败:', result.message);
            }
        });
}

/**
 * 更新存储统计显示
 */
function updateStorageStats(stats) {
    const totalGB = (stats.totalSize / (1024 * 1024 * 1024)).toFixed(2);
    const usedGB = (stats.usedSize / (1024 * 1024 * 1024)).toFixed(2);
    const usagePercent = stats.totalSize > 0 ? (stats.usedSize / stats.totalSize * 100).toFixed(1) : 0;
    
    document.getElementById('totalStorage').textContent = `${totalGB} GB`;
    document.getElementById('usedStorage').textContent = `${usedGB} GB`;
    document.getElementById('storageUsedBar').style.width = `${usagePercent}%`;
    
    // 更新文件类型分布
    if (stats.breakdown) {
        updateStorageBreakdown(stats.breakdown, stats.totalSize);
    }
}

/**
 * 更新存储分布显示
 */
function updateStorageBreakdown(breakdown, totalSize) {
    const types = ['image', 'video', 'document', 'other'];
    
    types.forEach(type => {
        const size = breakdown[type] || 0;
        const sizeGB = (size / (1024 * 1024 * 1024)).toFixed(2);
        const percent = totalSize > 0 ? (size / totalSize * 100).toFixed(1) : 0;
        
        const sizeElement = document.getElementById(`${type}Storage`);
        const percentElement = document.getElementById(`${type}Percent`);
        
        if (sizeElement) sizeElement.textContent = `${sizeGB} GB`;
        if (percentElement) percentElement.textContent = `${percent}%`;
    });
}

/**
 * 加载排行榜数据
 */
function loadRankingData() {
    return fetch(`/api/admin/stats/ranking?type=${currentRanking}&limit=10`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                updateRankingList(result.data);
            } else {
                console.error('获取排行榜失败:', result.message);
            }
        });
}

/**
 * 更新排行榜显示
 */
function updateRankingList(rankings) {
    const container = document.getElementById('rankingList');
    
    if (!rankings || rankings.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">📊</div>
                <p>暂无排行数据</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = rankings.map((item, index) => `
        <div class="ranking-item">
            <div class="ranking-number">${index + 1}</div>
            <div class="file-info">
                <div class="file-name">${item.fileName}</div>
                <div class="file-meta">${item.fileType} • ${Utils.formatFileSize(item.fileSize)}</div>
            </div>
            <div class="ranking-value">
                ${getRankingValue(item)}
            </div>
        </div>
    `).join('');
}

/**
 * 获取排行榜数值
 */
function getRankingValue(item) {
    switch (currentRanking) {
        case 'downloads':
            return `${item.downloadCount || 0} 次下载`;
        case 'views':
            return `${item.viewCount || 0} 次浏览`;
        case 'shares':
            return `${item.shareCount || 0} 次分享`;
        default:
            return '0';
    }
}

/**
 * 初始化图表
 */
function initCharts() {
    initUploadDownloadChart();
    initFileTypeChart();
}

/**
 * 初始化上传下载趋势图
 */
function initUploadDownloadChart() {
    const ctx = document.getElementById('uploadDownloadChart').getContext('2d');
    
    uploadDownloadChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '上传量',
                data: [],
                borderColor: '#ff6b9d',
                backgroundColor: 'rgba(255, 107, 157, 0.1)',
                tension: 0.4
            }, {
                label: '下载量',
                data: [],
                borderColor: '#4ecdc4',
                backgroundColor: 'rgba(78, 205, 196, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // 加载图表数据
    loadChartData();
}

/**
 * 初始化文件类型分布图
 */
function initFileTypeChart() {
    const ctx = document.getElementById('fileTypeChart').getContext('2d');
    
    fileTypeChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['图片', '视频', '文档', '其他'],
            datasets: [{
                data: [0, 0, 0, 0],
                backgroundColor: [
                    '#ff6b9d',
                    '#4ecdc4',
                    '#45b7d1',
                    '#96ceb4'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // 加载图表数据
    loadFileTypeData();
}

/**
 * 加载图表数据
 */
function loadChartData() {
    fetch(`/api/admin/stats/chart?days=${currentTimeRange}`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                updateChartData(result.data);
            }
        })
        .catch(error => {
            console.error('加载图表数据失败:', error);
        });
}

/**
 * 更新图表数据
 */
function updateChartData(data) {
    if (uploadDownloadChart && data.timeline) {
        uploadDownloadChart.data.labels = data.timeline.labels;
        uploadDownloadChart.data.datasets[0].data = data.timeline.uploads;
        uploadDownloadChart.data.datasets[1].data = data.timeline.downloads;
        uploadDownloadChart.update();
    }
}

/**
 * 加载文件类型数据
 */
function loadFileTypeData() {
    fetch('/api/admin/stats/file-types')
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                updateFileTypeChart(result.data);
            }
        })
        .catch(error => {
            console.error('加载文件类型数据失败:', error);
        });
}

/**
 * 更新文件类型图表
 */
function updateFileTypeChart(data) {
    if (fileTypeChart && data) {
        fileTypeChart.data.datasets[0].data = [
            data.image || 0,
            data.video || 0,
            data.document || 0,
            data.other || 0
        ];
        fileTypeChart.update();
    }
}

/**
 * 切换时间范围
 */
function changeTimeRange() {
    const select = document.getElementById('timeRange');
    currentTimeRange = parseInt(select.value);
    
    // 重新加载数据
    loadStatsData();
    loadChartData();
}

/**
 * 切换排行榜类型
 */
function switchRanking(type) {
    // 更新标签状态
    document.querySelectorAll('.ranking-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[onclick="switchRanking('${type}')"]`).classList.add('active');
    
    currentRanking = type;
    loadRankingData();
}

/**
 * 刷新统计数据
 */
function refreshStats() {
    Toast.info('正在刷新数据...');
    loadStatsData().then(() => {
        Toast.success('数据刷新完成');
    });
}

/**
 * 导出报告
 */
function exportReport() {
    const link = document.createElement('a');
    link.href = `/api/admin/stats/export?days=${currentTimeRange}`;
    link.download = `stats-report-${currentTimeRange}days-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    Toast.success('报告导出中...');
}

/**
 * 查看用户详情
 */
function viewUserDetails() {
    window.open('/admin/users', '_blank');
}

/**
 * 查看存储详情
 */
function viewStorageDetails() {
    window.open('/admin/files', '_blank');
}
