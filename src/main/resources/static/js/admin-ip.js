/**
 * 管理员IP管理JavaScript
 */

// 全局变量
let currentTab = 'blacklist';
let blacklistPage = 1;
let whitelistPage = 1;
let pageSize = 20;
let selectedBlacklist = new Set();
let selectedWhitelist = new Set();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initIpManagement();
    loadBlacklist();
    loadWhitelist();
});

/**
 * 初始化IP管理
 */
function initIpManagement() {
    console.log('IP管理初始化');
    
    // 绑定搜索事件
    document.getElementById('blacklistIpSearch').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchBlacklist();
        }
    });
    
    document.getElementById('blacklistReasonSearch').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchBlacklist();
        }
    });
    
    document.getElementById('whitelistIpSearch').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchWhitelist();
        }
    });
    
    document.getElementById('whitelistDescSearch').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchWhitelist();
        }
    });
}

/**
 * 切换标签页
 */
function switchTab(tab) {
    // 更新标签头状态
    document.querySelectorAll('.tab-header').forEach(header => {
        header.classList.remove('active');
    });
    document.querySelector(`[onclick="switchTab('${tab}')"]`).classList.add('active');
    
    // 更新标签内容状态
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(tab + 'Tab').classList.add('active');
    
    currentTab = tab;
}

/**
 * 加载黑名单列表
 */
function loadBlacklist() {
    const ipAddress = document.getElementById('blacklistIpSearch').value.trim();
    const reason = document.getElementById('blacklistReasonSearch').value.trim();
    
    const params = new URLSearchParams({
        page: blacklistPage,
        size: pageSize
    });
    
    if (ipAddress) params.append('ipAddress', ipAddress);
    if (reason) params.append('reason', reason);
    
    fetch(`/api/admin/ip/blacklist?${params}`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                displayBlacklist(result.data.blacklists);
                updateBlacklistPagination(result.data.total);
            } else {
                Toast.error(result.message || '加载黑名单失败');
            }
        })
        .catch(error => {
            console.error('加载黑名单出错:', error);
            Toast.error('加载失败，请重试');
        });
}

/**
 * 显示黑名单列表（增强版本）
 */
function displayBlacklist(blacklists) {
    const tbody = document.getElementById('blacklistTableBody');
    
    if (!blacklists || blacklists.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="empty-cell">
                    <div class="empty-state">
                        <div class="empty-icon">🚫</div>
                        <p>暂无黑名单数据</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = blacklists.map(item => `
        <tr data-id="${item.id}">
            <td>
                <input type="checkbox" value="${item.id}" onchange="updateBlacklistSelection()">
            </td>
            <td>
                <div class="ip-info">
                    <span class="ip-address">${item.ipAddress}</span>
                    <span class="ip-type-badge ${item.ipType?.toLowerCase()}">${getIpTypeText(item.ipType)}</span>
                    <div class="ip-location" data-ip="${item.ipAddress}">
                        <small class="location-text">📍 ${item.location || '查询中...'}</small>
                    </div>
                </div>
            </td>
            <td>
                <span class="reason-text">${item.reason || '未填写'}</span>
            </td>
            <td>
                <span class="status-badge ${item.status?.toLowerCase()}">${getStatusText(item.status)}</span>
            </td>
            <td>
                <div class="datetime-info">
                    <div>${formatDateTime(item.createdTime)}</div>
                    <small class="text-muted">触发: ${item.triggerCount || 0}次</small>
                </div>
            </td>
            <td>
                <div class="expiry-info">
                    ${item.expiresAt ? 
                        `<span class="expiry-time ${isExpired(item.expiresAt) ? 'expired' : 'active'}">${formatDateTime(item.expiresAt)}</span>` : 
                        '<span class="permanent">永久</span>'
                    }
                </div>
            </td>
            <td>管理员</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" onclick="editBlacklistItem(${item.id})" title="编辑">
                        ✏️
                    </button>
                    <button class="action-btn ${item.status === 'ENABLED' ? 'disable-btn' : 'enable-btn'}" 
                            onclick="toggleBlacklistStatus(${item.id}, '${item.status}')" 
                            title="${item.status === 'ENABLED' ? '禁用' : '启用'}">
                        ${item.status === 'ENABLED' ? '⏸️' : '▶️'}
                    </button>
                    <button class="action-btn delete-btn" onclick="removeFromBlacklist(${item.id})" title="删除">
                        🗑️
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    // 异步查询地理位置信息
    queryIpLocations(blacklists);
}

/**
 * 加载白名单列表
 */
function loadWhitelist() {
    const ipAddress = document.getElementById('whitelistIpSearch').value.trim();
    const description = document.getElementById('whitelistDescSearch').value.trim();
    
    const params = new URLSearchParams({
        page: whitelistPage,
        size: pageSize
    });
    
    if (ipAddress) params.append('ipAddress', ipAddress);
    if (description) params.append('description', description);
    
    fetch(`/api/admin/ip/whitelist?${params}`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                displayWhitelist(result.data.whitelists);
                updateWhitelistPagination(result.data.total);
            } else {
                Toast.error(result.message || '加载白名单失败');
            }
        })
        .catch(error => {
            console.error('加载白名单出错:', error);
            Toast.error('加载失败，请重试');
        });
}

/**
 * 显示白名单列表（增强版本）
 */
function displayWhitelist(whitelists) {
    const tbody = document.getElementById('whitelistTableBody');
    
    if (!whitelists || whitelists.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="empty-cell">
                    <div class="empty-state">
                        <div class="empty-icon">✅</div>
                        <p>暂无白名单数据</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = whitelists.map(item => `
        <tr data-id="${item.id}">
            <td>
                <input type="checkbox" value="${item.id}" onchange="updateWhitelistSelection()">
            </td>
            <td>
                <div class="ip-info">
                    <span class="ip-address">${item.ipAddress}</span>
                    <span class="ip-type-badge ${item.ipType?.toLowerCase()}">${getIpTypeText(item.ipType)}</span>
                    <div class="ip-location" data-ip="${item.ipAddress}">
                        <small class="location-text">📍 ${item.location || '查询中...'}</small>
                    </div>
                </div>
            </td>
            <td>
                <span class="description-text">${item.description || '未填写'}</span>
            </td>
            <td>
                <span class="status-badge ${item.status?.toLowerCase()}">${getStatusText(item.status)}</span>
            </td>
            <td>
                <div class="datetime-info">
                    <div>${formatDateTime(item.createdTime)}</div>
                    <small class="text-muted">命中: ${item.hitCount || 0}次</small>
                </div>
            </td>
            <td>
                <div class="expiry-info">
                    ${item.expiresAt ? 
                        `<span class="expiry-time ${isExpired(item.expiresAt) ? 'expired' : 'active'}">${formatDateTime(item.expiresAt)}</span>` : 
                        '<span class="permanent">永久</span>'
                    }
                </div>
            </td>
            <td>管理员</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" onclick="editWhitelistItem(${item.id})" title="编辑">
                        ✏️
                    </button>
                    <button class="action-btn ${item.status === 'ENABLED' ? 'disable-btn' : 'enable-btn'}" 
                            onclick="toggleWhitelistStatus(${item.id}, '${item.status}')" 
                            title="${item.status === 'ENABLED' ? '禁用' : '启用'}">
                        ${item.status === 'ENABLED' ? '⏸️' : '▶️'}
                    </button>
                    <button class="action-btn delete-btn" onclick="removeFromWhitelist(${item.id})" title="删除">
                        🗑️
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    // 异步查询地理位置信息
    queryIpLocations(whitelists);
}

/**
 * 更新黑名单分页
 */
function updateBlacklistPagination(total) {
    const totalPages = Math.ceil(total / pageSize);
    const pagination = document.getElementById('blacklistPagination');
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // 上一页
    if (blacklistPage > 1) {
        paginationHTML += `<button class="page-btn" onclick="goToBlacklistPage(${blacklistPage - 1})">上一页</button>`;
    }
    
    // 页码
    const startPage = Math.max(1, blacklistPage - 2);
    const endPage = Math.min(totalPages, blacklistPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `<button class="page-btn ${i === blacklistPage ? 'active' : ''}" onclick="goToBlacklistPage(${i})">${i}</button>`;
    }
    
    // 下一页
    if (blacklistPage < totalPages) {
        paginationHTML += `<button class="page-btn" onclick="goToBlacklistPage(${blacklistPage + 1})">下一页</button>`;
    }
    
    pagination.innerHTML = paginationHTML;
}

/**
 * 更新白名单分页
 */
function updateWhitelistPagination(total) {
    const totalPages = Math.ceil(total / pageSize);
    const pagination = document.getElementById('whitelistPagination');
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // 上一页
    if (whitelistPage > 1) {
        paginationHTML += `<button class="page-btn" onclick="goToWhitelistPage(${whitelistPage - 1})">上一页</button>`;
    }
    
    // 页码
    const startPage = Math.max(1, whitelistPage - 2);
    const endPage = Math.min(totalPages, whitelistPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `<button class="page-btn ${i === whitelistPage ? 'active' : ''}" onclick="goToWhitelistPage(${i})">${i}</button>`;
    }
    
    // 下一页
    if (whitelistPage < totalPages) {
        paginationHTML += `<button class="page-btn" onclick="goToWhitelistPage(${whitelistPage + 1})">下一页</button>`;
    }
    
    pagination.innerHTML = paginationHTML;
}

/**
 * 跳转到黑名单指定页面
 */
function goToBlacklistPage(page) {
    blacklistPage = page;
    loadBlacklist();
}

/**
 * 跳转到白名单指定页面
 */
function goToWhitelistPage(page) {
    whitelistPage = page;
    loadWhitelist();
}

/**
 * 搜索黑名单
 */
function searchBlacklist() {
    blacklistPage = 1;
    loadBlacklist();
}

/**
 * 重置黑名单搜索
 */
function resetBlacklistSearch() {
    document.getElementById('blacklistIpSearch').value = '';
    document.getElementById('blacklistReasonSearch').value = '';
    blacklistPage = 1;
    loadBlacklist();
}

/**
 * 搜索白名单
 */
function searchWhitelist() {
    whitelistPage = 1;
    loadWhitelist();
}

/**
 * 重置白名单搜索
 */
function resetWhitelistSearch() {
    document.getElementById('whitelistIpSearch').value = '';
    document.getElementById('whitelistDescSearch').value = '';
    whitelistPage = 1;
    loadWhitelist();
}

/**
 * 更新黑名单选择状态
 */
function updateBlacklistSelection() {
    const checkboxes = document.querySelectorAll('#blacklistTableBody input[type="checkbox"]');
    selectedBlacklist.clear();
    
    checkboxes.forEach(checkbox => {
        if (checkbox.checked) {
            selectedBlacklist.add(parseInt(checkbox.value));
        }
    });
    
    // 更新全选状态
    const selectAll = document.getElementById('selectAllBlacklist');
    selectAll.checked = checkboxes.length > 0 && selectedBlacklist.size === checkboxes.length;
    selectAll.indeterminate = selectedBlacklist.size > 0 && selectedBlacklist.size < checkboxes.length;
}

/**
 * 更新白名单选择状态
 */
function updateWhitelistSelection() {
    const checkboxes = document.querySelectorAll('#whitelistTableBody input[type="checkbox"]');
    selectedWhitelist.clear();
    
    checkboxes.forEach(checkbox => {
        if (checkbox.checked) {
            selectedWhitelist.add(parseInt(checkbox.value));
        }
    });
    
    // 更新全选状态
    const selectAll = document.getElementById('selectAllWhitelist');
    selectAll.checked = checkboxes.length > 0 && selectedWhitelist.size === checkboxes.length;
    selectAll.indeterminate = selectedWhitelist.size > 0 && selectedWhitelist.size < checkboxes.length;
}

/**
 * 全选/取消全选黑名单
 */
function toggleSelectAllBlacklist() {
    const selectAll = document.getElementById('selectAllBlacklist');
    const checkboxes = document.querySelectorAll('#blacklistTableBody input[type="checkbox"]');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateBlacklistSelection();
}

/**
 * 全选/取消全选白名单
 */
function toggleSelectAllWhitelist() {
    const selectAll = document.getElementById('selectAllWhitelist');
    const checkboxes = document.querySelectorAll('#whitelistTableBody input[type="checkbox"]');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateWhitelistSelection();
}

/**
 * 显示添加黑名单模态框
 */
function showAddBlacklistModal() {
    document.getElementById('addBlacklistModal').style.display = 'flex';
    document.getElementById('addBlacklistForm').reset();
}

/**
 * 隐藏添加黑名单模态框
 */
function hideAddBlacklistModal() {
    document.getElementById('addBlacklistModal').style.display = 'none';
}

/**
 * 显示添加白名单模态框
 */
function showAddWhitelistModal() {
    document.getElementById('addWhitelistModal').style.display = 'flex';
    document.getElementById('addWhitelistForm').reset();
}

/**
 * 隐藏添加白名单模态框
 */
function hideAddWhitelistModal() {
    document.getElementById('addWhitelistModal').style.display = 'none';
}

/**
 * 添加到黑名单
 */
function addToBlacklist() {
    const form = document.getElementById('addBlacklistForm');
    const formData = new FormData(form);
    
    if (!form.checkValidity()) {
        Toast.error('请填写必填字段');
        return;
    }
    
    fetch('/api/admin/ip/blacklist', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success('IP已添加到黑名单');
            hideAddBlacklistModal();
            loadBlacklist();
        } else {
            Toast.error(result.message || '添加失败');
        }
    })
    .catch(error => {
        console.error('添加黑名单出错:', error);
        Toast.error('添加失败，请重试');
    });
}

/**
 * 添加到白名单
 */
function addToWhitelist() {
    const form = document.getElementById('addWhitelistForm');
    const formData = new FormData(form);
    
    if (!form.checkValidity()) {
        Toast.error('请填写必填字段');
        return;
    }
    
    fetch('/api/admin/ip/whitelist', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success('IP已添加到白名单');
            hideAddWhitelistModal();
            loadWhitelist();
        } else {
            Toast.error(result.message || '添加失败');
        }
    })
    .catch(error => {
        console.error('添加白名单出错:', error);
        Toast.error('添加失败，请重试');
    });
}

/**
 * 从黑名单移除
 */
function removeFromBlacklist(id) {
    if (!confirm('确定要从黑名单移除这个IP吗？')) {
        return;
    }
    
    fetch(`/api/admin/ip/blacklist/${id}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success('IP已从黑名单移除');
            loadBlacklist();
        } else {
            Toast.error(result.message || '移除失败');
        }
    })
    .catch(error => {
        console.error('移除黑名单出错:', error);
        Toast.error('移除失败，请重试');
    });
}

/**
 * 从白名单移除
 */
function removeFromWhitelist(id) {
    if (!confirm('确定要从白名单移除这个IP吗？')) {
        return;
    }
    
    fetch(`/api/admin/ip/whitelist/${id}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success('IP已从白名单移除');
            loadWhitelist();
        } else {
            Toast.error(result.message || '移除失败');
        }
    })
    .catch(error => {
        console.error('移除白名单出错:', error);
        Toast.error('移除失败，请重试');
    });
}

/**
 * 批量删除黑名单
 */
function batchDeleteBlacklist() {
    if (selectedBlacklist.size === 0) {
        Toast.error('请先选择要删除的黑名单');
        return;
    }
    
    if (!confirm(`确定要删除选中的 ${selectedBlacklist.size} 个黑名单记录吗？`)) {
        return;
    }
    
    const ids = Array.from(selectedBlacklist);
    
    fetch('/api/admin/ip/blacklist/batch', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(ids)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success('批量删除成功');
            selectedBlacklist.clear();
            loadBlacklist();
        } else {
            Toast.error(result.message || '批量删除失败');
        }
    })
    .catch(error => {
        console.error('批量删除黑名单出错:', error);
        Toast.error('批量删除失败，请重试');
    });
}

/**
 * 批量删除白名单
 */
function batchDeleteWhitelist() {
    if (selectedWhitelist.size === 0) {
        Toast.error('请先选择要删除的白名单');
        return;
    }
    
    if (!confirm(`确定要删除选中的 ${selectedWhitelist.size} 个白名单记录吗？`)) {
        return;
    }
    
    const ids = Array.from(selectedWhitelist);
    
    fetch('/api/admin/ip/whitelist/batch', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(ids)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success('批量删除成功');
            selectedWhitelist.clear();
            loadWhitelist();
        } else {
            Toast.error(result.message || '批量删除失败');
        }
    })
    .catch(error => {
        console.error('批量删除白名单出错:', error);
        Toast.error('批量删除失败，请重试');
    });
}

// =================== 增强功能函数 ===================

/**
 * 获取IP类型文本
 */
function getIpTypeText(ipType) {
    const typeMap = {
        'SINGLE': '单IP',
        'RANGE': 'IP段',
        'CIDR': 'CIDR'
    };
    return typeMap[ipType] || '未知';
}

/**
 * 获取状态文本
 */
function getStatusText(status) {
    const statusMap = {
        'ENABLED': '启用',
        'DISABLED': '禁用'
    };
    return statusMap[status] || '未知';
}

/**
 * 检查是否已过期
 */
function isExpired(expiresAt) {
    if (!expiresAt) return false;
    return new Date(expiresAt) < new Date();
}

/**
 * 切换黑名单状态
 */
function toggleBlacklistStatus(id, currentStatus) {
    const newStatus = currentStatus === 'ENABLED' ? 'DISABLED' : 'ENABLED';
    const action = newStatus === 'ENABLED' ? '启用' : '禁用';
    
    if (!confirm(`确定要${action}这个黑名单项吗？`)) {
        return;
    }
    
    fetch(`/api/admin/ip/blacklist/${id}/status`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success(`黑名单项已${action}`);
            loadBlacklist();
        } else {
            Toast.error(result.message || `${action}失败`);
        }
    })
    .catch(error => {
        console.error('切换状态出错:', error);
        Toast.error(`${action}失败，请重试`);
    });
}

/**
 * 切换白名单状态
 */
function toggleWhitelistStatus(id, currentStatus) {
    const newStatus = currentStatus === 'ENABLED' ? 'DISABLED' : 'ENABLED';
    const action = newStatus === 'ENABLED' ? '启用' : '禁用';
    
    if (!confirm(`确定要${action}这个白名单项吗？`)) {
        return;
    }
    
    fetch(`/api/admin/ip/whitelist/${id}/status`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success(`白名单项已${action}`);
            loadWhitelist();
        } else {
            Toast.error(result.message || `${action}失败`);
        }
    })
    .catch(error => {
        console.error('切换状态出错:', error);
        Toast.error(`${action}失败，请重试`);
    });
}

/**
 * 编辑黑名单项
 */
function editBlacklistItem(id) {
    // 获取黑名单项详情
    fetch(`/api/admin/ip/blacklist/${id}`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showEditBlacklistModal(result.data);
            } else {
                Toast.error(result.message || '获取详情失败');
            }
        })
        .catch(error => {
            console.error('获取黑名单详情出错:', error);
            Toast.error('获取详情失败，请重试');
        });
}

/**
 * 编辑白名单项
 */
function editWhitelistItem(id) {
    // 获取白名单项详情
    fetch(`/api/admin/ip/whitelist/${id}`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showEditWhitelistModal(result.data);
            } else {
                Toast.error(result.message || '获取详情失败');
            }
        })
        .catch(error => {
            console.error('获取白名单详情出错:', error);
            Toast.error('获取详情失败，请重试');
        });
}

/**
 * 显示编辑黑名单模态框
 */
function showEditBlacklistModal(item) {
    const modal = document.getElementById('editBlacklistModal');
    if (!modal) {
        console.error('编辑黑名单模态框不存在');
        return;
    }
    
    // 填充表单数据
    document.getElementById('editBlacklistId').value = item.id;
    document.getElementById('editBlacklistIp').value = item.ipAddress;
    document.getElementById('editBlacklistReason').value = item.reason || '';
    document.getElementById('editBlacklistStatus').value = item.status || 'ENABLED';
    
    if (item.expiresAt) {
        const expireDate = new Date(item.expiresAt);
        document.getElementById('editBlacklistExpires').value = expireDate.toISOString().slice(0, 16);
    } else {
        document.getElementById('editBlacklistExpires').value = '';
    }
    
    modal.style.display = 'flex';
}

/**
 * 显示编辑白名单模态框
 */
function showEditWhitelistModal(item) {
    const modal = document.getElementById('editWhitelistModal');
    if (!modal) {
        console.error('编辑白名单模态框不存在');
        return;
    }
    
    // 填充表单数据
    document.getElementById('editWhitelistId').value = item.id;
    document.getElementById('editWhitelistIp').value = item.ipAddress;
    document.getElementById('editWhitelistDesc').value = item.description || '';
    document.getElementById('editWhitelistStatus').value = item.status || 'ENABLED';
    
    if (item.expiresAt) {
        const expireDate = new Date(item.expiresAt);
        document.getElementById('editWhitelistExpires').value = expireDate.toISOString().slice(0, 16);
    } else {
        document.getElementById('editWhitelistExpires').value = '';
    }
    
    modal.style.display = 'flex';
}

/**
 * 隐藏编辑黑名单模态框
 */
function hideEditBlacklistModal() {
    const modal = document.getElementById('editBlacklistModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * 隐藏编辑白名单模态框
 */
function hideEditWhitelistModal() {
    const modal = document.getElementById('editWhitelistModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * 更新黑名单项
 */
function updateBlacklistItem() {
    const form = document.getElementById('editBlacklistForm');
    const formData = new FormData(form);
    
    const data = {
        id: formData.get('id'),
        ipAddress: formData.get('ipAddress').trim(),
        reason: formData.get('reason').trim(),
        status: formData.get('status'),
        expiresAt: formData.get('expiresAt') || null
    };
    
    if (!data.ipAddress) {
        Toast.error('请输入IP地址');
        return;
    }
    
    fetch(`/api/admin/ip/blacklist/${data.id}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success('黑名单项更新成功');
            hideEditBlacklistModal();
            loadBlacklist();
        } else {
            Toast.error(result.message || '更新失败');
        }
    })
    .catch(error => {
        console.error('更新黑名单出错:', error);
        Toast.error('更新失败，请重试');
    });
}

/**
 * 更新白名单项
 */
function updateWhitelistItem() {
    const form = document.getElementById('editWhitelistForm');
    const formData = new FormData(form);
    
    const data = {
        id: formData.get('id'),
        ipAddress: formData.get('ipAddress').trim(),
        description: formData.get('description').trim(),
        status: formData.get('status'),
        expiresAt: formData.get('expiresAt') || null
    };
    
    if (!data.ipAddress) {
        Toast.error('请输入IP地址');
        return;
    }
    
    fetch(`/api/admin/ip/whitelist/${data.id}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success('白名单项更新成功');
            hideEditWhitelistModal();
            loadWhitelist();
        } else {
            Toast.error(result.message || '更新失败');
        }
    })
    .catch(error => {
        console.error('更新白名单出错:', error);
        Toast.error('更新失败，请重试');
    });
}

/**
 * 加载IP访问统计
 */
function loadIpStats() {
    fetch('/api/admin/ip/stats')
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                updateStatsDisplay(result.data);
            } else {
                console.error('加载统计失败:', result.message);
            }
        })
        .catch(error => {
            console.error('加载IP统计出错:', error);
        });
}

/**
 * 更新统计显示
 */
function updateStatsDisplay(stats) {
    // 更新卡片数据
    const blockedTodayEl = document.getElementById('blockedToday');
    const totalRequestsEl = document.getElementById('totalRequests');
    
    if (blockedTodayEl && stats.blockedToday !== undefined) {
        blockedTodayEl.textContent = stats.blockedToday;
    }
    
    if (totalRequestsEl && stats.totalRequests !== undefined) {
        totalRequestsEl.textContent = stats.totalRequests;
    }
}

/**
 * 手动触发缓存预热
 */
function warmupCache() {
    fetch('/api/admin/ip/cache/warmup', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success('缓存预热已启动');
        } else {
            Toast.error(result.message || '缓存预热失败');
        }
    })
    .catch(error => {
        console.error('缓存预热出错:', error);
        Toast.error('缓存预热失败，请重试');
    });
}

/**
 * 清理缓存
 */
function clearCache() {
    if (!confirm('确定要清理所有IP管理缓存吗？')) {
        return;
    }
    
    fetch('/api/admin/ip/cache/clear', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success('缓存清理成功');
        } else {
            Toast.error(result.message || '缓存清理失败');
        }
    })
    .catch(error => {
        console.error('缓存清理出错:', error);
        Toast.error('缓存清理失败，请重试');
    });
}

// 页面加载完成后加载统计数据
document.addEventListener('DOMContentLoaded', function() {
    loadIpStats();
    
    // 每30秒刷新统计数据
    setInterval(loadIpStats, 30000);
});

// =================== IP地理位置查询功能 ===================

/**
 * 查询IP地理位置信息
 */
function queryIpLocations(items) {
    if (!items || items.length === 0) return;
    
    // 筛选出需要查询地理位置的IP（没有location或location为"查询中..."）
    const ipsToQuery = items
        .filter(item => !item.location || item.location === '查询中...')
        .map(item => item.ipAddress)
        .filter(ip => isValidSingleIp(ip)); // 只查询单个IP，不查询IP段
    
    if (ipsToQuery.length === 0) return;
    
    // 批量查询地理位置
    ipsToQuery.forEach(ip => {
        queryIpLocation(ip);
    });
}

/**
 * 查询单个IP的地理位置
 */
function queryIpLocation(ipAddress) {
    if (!ipAddress || !isValidSingleIp(ipAddress)) return;
    
    fetch(`/api/admin/ip/location/${encodeURIComponent(ipAddress)}`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                updateLocationDisplay(ipAddress, result.data);
            } else {
                updateLocationDisplay(ipAddress, '未知');
            }
        })
        .catch(error => {
            console.debug('查询IP地理位置失败:', ipAddress, error);
            updateLocationDisplay(ipAddress, '查询失败');
        });
}

/**
 * 更新页面上的地理位置显示
 */
function updateLocationDisplay(ipAddress, location) {
    const locationElements = document.querySelectorAll(`[data-ip="${ipAddress}"] .location-text`);
    locationElements.forEach(element => {
        element.textContent = `📍 ${location}`;
        
        // 根据位置类型添加样式
        element.classList.remove('location-unknown', 'location-local', 'location-private', 'location-foreign');
        
        if (location === '本地' || location.includes('localhost') || location.includes('127.0.0.1')) {
            element.classList.add('location-local');
        } else if (location === '内网' || location.includes('内网')) {
            element.classList.add('location-private');
        } else if (location === '未知' || location === '查询失败' || location === '查询中...') {
            element.classList.add('location-unknown');
        } else {
            element.classList.add('location-foreign');
        }
    });
}

/**
 * 验证是否为有效的单个IPv4地址（不包括CIDR）
 */
function isValidSingleIp(ip) {
    if (!ip || typeof ip !== 'string') return false;
    
    // 排除CIDR格式
    if (ip.includes('/')) return false;
    
    // 简单的IPv4验证
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipv4Regex.test(ip.trim());
}

/**
 * 批量查询地理位置（管理员功能）
 */
function batchQueryIpLocations() {
    // 收集当前页面上所有的IP地址
    const ipElements = document.querySelectorAll('.ip-address');
    const ips = Array.from(ipElements)
        .map(el => el.textContent.trim())
        .filter(ip => isValidSingleIp(ip))
        .filter((ip, index, arr) => arr.indexOf(ip) === index); // 去重
    
    if (ips.length === 0) {
        Toast.error('没有需要查询的IP地址');
        return;
    }
    
    fetch('/api/admin/ip/location/batch', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(ips)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success(`已启动批量查询 ${ips.length} 个IP地址的地理位置`);
            
            // 延迟几秒后刷新页面数据
            setTimeout(() => {
                if (currentTab === 'blacklist') {
                    loadBlacklist();
                } else {
                    loadWhitelist();
                }
            }, 3000);
        } else {
            Toast.error(result.message || '批量查询启动失败');
        }
    })
    .catch(error => {
        console.error('批量查询地理位置失败:', error);
        Toast.error('批量查询失败，请重试');
    });
}

/**
 * 清理地理位置缓存
 */
function clearLocationCache() {
    if (!confirm('确定要清理地理位置缓存吗？这将重新查询所有IP的地理位置。')) {
        return;
    }
    
    fetch('/api/admin/ip/location/cache/clear', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success('地理位置缓存清理成功');
            
            // 重新加载页面数据
            setTimeout(() => {
                if (currentTab === 'blacklist') {
                    loadBlacklist();
                } else {
                    loadWhitelist();
                }
            }, 1000);
        } else {
            Toast.error(result.message || '缓存清理失败');
        }
    })
    .catch(error => {
        console.error('清理地理位置缓存失败:', error);
        Toast.error('缓存清理失败，请重试');
    });
}
