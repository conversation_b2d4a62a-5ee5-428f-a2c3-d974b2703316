/**
 * 分享管理页面JavaScript
 */

let currentEditShareId = null;
let isSelectMode = false;
let selectedShares = new Set();

document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initSharesPage();

    // 绑定事件
    bindEvents();

    // 检查日期选择器支持
    checkDateTimeSupport();
});

/**
 * 初始化分享页面
 */
function initSharesPage() {
    // 加载分享统计
    loadShareStats();
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 模态框点击外部关闭
    document.addEventListener('click', function(e) {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            if (e.target === modal) {
                modal.classList.remove('show');
            }
        });
    });

    // 全选/取消全选快捷键
    document.addEventListener('keydown', function(e) {
        if (isSelectMode && e.ctrlKey && e.key === 'a') {
            e.preventDefault();
            selectAll();
        }
        if (isSelectMode && e.key === 'Escape') {
            clearSelection();
        }
    });
}

/**
 * 加载分享统计
 */
async function loadShareStats() {
    try {
        const response = await Http.get('/api/shares/stats');
        if (response.code === 200) {
            // 更新统计数据显示
            // 这里可以动态更新统计卡片
        }
    } catch (error) {
        console.error('Load share stats error:', error);
    }
}

/**
 * 复制分享链接
 */
async function copyShareLink(shareCode) {
    const shareUrl = `${window.location.origin}/share/${shareCode}`;
    
    try {
        await Utils.copyToClipboard(shareUrl);
        Toast.success('分享链接已复制到剪贴板');
    } catch (error) {
        console.error('Copy share link error:', error);
        Toast.error('复制失败');
    }
}

/**
 * 查看分享
 */
function viewShare(shareCode) {
    const shareUrl = `${window.location.origin}/share/${shareCode}`;
    window.open(shareUrl, '_blank');
}

/**
 * 编辑分享
 */
function editShare(shareId) {
    currentEditShareId = shareId;

    // 从页面中获取当前分享信息
    const shareItem = document.querySelector(`[data-share-id="${shareId}"]`);
    if (shareItem) {
        // 获取分享信息
        const shareCode = shareItem.querySelector('[data-share-code]')?.dataset.shareCode;

        // 从页面元素中提取信息（这是临时方案，理想情况下应该从API获取）
        const metaItems = shareItem.querySelectorAll('.meta-item');
        let hasPassword = false;
        let expireTime = '';
        let downloadLimit = '';

        metaItems.forEach(item => {
            const text = item.textContent.trim();
            if (text.includes('有密码')) {
                hasPassword = true;
            } else if (text.includes('过期时间:')) {
                expireTime = text.replace('过期时间: ', '').trim();
                // 转换为datetime-local格式
                if (expireTime) {
                    expireTime = expireTime.replace(' ', 'T');
                }
            } else if (text.includes('下载限制:')) {
                const limitMatch = text.match(/下载限制: \d+\/(\d+)/);
                if (limitMatch) {
                    downloadLimit = limitMatch[1];
                }
            }
        });

        // 填充表单
        fillEditForm(hasPassword, expireTime, downloadLimit);
    }

    showEditShareModal();
}

/**
 * 填充编辑表单
 */
function fillEditForm(hasPassword, expireTime, downloadLimit) {
    // 密码字段保持空白（出于安全考虑）
    const passwordField = document.getElementById('sharePassword');
    if (passwordField) {
        passwordField.value = '';
        passwordField.placeholder = hasPassword ? '留空保持原密码不变' : '留空表示无密码';
    }

    // 过期时间
    const expireTimeField = document.getElementById('expireTime');
    if (expireTimeField) {
        const formattedTime = formatDateTimeLocal(expireTime);
        expireTimeField.value = formattedTime;
        console.log('设置过期时间:', expireTime, '格式化后:', formattedTime);
    }

    // 下载限制
    const downloadLimitField = document.getElementById('downloadLimit');
    if (downloadLimitField) {
        downloadLimitField.value = downloadLimit || '';
    }
}

/**
 * 显示编辑分享模态框
 */
function showEditShareModal() {
    const modal = document.getElementById('editShareModal');
    modal.classList.add('show');

    // 确保日期选择器正确显示
    setTimeout(() => {
        const expireTimeField = document.getElementById('expireTime');
        if (expireTimeField) {
            // 强制重新渲染日期选择器
            expireTimeField.style.display = 'none';
            expireTimeField.offsetHeight; // 触发重排
            expireTimeField.style.display = 'block';

            console.log('日期选择器已重新渲染');
        }

        // 聚焦到第一个输入框
        const passwordField = document.getElementById('sharePassword');
        if (passwordField) {
            passwordField.focus();
        }
    }, 100);
}

/**
 * 隐藏编辑分享模态框
 */
function hideEditShareModal() {
    const modal = document.getElementById('editShareModal');
    modal.classList.remove('show');
    
    // 清空表单
    document.getElementById('editShareForm').reset();
    currentEditShareId = null;
}

/**
 * 更新分享设置
 */
async function updateShare() {
    if (!currentEditShareId) {
        Toast.error('无效的分享ID');
        return;
    }

    const form = document.getElementById('editShareForm');
    const formData = Form.serialize(form);

    try {
        const response = await Http.putForm(`/api/shares/${currentEditShareId}`, formData);

        if (response.code === 200) {
            Toast.success('分享设置更新成功');
            hideEditShareModal();
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            Toast.error(response.message || '更新失败');
        }
    } catch (error) {
        console.error('Update share error:', error);
        Toast.error('更新失败，请稍后重试');
    }
}

/**
 * 取消分享
 */
async function cancelShare(shareId) {
    if (!confirm('确定要取消这个分享吗？')) {
        return;
    }
    
    try {
        const response = await Http.delete(`/api/shares/${shareId}`);
        
        if (response.code === 200) {
            Toast.success('分享已取消');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            Toast.error(response.message || '取消失败');
        }
    } catch (error) {
        console.error('Cancel share error:', error);
        Toast.error('取消失败');
    }
}

/**
 * 筛选分享
 */
function filterShares() {
    const statusFilter = document.getElementById('statusFilter').value;
    const shareItems = document.querySelectorAll('.share-item');
    
    shareItems.forEach(item => {
        let show = true;
        
        if (statusFilter) {
            const isActive = item.classList.contains('active');
            const isInactive = item.classList.contains('inactive');
            
            switch (statusFilter) {
                case 'active':
                    show = isActive;
                    break;
                case 'expired':
                case 'disabled':
                    show = isInactive;
                    break;
            }
        }
        
        item.style.display = show ? 'flex' : 'none';
    });
}

/**
 * 用户菜单切换
 */
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

/**
 * 切换选择模式
 */
function toggleSelectMode() {
    isSelectMode = !isSelectMode;
    const toggleBtn = document.getElementById('toggleSelectBtn');
    const batchControls = document.getElementById('batchControls');
    const selectionControls = document.getElementById('selectionControls');
    const checkboxes = document.querySelectorAll('.share-checkbox');
    const shareItems = document.querySelectorAll('.share-item');

    if (isSelectMode) {
        // 进入选择模式
        toggleBtn.classList.add('active');
        toggleBtn.querySelector('.text').textContent = '退出选择';
        toggleBtn.querySelector('.icon').textContent = '✖️';

        // 显示复选框和全选控件
        checkboxes.forEach(checkbox => {
            checkbox.style.display = 'flex';
        });
        selectionControls.style.display = 'flex';

        // 添加选择模式样式
        shareItems.forEach(item => {
            item.classList.add('select-mode');
        });

    } else {
        // 退出选择模式
        toggleBtn.classList.remove('active');
        toggleBtn.querySelector('.text').textContent = '批量选择';
        toggleBtn.querySelector('.icon').textContent = '☑️';

        // 隐藏复选框、批量控件和全选控件
        checkboxes.forEach(checkbox => {
            checkbox.style.display = 'none';
        });
        batchControls.style.display = 'none';
        selectionControls.style.display = 'none';

        // 移除选择模式样式
        shareItems.forEach(item => {
            item.classList.remove('select-mode', 'selected');
        });

        // 清空选择
        clearSelection();
    }
}

/**
 * 更新选择状态
 */
function updateSelection() {
    const checkboxes = document.querySelectorAll('.share-checkbox input[type="checkbox"]');
    const batchControls = document.getElementById('batchControls');
    const selectedCount = document.getElementById('selectedCount');

    // 更新选中的分享集合
    selectedShares.clear();
    checkboxes.forEach(checkbox => {
        const shareItem = checkbox.closest('.share-item');
        if (checkbox.checked) {
            selectedShares.add(parseInt(checkbox.value));
            shareItem.classList.add('selected');
        } else {
            shareItem.classList.remove('selected');
        }
    });

    // 更新UI
    if (selectedShares.size > 0) {
        batchControls.style.display = 'flex';
        selectedCount.textContent = selectedShares.size;
    } else {
        batchControls.style.display = 'none';
    }

    // 更新全选按钮状态
    updateSelectAllState();
}

/**
 * 更新全选按钮状态
 */
function updateSelectAllState() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.share-checkbox input[type="checkbox"]');

    if (!selectAll || checkboxes.length === 0) return;

    const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;

    if (checkedCount === 0) {
        selectAll.checked = false;
        selectAll.indeterminate = false;
    } else if (checkedCount === checkboxes.length) {
        selectAll.checked = true;
        selectAll.indeterminate = false;
    } else {
        selectAll.checked = false;
        selectAll.indeterminate = true;
    }
}

/**
 * 全选/取消全选
 */
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.share-checkbox input[type="checkbox"]');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateSelection();
}

/**
 * 全选
 */
function selectAll() {
    const checkboxes = document.querySelectorAll('.share-checkbox input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelection();
}

/**
 * 清空选择
 */
function clearSelection() {
    selectedShares.clear();
    const checkboxes = document.querySelectorAll('.share-checkbox input[type="checkbox"]');
    const shareItems = document.querySelectorAll('.share-item');

    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });

    shareItems.forEach(item => {
        item.classList.remove('selected');
    });

    document.getElementById('batchControls').style.display = 'none';
    updateSelectAllState();
}

/**
 * 批量操作
 */
async function batchOperation(operation) {
    if (selectedShares.size === 0) {
        Toast.error('请先选择要操作的分享');
        return;
    }

    const operationNames = {
        'cancel': '取消',
        'enable': '启用',
        'delete': '删除'
    };

    const operationName = operationNames[operation] || operation;

    // 确认操作
    if (!confirm(`确定要${operationName}选中的 ${selectedShares.size} 个分享吗？`)) {
        return;
    }

    try {
        const shareIds = Array.from(selectedShares);
        const formData = new FormData();
        formData.append('operation', operation);
        shareIds.forEach(id => formData.append('shareIds', id));

        const response = await Http.postForm('/api/shares/batch', formData);

        if (response.code === 200) {
            Toast.success(response.message || `${operationName}成功`);

            // 刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            Toast.error(response.message || `${operationName}失败`);
        }

    } catch (error) {
        console.error('Batch operation error:', error);
        Toast.error(`${operationName}失败，请稍后重试`);
    }
}

/**
 * 检查浏览器对datetime-local的支持
 */
function checkDateTimeSupport() {
    const input = document.createElement('input');
    input.type = 'datetime-local';

    // 检查是否支持datetime-local
    if (input.type !== 'datetime-local') {
        console.warn('浏览器不支持datetime-local，使用备用方案');
        // 可以在这里添加备用的日期选择器实现
        return false;
    }

    console.log('浏览器支持datetime-local');
    return true;
}

/**
 * 格式化日期为datetime-local格式
 */
function formatDateTimeLocal(dateString) {
    if (!dateString) return '';

    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '';

        // 转换为本地时间的ISO字符串，然后截取前16位
        const offset = date.getTimezoneOffset();
        const localDate = new Date(date.getTime() - (offset * 60 * 1000));
        return localDate.toISOString().slice(0, 16);
    } catch (error) {
        console.error('日期格式化错误:', error);
        return '';
    }
}

/**
 * 用户登出
 */
async function logout() {
    try {
        const response = await Http.post('/api/auth/logout');
        
        if (response.code === 200) {
            Toast.success('登出成功');
            setTimeout(() => {
                window.location.href = '/login';
            }, 1000);
        } else {
            Toast.error(response.message || '登出失败');
        }
    } catch (error) {
        console.error('Logout error:', error);
        Toast.error('网络错误，请稍后重试');
    }
}

// 点击其他地方关闭用户菜单
document.addEventListener('click', function(e) {
    const userMenu = document.querySelector('.user-menu');
    const dropdown = document.getElementById('userDropdown');
    
    if (userMenu && dropdown && !userMenu.contains(e.target)) {
        dropdown.classList.remove('show');
    }
});
