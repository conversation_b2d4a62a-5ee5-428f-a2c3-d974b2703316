/**
 * 管理员仪表板JavaScript
 */

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initAdminDashboard();
    loadSystemStats();
    formatStorageSize();
});

/**
 * 初始化管理员仪表板
 */
function initAdminDashboard() {
    console.log('管理员仪表板初始化');
    
    // 设置定时刷新统计数据
    setInterval(loadSystemStats, 30000); // 30秒刷新一次
}

/**
 * 加载系统统计数据
 */
function loadSystemStats() {
    fetch('/api/admin/stats')
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                updateStatsDisplay(result.data);
            } else {
                console.error('获取统计数据失败:', result.message);
            }
        })
        .catch(error => {
            console.error('获取统计数据出错:', error);
        });
}

/**
 * 更新统计数据显示
 */
function updateStatsDisplay(stats) {
    // 更新统计卡片数据
    const statCards = document.querySelectorAll('.stat-card');
    
    if (statCards.length >= 4) {
        // 总用户数
        statCards[0].querySelector('h3').textContent = stats.totalUsers || 0;
        statCards[0].querySelector('.stat-change span').textContent = stats.todayRegistrations || 0;
        
        // 总文件数
        statCards[1].querySelector('h3').textContent = stats.totalFiles || 0;
        statCards[1].querySelector('.stat-change span').textContent = stats.todayUploads || 0;
        
        // 总存储空间
        const totalSizeText = Utils.formatFileSize(stats.totalSize || 0);
        statCards[2].querySelector('h3').textContent = totalSizeText;
        statCards[2].querySelector('.stat-change span').textContent = stats.deletedFiles || 0;
        
        // 活跃用户
        statCards[3].querySelector('h3').textContent = stats.activeUsers || 0;
        statCards[3].querySelector('.stat-change span').textContent = stats.disabledUsers || 0;
    }
}

/**
 * 格式化存储大小显示
 */
function formatStorageSize() {
    if (window.adminStats && window.adminStats.totalSize) {
        const totalSizeElement = document.getElementById('totalSizeText');
        if (totalSizeElement) {
            totalSizeElement.textContent = Utils.formatFileSize(window.adminStats.totalSize);
        }
    }
}

/**
 * 查看文件详情
 */
function viewFile(fileId) {
    // 打开文件详情模态框或跳转到文件详情页
    window.open(`/admin/files/${fileId}`, '_blank');
}

/**
 * 删除文件
 */
function deleteFile(fileId) {
    if (!confirm('确定要删除这个文件吗？此操作不可恢复！')) {
        return;
    }
    
    fetch(`/api/admin/files/${fileId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success('文件删除成功');
            // 刷新页面或移除对应行
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            Toast.error(result.message || '删除失败');
        }
    })
    .catch(error => {
        console.error('删除文件出错:', error);
        Toast.error('删除失败，请重试');
    });
}

/**
 * 快速操作 - 清理回收站
 */
function cleanupRecycleBin() {
    if (!confirm('确定要清理回收站吗？这将永久删除所有回收站中的文件！')) {
        return;
    }
    
    const days = prompt('请输入要清理多少天前的删除文件（默认30天）:', '30');
    if (days === null) return;
    
    const cleanupDays = parseInt(days) || 30;
    
    fetch('/api/admin/cleanup', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `days=${cleanupDays}`
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success(result.message || '清理完成');
            loadSystemStats(); // 刷新统计数据
        } else {
            Toast.error(result.message || '清理失败');
        }
    })
    .catch(error => {
        console.error('清理回收站出错:', error);
        Toast.error('清理失败，请重试');
    });
}

/**
 * 快速操作 - 系统备份
 */
function systemBackup() {
    if (!confirm('确定要开始系统备份吗？这可能需要一些时间。')) {
        return;
    }
    
    Toast.info('备份开始，请稍候...');
    
    fetch('/api/admin/backup', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            Toast.success('备份完成');
        } else {
            Toast.error(result.message || '备份失败');
        }
    })
    .catch(error => {
        console.error('系统备份出错:', error);
        Toast.error('备份失败，请重试');
    });
}

/**
 * 导出统计报告
 */
function exportReport() {
    const reportType = prompt('请选择报告类型:\n1. 用户统计\n2. 文件统计\n3. 存储统计\n4. 完整报告', '4');
    if (reportType === null) return;
    
    const type = parseInt(reportType) || 4;
    const typeMap = {
        1: 'users',
        2: 'files', 
        3: 'storage',
        4: 'complete'
    };
    
    const reportTypeStr = typeMap[type] || 'complete';
    
    // 创建下载链接
    const link = document.createElement('a');
    link.href = `/api/admin/export-report?type=${reportTypeStr}`;
    link.download = `system-report-${reportTypeStr}-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    Toast.success('报告导出中...');
}

/**
 * 实时监控切换
 */
function toggleRealTimeMonitor() {
    const isEnabled = localStorage.getItem('realTimeMonitor') === 'true';
    const newState = !isEnabled;
    
    localStorage.setItem('realTimeMonitor', newState.toString());
    
    if (newState) {
        Toast.success('实时监控已开启');
        startRealTimeMonitor();
    } else {
        Toast.info('实时监控已关闭');
        stopRealTimeMonitor();
    }
}

/**
 * 开始实时监控
 */
function startRealTimeMonitor() {
    // 每5秒刷新一次数据
    window.realTimeInterval = setInterval(() => {
        loadSystemStats();
    }, 5000);
}

/**
 * 停止实时监控
 */
function stopRealTimeMonitor() {
    if (window.realTimeInterval) {
        clearInterval(window.realTimeInterval);
        window.realTimeInterval = null;
    }
}

/**
 * 页面卸载时清理
 */
window.addEventListener('beforeunload', function() {
    stopRealTimeMonitor();
});

// 检查是否启用实时监控
if (localStorage.getItem('realTimeMonitor') === 'true') {
    startRealTimeMonitor();
}
