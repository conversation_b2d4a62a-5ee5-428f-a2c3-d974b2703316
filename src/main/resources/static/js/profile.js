/**
 * 个人中心页面JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initProfilePage();
    
    // 绑定事件
    bindEvents();
});

/**
 * 初始化个人中心页面
 */
function initProfilePage() {
    // 加载用户统计数据
    loadUserStats();
    
    // 更新存储使用情况
    updateStorageDisplay();
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 密码确认验证
    const newPassword = document.getElementById('newPassword');
    const confirmPassword = document.getElementById('confirmPassword');
    
    if (newPassword && confirmPassword) {
        confirmPassword.addEventListener('input', validatePasswordMatch);
        newPassword.addEventListener('input', validatePasswordMatch);
    }
}

/**
 * 加载用户统计数据
 */
async function loadUserStats() {
    try {
        const response = await Http.get('/api/profile/stats');
        if (response.code === 200) {
            const stats = response.data;
            
            // 更新统计显示
            updateStatValue('fileCount', stats.fileCount || 0);
            updateStatValue('folderCount', stats.folderCount || 0);
            updateStatValue('shareCount', stats.shareCount || 0);
            updateStatValue('downloadCount', stats.downloadCount || 0);
        }
    } catch (error) {
        console.error('Load user stats error:', error);
    }
}

/**
 * 更新统计值
 */
function updateStatValue(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

/**
 * 更新存储显示
 */
function updateStorageDisplay() {
    if (!window.currentUser) return;
    
    const user = window.currentUser;
    const storageBar = document.getElementById('storageBar');
    
    if (storageBar && user.storageLimit !== -1) {
        const percentage = Math.min((user.storageUsed / user.storageLimit) * 100, 100);
        storageBar.style.width = percentage + '%';
        
        // 根据使用率改变颜色
        if (percentage > 90) {
            storageBar.style.background = 'linear-gradient(90deg, #f44336, #d32f2f)';
        } else if (percentage > 70) {
            storageBar.style.background = 'linear-gradient(90deg, #ff9800, #f57c00)';
        } else {
            storageBar.style.background = 'linear-gradient(90deg, var(--primary-color), var(--accent-color))';
        }
    }
}

/**
 * 上传头像
 */
function uploadAvatar() {
    document.getElementById('avatarInput').click();
}

/**
 * 处理头像上传
 */
async function handleAvatarUpload() {
    const fileInput = document.getElementById('avatarInput');
    const file = fileInput.files[0];
    
    if (!file) return;
    
    // 验证文件类型
    if (!file.type.startsWith('image/')) {
        Toast.error('请选择图片文件');
        return;
    }
    
    // 验证文件大小（2MB）
    if (file.size > 2 * 1024 * 1024) {
        Toast.error('头像文件大小不能超过2MB');
        return;
    }
    
    try {
        const formData = new FormData();
        formData.append('avatar', file);
        
        const response = await Http.postForm('/api/profile/avatar', formData);
        
        if (response.code === 200) {
            Toast.success('头像上传成功');
            
            // 更新头像显示
            const avatarImg = document.getElementById('avatarImg');
            const userAvatars = document.querySelectorAll('.user-avatar img');
            
            if (avatarImg) {
                avatarImg.src = response.data + '?t=' + Date.now();
            }
            
            userAvatars.forEach(img => {
                img.src = response.data + '?t=' + Date.now();
            });
            
        } else {
            Toast.error(response.message || '头像上传失败');
        }
    } catch (error) {
        console.error('Upload avatar error:', error);
        Toast.error('头像上传失败');
    }
}

/**
 * 更新个人信息
 */
async function updateProfile() {
    const form = document.getElementById('profileForm');
    
    if (!Form.validate(form)) {
        return;
    }
    
    try {
        const formData = Form.serialize(form);
        const response = await Http.putForm('/api/profile', formData);

        if (response.code === 200) {
            Toast.success('信息更新成功');
        } else {
            Toast.error(response.message || '更新失败');
        }
    } catch (error) {
        console.error('Update profile error:', error);
        Toast.error('更新失败');
    }
}

/**
 * 修改密码
 */
async function changePassword() {
    const form = document.getElementById('passwordForm');
    
    if (!Form.validate(form)) {
        return;
    }
    
    if (!validatePasswordMatch()) {
        return;
    }
    
    try {
        const formData = Form.serialize(form);
        const response = await Http.putForm('/api/profile/password', formData);

        if (response.code === 200) {
            Toast.success('密码修改成功');
            form.reset();
        } else {
            Toast.error(response.message || '密码修改失败');
        }
    } catch (error) {
        console.error('Change password error:', error);
        Toast.error('密码修改失败');
    }
}

/**
 * 验证密码匹配
 */
function validatePasswordMatch() {
    const newPassword = document.getElementById('newPassword');
    const confirmPassword = document.getElementById('confirmPassword');
    
    if (!newPassword || !confirmPassword) return true;
    
    const group = confirmPassword.closest('.form-group');
    if (!group) return true;
    
    // 清除之前的错误状态
    group.classList.remove('error', 'success');
    const errorMsg = group.querySelector('.form-error-message');
    if (errorMsg) errorMsg.remove();
    
    if (newPassword.value && confirmPassword.value) {
        if (newPassword.value !== confirmPassword.value) {
            Form.showFieldError(group, '两次输入的密码不一致');
            return false;
        } else {
            group.classList.add('success');
        }
    }
    
    return true;
}

/**
 * 用户菜单切换
 */
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

/**
 * 用户登出
 */
async function logout() {
    try {
        const response = await Http.post('/api/auth/logout');
        
        if (response.code === 200) {
            Toast.success('登出成功');
            setTimeout(() => {
                window.location.href = '/login';
            }, 1000);
        } else {
            Toast.error(response.message || '登出失败');
        }
    } catch (error) {
        console.error('Logout error:', error);
        Toast.error('网络错误，请稍后重试');
    }
}

// 点击其他地方关闭用户菜单
document.addEventListener('click', function(e) {
    const userMenu = document.querySelector('.user-menu');
    const dropdown = document.getElementById('userDropdown');
    
    if (userMenu && dropdown && !userMenu.contains(e.target)) {
        dropdown.classList.remove('show');
    }
});
