/**
 * 通用JavaScript工具函数
 */

// Toast提示功能
const Toast = {
    show: function(message, type = 'info', duration = 3000) {
        const toast = document.getElementById('toast');
        if (!toast) return;
        
        toast.textContent = message;
        toast.className = `toast ${type}`;
        
        // 显示toast
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
        }, duration);
    },
    
    success: function(message, duration = 3000) {
        this.show(message, 'success', duration);
    },
    
    error: function(message, duration = 5000) {
        this.show(message, 'error', duration);
    },
    
    warning: function(message, duration = 4000) {
        this.show(message, 'warning', duration);
    },
    
    info: function(message, duration = 3000) {
        this.show(message, 'info', duration);
    }
};

// HTTP请求工具
const Http = {
    request: async function(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            credentials: 'same-origin'
        };
        
        // 只有当body不是FormData时才设置默认的JSON Content-Type
        if (!(options.body instanceof FormData)) {
            defaultOptions.headers = {
                'Content-Type': 'application/json'
            };
        }
        
        const config = { ...defaultOptions, ...options };
        
        // 合并headers，确保options中的headers能覆盖默认headers
        if (defaultOptions.headers && options.headers) {
            config.headers = { ...defaultOptions.headers, ...options.headers };
        }
        
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error('HTTP request failed:', error);
            throw error;
        }
    },
    
    get: function(url, params = {}) {
        const urlParams = new URLSearchParams(params);
        const fullUrl = urlParams.toString() ? `${url}?${urlParams}` : url;
        return this.request(fullUrl);
    },
    
    post: function(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },
    
    postForm: function(url, formData) {
        const options = {
            method: 'POST',
            body: formData
        };
        
        // 只有当formData不是FormData对象时才设置Content-Type
        // FormData对象需要浏览器自动设置multipart/form-data和boundary
        if (!(formData instanceof FormData)) {
            options.headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            };
        }
        
        return this.request(url, options);
    },
    
    put: function(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },
    
    delete: function(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    },

    putForm: function(url, data = {}) {
        const formData = new URLSearchParams();
        for (const [key, value] of Object.entries(data)) {
            if (value !== null && value !== undefined) {
                formData.append(key, value);
            }
        }
        return this.request(url, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: formData
        });
    }
};

// 表单工具
const Form = {
    serialize: function(form) {
        const formData = new FormData(form);
        const data = {};
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        return data;
    },
    
    serializeToFormData: function(form) {
        return new FormData(form);
    },
    
    validate: function(form) {
        const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
        let isValid = true;
        
        inputs.forEach(input => {
            const group = input.closest('.form-group');
            if (!group) return;
            
            // 清除之前的错误状态
            group.classList.remove('error', 'success');
            const errorMsg = group.querySelector('.form-error-message');
            if (errorMsg) errorMsg.remove();
            
            // 验证
            if (!input.value.trim()) {
                this.showFieldError(group, '此字段为必填项');
                isValid = false;
            } else if (input.type === 'email' && !this.isValidEmail(input.value)) {
                this.showFieldError(group, '请输入有效的邮箱地址');
                isValid = false;
            } else {
                group.classList.add('success');
            }
        });
        
        return isValid;
    },
    
    showFieldError: function(group, message) {
        group.classList.add('error');
        const errorElement = document.createElement('span');
        errorElement.className = 'form-error-message';
        errorElement.textContent = message;
        group.appendChild(errorElement);
    },
    
    isValidEmail: function(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
};

// 工具函数
const Utils = {
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    formatDate: function(date) {
        if (!date) return '';
        
        const d = new Date(date);
        const now = new Date();
        const diff = now - d;
        
        // 小于1分钟
        if (diff < 60000) {
            return '刚刚';
        }
        
        // 小于1小时
        if (diff < 3600000) {
            return Math.floor(diff / 60000) + '分钟前';
        }
        
        // 小于1天
        if (diff < 86400000) {
            return Math.floor(diff / 3600000) + '小时前';
        }
        
        // 小于7天
        if (diff < 604800000) {
            return Math.floor(diff / 86400000) + '天前';
        }
        
        // 超过7天显示具体日期
        return d.toLocaleDateString('zh-CN');
    },
    
    formatDateTime: function(dateTime) {
        if (!dateTime) return '';
        
        const d = new Date(dateTime);
        if (isNaN(d.getTime())) return '';
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}`;
    },
    
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    copyToClipboard: async function(text) {
        try {
            await navigator.clipboard.writeText(text);
            Toast.success('已复制到剪贴板');
        } catch (err) {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                Toast.success('已复制到剪贴板');
            } catch (err) {
                Toast.error('复制失败');
            }
            document.body.removeChild(textArea);
        }
    },
    
    getFileIcon: function(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const iconMap = {
            // 图片
            'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️', 'bmp': '🖼️', 'webp': '🖼️', 'svg': '🖼️',
            // 视频
            'mp4': '🎬', 'avi': '🎬', 'mkv': '🎬', 'mov': '🎬', 'wmv': '🎬', 'flv': '🎬', 'webm': '🎬',
            // 音频
            'mp3': '🎵', 'wav': '🎵', 'flac': '🎵', 'aac': '🎵', 'ogg': '🎵', 'wma': '🎵',
            // 文档
            'pdf': '📄', 'doc': '📝', 'docx': '📝', 'xls': '📊', 'xlsx': '📊', 'ppt': '📋', 'pptx': '📋',
            'txt': '📄', 'rtf': '📄',
            // 压缩包
            'zip': '📦', 'rar': '📦', '7z': '📦', 'tar': '📦', 'gz': '📦',
            // 代码
            'js': '💻', 'html': '💻', 'css': '💻', 'java': '💻', 'py': '💻', 'cpp': '💻', 'c': '💻',
            'php': '💻', 'go': '💻', 'rs': '💻', 'ts': '💻'
        };
        
        return iconMap[ext] || '📄';
    }
};

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有表单验证
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!Form.validate(form)) {
                e.preventDefault();
            }
        });
    });
    
    // 初始化所有输入框的实时验证
    const inputs = document.querySelectorAll('input, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            const form = input.closest('form');
            if (form) {
                Form.validate(form);
            }
        });
    });
});

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
    Toast.error('发生了一个错误，请刷新页面重试');
});

window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled promise rejection:', e.reason);
    Toast.error('网络请求失败，请检查网络连接');
});
