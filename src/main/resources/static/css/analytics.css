/* 数据分析页面样式 */

.time-range-selector {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow-sm);
}

.time-range-selector .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.time-range-selector label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.time-range-selector input {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.875rem;
}

.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.overview-cards .card {
    background: var(--card-bg);
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.overview-cards .card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.overview-cards .card-header h3 {
    margin: 0 0 1rem 0;
    color: var(--primary-color);
    font-size: 1.125rem;
    font-weight: 600;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.stat-item:last-child {
    margin-bottom: 0;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.stat-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.charts-container {
    margin-bottom: 2rem;
}

.chart-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.chart-card {
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.chart-card .card-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-light);
}

.chart-card .card-header h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.chart-card .card-body {
    padding: 1.5rem;
}

.chart-card canvas {
    max-height: 300px;
}

.table-container {
    max-height: 300px;
    overflow-y: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background: var(--bg-light);
    font-weight: 600;
    color: var(--text-secondary);
    position: sticky;
    top: 0;
    z-index: 1;
}

.table tbody tr:hover {
    background: var(--hover-bg);
}

.detailed-analysis {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.detailed-analysis .card {
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.detailed-analysis .card-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-light);
}

.detailed-analysis .card-header h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.detailed-analysis .card-body {
    padding: 1.5rem;
}

.export-section {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding: 2rem 0;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay p {
    color: white;
    font-size: 1rem;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .chart-row {
        grid-template-columns: 1fr;
    }
    
    .detailed-analysis {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .time-range-selector {
        flex-direction: column;
        align-items: stretch;
    }
    
    .overview-cards {
        grid-template-columns: 1fr;
    }
    
    .export-section {
        flex-direction: column;
        align-items: center;
    }
}

/* 图表样式增强 */
.chart-card .card-body {
    position: relative;
}

.chart-legend {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
    font-size: 0.875rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

/* 统计卡片增强 */
.stat-value.positive {
    color: var(--success-color);
}

.stat-value.negative {
    color: var(--danger-color);
}

.stat-value.neutral {
    color: var(--warning-color);
}

/* 表格增强 */
.table .number {
    text-align: right;
    font-family: 'Courier New', monospace;
}

.table .status {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status.active {
    background: var(--success-light);
    color: var(--success-color);
}

.status.inactive {
    background: var(--danger-light);
    color: var(--danger-color);
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-primary);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}
