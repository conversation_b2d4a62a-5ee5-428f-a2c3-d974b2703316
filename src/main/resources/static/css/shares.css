/* 分享管理页面样式 */

.shares-content {
    padding: 24px;
    flex: 1;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
}

/* 统计卡片样式美化 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card .stat-icon {
    font-size: 32px;
    margin-bottom: 12px;
    display: block;
}

.stat-card .stat-info h3 {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.2;
}

.stat-card .stat-info p {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 4px 0 0 0;
    font-weight: 500;
}

/* 分享列表容器 - 去除框线设计 */
.shares-list {
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    overflow: visible;
    margin-top: 0;
}

/* 分享头部 - 简化设计 */
.shares-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0 24px 0;
    border-bottom: none;
    background: transparent;
    margin-bottom: 24px;
}

.shares-header-left {
    display: flex;
    align-items: center;
    gap: 24px;
}

.shares-header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.shares-header h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 批量操作控件 */
/* 批量操作控件 - 现代化设计 */
.batch-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px;
    color: white;
    font-size: 14px;
    box-shadow: 0 4px 16px rgba(255, 107, 157, 0.3);
    backdrop-filter: blur(10px);
}

.selected-count {
    font-weight: 600;
}

.batch-btn {
    padding: 8px 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 8px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.batch-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.batch-btn.danger {
    border-color: var(--error-color);
    background-color: var(--error-color);
}

.batch-btn.danger:hover {
    background-color: #d32f2f;
}

.batch-btn.outline {
    background-color: transparent;
    border-color: white;
}

.batch-btn.outline:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.toggle-select-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 18px;
    border: 2px solid var(--primary-color);
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.1), rgba(255, 107, 157, 0.05));
    color: var(--primary-color);
    border-radius: 12px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.toggle-select-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

.toggle-select-btn.active {
    background-color: var(--primary-color);
    color: white;
}

/* 全选控件 */
.selection-controls {
    display: flex;
    align-items: center;
    margin-right: 16px;
}

.select-all-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--text-secondary);
    cursor: pointer;
    user-select: none;
    padding: 6px 12px;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.select-all-label:hover {
    color: var(--text-primary);
    background-color: var(--bg-light);
}

.select-all-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
    accent-color: var(--primary-color);
}

/* 半选中状态样式 */
.select-all-label input[type="checkbox"]:indeterminate {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 日期时间选择器样式 */
input[type="datetime-local"] {
    width: 100% !important;
    padding: 8px 12px !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 4px !important;
    font-size: 14px !important;
    color: #212529 !important;
    background-color: #ffffff !important;
    transition: border-color 0.2s ease !important;
    display: block !important;
    height: auto !important;
    min-height: 38px !important;
    line-height: 1.5 !important;
}

input[type="datetime-local"]:focus {
    outline: none !important;
    border-color: #2196F3 !important;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1) !important;
}

input[type="datetime-local"]::-webkit-calendar-picker-indicator {
    cursor: pointer !important;
    filter: invert(0.5) !important;
    width: 16px !important;
    height: 16px !important;
    display: block !important;
}

input[type="datetime-local"]::-webkit-calendar-picker-indicator:hover {
    filter: invert(0.3) !important;
}

/* 确保在模态框中正确显示 */
.modal input[type="datetime-local"] {
    position: relative !important;
    z-index: 1 !important;
}

/* 数字输入框样式 */
input[type="number"] {
    width: 100% !important;
    padding: 8px 12px !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 4px !important;
    font-size: 14px !important;
    color: #212529 !important;
    background-color: #ffffff !important;
    transition: border-color 0.2s ease !important;
    display: block !important;
    height: auto !important;
    min-height: 38px !important;
    line-height: 1.5 !important;
}

input[type="number"]:focus {
    outline: none !important;
    border-color: #2196F3 !important;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1) !important;
}

/* 确保模态框在分享页面正常显示 */
.modal {
    z-index: 10000 !important;
}

/* 表单组样式 */
.form-group {
    margin-bottom: 16px !important;
}

.form-group label {
    display: block !important;
    margin-bottom: 6px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    color: #212529 !important;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100% !important;
    padding: 8px 12px !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 4px !important;
    font-size: 14px !important;
    color: #212529 !important;
    background-color: #ffffff !important;
    transition: border-color 0.2s ease !important;
    display: block !important;
    height: auto !important;
    min-height: 38px !important;
    line-height: 1.5 !important;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none !important;
    border-color: #2196F3 !important;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1) !important;
}

.form-help {
    font-size: 12px !important;
    color: #6c757d !important;
    margin-top: 4px !important;
    display: block !important;
}

/* 修复模态框中的表单显示问题 */
.modal .form-group {
    position: relative !important;
    z-index: 1 !important;
}

.modal .form-group input {
    position: relative !important;
    z-index: 2 !important;
}

/* 确保日期选择器的下拉菜单能正确显示 */
.modal-content {
    overflow: visible !important;
}

.modal-body {
    overflow: visible !important;
}

/* 修复可能的样式冲突 */
#editShareModal input[type="text"],
#editShareModal input[type="datetime-local"],
#editShareModal input[type="number"] {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    box-sizing: border-box !important;
}

#editShareModal input[type="datetime-local"]::-webkit-calendar-picker-indicator {
    background: transparent !important;
    bottom: 0 !important;
    color: transparent !important;
    cursor: pointer !important;
    height: auto !important;
    left: 0 !important;
    position: absolute !important;
    right: 0 !important;
    top: 0 !important;
    width: auto !important;
    z-index: 3 !important;
}

.shares-filter select {
    padding: 10px 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 500;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.shares-filter select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* 分享项目容器 - 去除内边距 */
.shares-items {
    padding: 0;
    display: grid;
    gap: 20px;
}

/* 分享项目卡片 - 现代化设计 */
.share-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 28px;
    margin-bottom: 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: none;
    border-radius: 16px;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.share-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.share-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.share-item:hover::before {
    opacity: 1;
}

.share-item.active {
    background: linear-gradient(135deg, #ffffff 0%, rgba(76, 175, 80, 0.05) 100%);
    border-left: 4px solid var(--success-color);
}

.share-item.inactive {
    background: linear-gradient(135deg, #ffffff 0%, rgba(244, 67, 54, 0.05) 100%);
    border-left: 4px solid var(--error-color);
    opacity: 0.9;
}

/* 分享复选框 */
.share-checkbox {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.share-checkbox input[type="checkbox"] {
    display: none;
}

.share-checkbox label {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    background-color: var(--bg-primary);
}

.share-checkbox input[type="checkbox"]:checked + label {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.share-checkbox input[type="checkbox"]:checked + label::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.share-checkbox label:hover {
    border-color: var(--primary-color);
}

/* 选择模式下的样式调整 */
.share-item.select-mode .share-checkbox {
    display: flex;
}

.share-item.selected {
    background-color: rgba(33, 150, 243, 0.1);
    border-color: var(--primary-color);
}

.share-info {
    flex: 1;
    min-width: 0; /* 防止flex项目溢出 */
}

.share-file {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    flex-wrap: wrap;
    gap: 8px;
}

.share-file .file-icon {
    font-size: 28px;
    flex-shrink: 0;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.share-file .file-name {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    flex-shrink: 0;
    word-break: break-word;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.share-file .file-size {
    font-size: 14px;
    color: var(--text-muted);
    font-weight: normal;
}

.share-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.share-code {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.share-code label {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

.share-code span {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-color);
    background-color: var(--bg-light);
    padding: 4px 8px;
    border-radius: var(--radius-sm);
}

.copy-btn {
    padding: 6px 12px;
    font-size: 12px;
    border: 1px solid var(--primary-color);
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.1), rgba(255, 107, 157, 0.05));
    color: var(--primary-color);
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.copy-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

.share-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-top: 8px;
}

.meta-item {
    font-size: 12px;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: 4px;
}

.meta-item.error {
    color: var(--error-color);
    font-weight: 600;
}

/* 分享项右侧区域 */
.share-item-right {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    flex-shrink: 0;
    min-width: 200px;
}

/* 分享操作按钮 - 现代化设计 */
.share-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: flex-end;
    flex-shrink: 0;
}

.share-actions .action-btn {
    padding: 10px 16px;
    font-size: 13px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-secondary);
    white-space: nowrap;
    min-width: 70px;
    text-align: center;
    font-weight: 500;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.share-actions .action-btn:hover {
    transform: translateY(-2px);
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: 0 4px 16px rgba(255, 107, 157, 0.3);
}

.share-actions .delete-btn:hover {
    transform: translateY(-2px);
    border-color: var(--error-color);
    background: linear-gradient(135deg, var(--error-color), #d32f2f);
    color: white;
    box-shadow: 0 4px 16px rgba(244, 67, 54, 0.3);
}

/* 分享状态标签 - 现代化设计 */
.share-status {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    min-width: 70px;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.status-badge.active {
    background: linear-gradient(135deg, var(--success-color), #45a049);
    color: white;
}

.status-badge.expired {
    background: linear-gradient(135deg, var(--warning-color), #f57c00);
    color: white;
}

.status-badge.disabled {
    background: linear-gradient(135deg, var(--error-color), #d32f2f);
    color: white;
}

/* 空状态 - 现代化设计 */
.shares-items .empty-state {
    text-align: center;
    padding: 80px 20px;
    color: var(--text-muted);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    border-radius: 16px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.shares-items .empty-icon {
    font-size: 64px;
    margin-bottom: 24px;
    opacity: 0.6;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.shares-items .empty-state h3 {
    font-size: 20px;
    margin-bottom: 12px;
    color: var(--text-primary);
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.shares-items .empty-state p {
    font-size: 15px;
    margin-bottom: 32px;
    color: var(--text-secondary);
    line-height: 1.6;
}

/* 中等屏幕响应式设计 */
@media (max-width: 1024px) {
    .share-item-right {
        min-width: 160px;
    }

    .share-actions .action-btn {
        font-size: 12px;
        padding: 6px 10px;
        min-width: 50px;
    }
}

/* 小屏幕响应式设计 */
@media (max-width: 768px) {
    .shares-content {
        padding: 16px;
    }

    .shares-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .shares-header-left {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .shares-header-right {
        flex-direction: column;
        gap: 12px;
    }

    .selection-controls {
        margin-right: 0;
        justify-content: center;
    }

    .batch-controls {
        flex-wrap: wrap;
        gap: 8px;
        padding: 12px;
    }

    .batch-btn {
        flex: 1;
        min-width: 80px;
        text-align: center;
    }

    .toggle-select-btn {
        justify-content: center;
    }

    .share-item {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
        padding: 16px;
    }

    .share-checkbox {
        align-self: flex-start;
    }

    .share-item-right {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        width: 100%;
    }

    .share-actions {
        flex-direction: row;
        justify-content: center;
        gap: 8px;
    }

    .share-actions .action-btn {
        flex: 1;
        max-width: 80px;
        font-size: 12px;
        padding: 6px 8px;
    }

    .share-status {
        align-self: center;
        margin-left: 0;
    }

    .share-meta {
        flex-direction: column;
        gap: 8px;
    }

    .share-code {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.share-item {
    animation: slideInUp 0.3s ease-out;
}

.share-item:nth-child(1) { animation-delay: 0.1s; }
.share-item:nth-child(2) { animation-delay: 0.2s; }
.share-item:nth-child(3) { animation-delay: 0.3s; }
.share-item:nth-child(4) { animation-delay: 0.4s; }
.share-item:nth-child(5) { animation-delay: 0.5s; }
