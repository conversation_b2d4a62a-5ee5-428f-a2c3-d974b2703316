/* 仪表板样式 */

.layout {
    display: flex;
    min-height: 100vh;
    background-color: var(--bg-secondary);
}

/* 侧边栏样式 */
.sidebar {
    width: 260px;
    background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-light) 100%);
    border-right: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
}

.sidebar-header {
    padding: 24px 20px;
    border-bottom: 1px solid var(--border-light);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

.sidebar-header h2 {
    color: white;
    font-size: 20px;
    font-weight: 700;
    margin: 0;
    text-align: center;
}

.sidebar-nav ul {
    list-style: none;
    padding: 16px 0;
    margin: 0;
}

.sidebar-nav li {
    margin: 4px 12px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
    font-weight: 500;
}

.nav-link:hover {
    background-color: var(--secondary-color);
    color: var(--text-primary);
    transform: translateX(4px);
}

.nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    box-shadow: var(--shadow-sm);
}

.nav-icon {
    font-size: 18px;
    margin-right: 12px;
    width: 24px;
    text-align: center;
}

.nav-text {
    font-size: 14px;
}

/* 主内容区样式 */
.main-content {
    flex: 1;
    margin-left: 260px;
    display: flex;
    flex-direction: column;
}

/* 顶部栏样式 */
.top-bar {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-light);
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 999;
}

.top-bar h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.top-bar-left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
}

.top-bar-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-name {
    font-weight: 500;
    color: var(--text-primary);
}

.user-menu {
    position: relative;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
    background: none;
    cursor: pointer;
    overflow: hidden;
    transition: transform 0.2s ease;
}

.user-avatar:hover {
    transform: scale(1.05);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    min-width: 150px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    z-index: 1001;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown a {
    display: block;
    padding: 12px 16px;
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.user-dropdown a:hover {
    background-color: var(--bg-light);
}

/* 仪表板内容样式 */
.dashboard-content {
    padding: 24px;
    flex: 1;
}

/* 统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.stat-card {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-light) 100%);
    border-radius: var(--radius-lg);
    padding: 24px;
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
    border: 1px solid var(--border-light);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 16px;
}

.stat-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-light));
    border-radius: var(--radius-md);
}

.stat-info h3 {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 4px 0;
}

.stat-info p {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0;
    font-weight: 500;
}

/* 存储空间样式 */
.storage-section {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 24px;
    margin-bottom: 32px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

.storage-section h2 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 16px 0;
}

.storage-bar {
    height: 12px;
    background-color: var(--border-light);
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 12px;
}

.storage-used {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 6px;
    transition: width 0.3s ease;
}

.storage-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.storage-unlimited {
    color: var(--success-color);
    font-weight: 600;
}

/* 最近文件样式 */
.recent-files {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 24px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

.recent-files h2 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 16px 0;
}

.file-list {
    min-height: 200px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-light);
    transition: background-color 0.2s ease;
}

.file-item:hover {
    background-color: var(--bg-light);
    margin: 0 -12px;
    padding: 12px;
    border-radius: var(--radius-md);
}

.file-item:last-child {
    border-bottom: none;
}

.file-icon {
    font-size: 24px;
    margin-right: 12px;
    width: 32px;
    text-align: center;
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.file-meta {
    font-size: 12px;
    color: var(--text-muted);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .sidebar {
        width: 220px;
    }
    
    .main-content {
        margin-left: 220px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .top-bar {
        padding: 12px 16px;
    }
    
    .dashboard-content {
        padding: 16px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .stat-card {
        padding: 20px;
    }
    
    .storage-section,
    .recent-files {
        padding: 20px;
    }
}

/* 动画效果 */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.stat-card {
    animation: slideInRight 0.6s ease-out;
    animation-fill-mode: both;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

.storage-section {
    animation: slideInRight 0.6s ease-out 0.5s both;
}

.recent-files {
    animation: slideInRight 0.6s ease-out 0.6s both;
}
