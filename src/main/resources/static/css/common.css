/* 通用样式 - LLB文件管理系统 */

/* CSS变量定义 */
:root {
    /* 主色调 */
    --primary-color: #2196F3;
    --primary-dark: #1976D2;
    --primary-light: #BBDEFB;
    --accent-color: #FF4081;
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --error-color: #F44336;
    --info-color: #2196F3;

    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-light: #f5f5f5;
    --bg-dark: #212529;

    /* 文字颜色 */
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --text-light: #ffffff;

    /* 边框颜色 */
    --border-color: #dee2e6;
    --border-light: #e9ecef;
    --border-dark: #adb5bd;

    /* 阴影 */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);

    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;

    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
}

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 基础样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* 8px网格系统 */
.grid-8 { margin: 8px; }
.grid-16 { margin: 16px; }
.grid-24 { margin: 24px; }
.grid-32 { margin: 32px; }

.p-8 { padding: 8px; }
.p-16 { padding: 16px; }
.p-24 { padding: 24px; }
.p-32 { padding: 32px; }

.mt-8 { margin-top: 8px; }
.mt-16 { margin-top: 16px; }
.mt-24 { margin-top: 24px; }
.mt-32 { margin-top: 32px; }

.mb-8 { margin-bottom: 8px; }
.mb-16 { margin-bottom: 16px; }
.mb-24 { margin-bottom: 24px; }
.mb-32 { margin-bottom: 32px; }

/* 粉色系主题色彩 */
:root {
    --primary-color: #ff6b9d;
    --primary-light: #ffb3d1;
    --primary-dark: #e91e63;
    --secondary-color: #ffc1e3;
    --accent-color: #ff8fab;
    
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;
    
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-muted: #999999;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-light: #fdf2f8;
    
    --border-color: #e0e0e0;
    --border-light: #f0f0f0;
    
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 16px rgba(0,0,0,0.2);
    
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border: none;
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 40px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--text-primary);
}

.btn-outline {
    background-color: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-small {
    padding: 4px 12px;
    font-size: 12px;
    min-height: 32px;
}

.btn-large {
    padding: 12px 24px;
    font-size: 16px;
    min-height: 48px;
}

.btn-full {
    width: 100%;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 表单样式 */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
}

.form-help {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: var(--text-muted);
}

.form-error {
    color: var(--error-color);
}

/* 卡片样式 */
.card {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: box-shadow 0.2s ease;
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-light);
    background-color: var(--bg-light);
}

.card-body {
    padding: 24px;
}

.card-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--border-light);
    background-color: var(--bg-secondary);
}

/* Toast提示样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: var(--radius-md);
    color: white;
    font-weight: 500;
    z-index: 9999;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 300px;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background-color: var(--success-color);
}

.toast.error {
    background-color: var(--error-color);
}

.toast.warning {
    background-color: var(--warning-color);
}

.toast.info {
    background-color: var(--info-color);
}

/* 加载动画 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: var(--text-muted);
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    margin-left: 8px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        font-size: 16px;
    }
    
    .btn {
        min-height: 44px;
        font-size: 16px;
    }
    
    .form-group input,
    .form-group textarea,
    .form-group select {
        font-size: 16px;
        padding: 12px;
    }
    
    .card-body {
        padding: 16px;
    }
    
    .toast {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }
    
    .toast.show {
        transform: translateY(0);
    }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.w-full { width: 100%; }
.h-full { height: 100%; }
