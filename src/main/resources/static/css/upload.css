/* 文件上传页面样式 */

/* 面包屑导航 */
.breadcrumb {
    margin-top: 12px;
    font-size: 14px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-light);
    margin-bottom: 8px;
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.2s ease;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    position: relative;
}

.breadcrumb a:hover {
    color: var(--primary-dark);
    background-color: var(--bg-light);
    transform: translateY(-1px);
}

.breadcrumb a:first-child::before {
    content: '🏠';
    margin-right: 4px;
    font-size: 12px;
}

.breadcrumb a:not(:first-child)::before {
    content: '📁';
    margin-right: 4px;
    font-size: 12px;
}

.breadcrumb span {
    color: var(--text-muted);
    margin: 0 2px;
    font-weight: 300;
}

.breadcrumb span::before {
    content: '/';
    color: var(--text-muted);
    font-weight: 300;
    margin: 0 4px;
}

.upload-content {
    padding: 24px;
    flex: 1;
}

/* 上传区域 */
.upload-area {
    margin-bottom: 32px;
}

.upload-zone {
    border: 3px dashed #e0e0e0;
    border-radius: 12px;
    padding: 60px 40px;
    text-align: center;
    background-color: #ffffff;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    display: block;
    width: 100%;
    box-sizing: border-box;
    min-height: 200px;
}

.upload-zone:hover {
    border-color: #ff6b9d;
    background: linear-gradient(135deg, #ffffff, #fdf2f8);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.upload-zone.drag-over {
    border-color: #ff6b9d;
    background: linear-gradient(135deg, #ffc1e3, #ffb3d1);
    transform: scale(1.02);
}

.upload-icon {
    display: inline-block;
    margin-bottom: 16px;
    opacity: 0.6;
    transition: all 0.3s ease;
    color: #ff6b9d;
}

.upload-icon svg {
    width: 64px;
    height: 64px;
    stroke: currentColor;
}

.upload-zone:hover .upload-icon {
    opacity: 1;
    transform: scale(1.1);
}

.upload-zone h3 {
    font-size: 24px;
    font-weight: 600;
    color: #333333;
    margin-bottom: 8px;
}

.upload-zone p {
    font-size: 16px;
    color: #666666;
    margin-bottom: 24px;
}

/* 上传列表 */
.upload-list {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 24px;
    overflow: hidden;
}

.upload-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fdf2f8;
}

.upload-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333333;
}

.upload-actions {
    display: flex;
    gap: 12px;
}

.upload-actions .btn {
    padding: 6px 12px;
    font-size: 12px;
    min-height: 32px;
}

/* 上传项目 */
.upload-items {
    max-height: 400px;
    overflow-y: auto;
}

.upload-item {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-light);
    transition: background-color 0.2s ease;
}

.upload-item:hover {
    background-color: var(--bg-light);
}

.upload-item:last-child {
    border-bottom: none;
}

.upload-file-icon {
    font-size: 24px;
    margin-right: 16px;
    width: 32px;
    text-align: center;
}

.upload-file-info {
    flex: 1;
    margin-right: 16px;
}

.upload-file-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    word-break: break-word;
}

.upload-file-meta {
    font-size: 12px;
    color: var(--text-muted);
    display: flex;
    gap: 16px;
}

.upload-progress {
    width: 200px;
    margin-right: 16px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--border-light);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 4px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: 12px;
    color: var(--text-secondary);
    text-align: center;
}

.upload-status {
    width: 80px;
    text-align: center;
}

.status-badge {
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 600;
}

.status-badge.uploading {
    background-color: var(--info-color);
    color: white;
}

.status-badge.completed {
    background-color: var(--success-color);
    color: white;
}

.status-badge.error {
    background-color: var(--error-color);
    color: white;
}

.status-badge.paused {
    background-color: var(--warning-color);
    color: white;
}

.upload-actions-item {
    display: flex;
    gap: 4px;
}

.action-btn-small {
    padding: 4px 8px;
    font-size: 12px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-primary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-secondary);
}

.action-btn-small:hover {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    color: white;
}

/* 上传统计 */
.upload-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    padding: 16px;
    margin-bottom: 20px;
}

.stats-item {
    text-align: center;
    padding: 12px 8px;
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
}

.stats-label {
    display: block;
    font-size: 11px;
    color: var(--text-muted);
    margin-bottom: 2px;
    text-transform: uppercase;
    font-weight: 600;
    line-height: 1.2;
}

.stats-value {
    font-size: 16px;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.2;
}

/* 上传提示 */
.upload-tips {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 24px;
}

.upload-tips h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: #333333;
}

.upload-tips ul {
    margin: 0;
    padding-left: 20px;
    color: #666666;
}

.upload-tips li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.upload-tips .highlight {
    color: #ff6b9d;
    font-weight: 600;
}

/* 拖拽样式 */
.drag-over {
    border-color: #ff6b9d !important;
    background: linear-gradient(135deg, #ffc1e3, #ffb3d1) !important;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .upload-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
        padding: 14px;
    }
    
    .stats-item {
        padding: 10px 6px;
    }
    
    .stats-label {
        font-size: 10px;
    }
    
    .stats-value {
        font-size: 14px;
    }
}

@media (max-width: 768px) {
    .upload-content {
        padding: 16px;
    }
    
    .upload-zone {
        padding: 40px 20px;
    }
    
    .upload-zone h3 {
        font-size: 20px;
    }
    
    .upload-zone p {
        font-size: 14px;
    }
    
    .upload-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .upload-item {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        padding: 16px;
    }
    
    .upload-progress {
        width: 100%;
        margin-right: 0;
    }
    
    .upload-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;
        padding: 12px;
    }
    
    .stats-item {
        padding: 8px 4px;
    }
    
    .stats-label {
        font-size: 9px;
    }
    
    .stats-value {
        font-size: 13px;
    }
    
    .upload-file-meta {
        flex-direction: column;
        gap: 4px;
    }
}

@media (max-width: 480px) {
    .upload-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: 6px;
        padding: 10px;
    }
    
    .stats-item {
        padding: 6px 2px;
    }
    
    .stats-label {
        font-size: 8px;
    }
    
    .stats-value {
        font-size: 12px;
    }
    
    .upload-actions {
        flex-direction: column;
        gap: 8px;
    }
}

@media (max-width: 320px) {
    .upload-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: 4px;
        padding: 8px;
    }
    
    .stats-item {
        padding: 4px 1px;
    }
    
    .stats-label {
        font-size: 7px;
    }
    
    .stats-value {
        font-size: 11px;
    }
}

/* 动画效果 */
@keyframes uploadPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.upload-zone.drag-over {
    animation: uploadPulse 1s ease-in-out infinite;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.upload-item {
    animation: slideInUp 0.3s ease-out;
}

.upload-area {
    animation: slideInUp 0.6s ease-out;
}

.upload-tips {
    animation: slideInUp 0.6s ease-out 0.2s both;
}
