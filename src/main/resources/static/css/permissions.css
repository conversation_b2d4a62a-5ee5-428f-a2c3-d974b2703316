/* 权限管理页面样式 */

.permission-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow-sm);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    background: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.stat-info h3 {
    margin: 0 0 0.25rem 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.stat-info p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.toolbar {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow-sm);
}

.search-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow-sm);
}

.search-group {
    display: flex;
    gap: 0.5rem;
    flex: 1;
}

.filter-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.permissions-table-container {
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.permissions-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.permissions-table th,
.permissions-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.permissions-table th {
    background: var(--bg-light);
    font-weight: 600;
    color: var(--text-secondary);
    position: sticky;
    top: 0;
    z-index: 1;
}

.permissions-table tbody tr:hover {
    background: var(--hover-bg);
}

.permissions-table tbody tr.selected {
    background: var(--primary-light);
}

.permission-type {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.permission-type.read {
    background: var(--info-light);
    color: var(--info-color);
}

.permission-type.write {
    background: var(--warning-light);
    color: var(--warning-color);
}

.permission-type.delete {
    background: var(--danger-light);
    color: var(--danger-color);
}

.permission-type.share {
    background: var(--success-light);
    color: var(--success-color);
}

.resource-type {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.resource-type.file {
    background: var(--primary-light);
    color: var(--primary-color);
}

.resource-type.folder {
    background: var(--secondary-light);
    color: var(--secondary-color);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-buttons .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow-sm);
    margin-bottom: 1.5rem;
}

.pagination {
    display: flex;
    gap: 0.5rem;
}

.pagination .page-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    background: var(--card-bg);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.pagination .page-btn:hover {
    background: var(--hover-bg);
}

.pagination .page-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.batch-operations {
    position: fixed;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1rem;
    align-items: center;
    padding: 1rem 1.5rem;
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
}

.selected-count {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.modal-content {
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow-xl);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-content.large {
    max-width: 800px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-header .close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-header .close:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group .form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.form-group .form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: normal;
    margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
    margin: 0;
}

/* 图标样式 */
.icon-permission::before { content: "🔐"; }
.icon-user::before { content: "👤"; }
.icon-file::before { content: "📄"; }
.icon-folder::before { content: "📁"; }
.icon-plus::before { content: "+"; }
.icon-batch::before { content: "⚡"; }
.icon-report::before { content: "📊"; }
.icon-warning::before { content: "⚠️"; }
.icon-delete::before { content: "🗑️"; }
.icon-copy::before { content: "📋"; }

/* 响应式设计 */
@media (max-width: 1024px) {
    .search-filters {
        flex-direction: column;
    }
    
    .filter-group {
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    .permission-stats {
        grid-template-columns: 1fr;
    }
    
    .toolbar {
        flex-direction: column;
    }
    
    .permissions-table-container {
        overflow-x: auto;
    }
    
    .permissions-table {
        min-width: 800px;
    }
    
    .batch-operations {
        position: relative;
        bottom: auto;
        left: auto;
        transform: none;
        margin-top: 1rem;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.modal {
    animation: fadeIn 0.2s ease;
}

.batch-operations {
    animation: fadeIn 0.3s ease;
}

/* 状态指示器 */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-indicator.active {
    background: var(--success-color);
}

.status-indicator.inactive {
    background: var(--danger-color);
}

.status-indicator.pending {
    background: var(--warning-color);
}
