/* 管理员面板样式 */

/* 页面标题 */
.page-subtitle {
    color: #666;
    font-size: 14px;
    margin: 4px 0 0 0;
}

/* 统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

/* 统计卡片 */
.stat-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 16px;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    font-size: 32px;
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border-radius: 12px;
    flex-shrink: 0;
}

.stat-content h3 {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin: 0 0 4px 0;
}

.stat-content p {
    color: #666;
    font-size: 14px;
    margin: 0 0 8px 0;
}

.stat-change {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 500;
}

.stat-change.positive {
    background: #e8f5e8;
    color: #2e7d32;
}

.stat-change.negative {
    background: #ffebee;
    color: #c62828;
}

.stat-change.neutral {
    background: #f5f5f5;
    color: #666;
}

/* 管理员内容区域 */
.admin-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 32px;
}

/* 内容区块 */
.content-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
}

.section-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.section-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* 数据表格 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 16px;
}

.data-table th,
.data-table td {
    text-align: left;
    padding: 12px 8px;
    border-bottom: 1px solid #f0f0f0;
}

.data-table th {
    background: #fafafa;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.data-table td {
    color: #666;
    font-size: 14px;
}

.data-table tr:hover {
    background: #fafafa;
}

/* 文件信息 */
.file-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.file-icon {
    font-size: 16px;
}

/* 存储排行 */
.storage-ranking {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.ranking-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.ranking-item:hover {
    background: #f0f0f0;
}

.ranking-number {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    flex-shrink: 0;
}

.user-info {
    flex: 1;
}

.user-name {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.user-meta {
    color: #666;
    font-size: 12px;
    margin-top: 2px;
}

.storage-info {
    text-align: right;
    min-width: 100px;
}

.storage-used {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.storage-progress {
    margin-top: 4px;
}

.progress-bar {
    width: 80px;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* 操作按钮 */
.action-btn {
    background: none;
    border: 1px solid #ddd;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 4px;
}

.action-btn:hover {
    background: #f5f5f5;
}

.action-btn.delete-btn {
    border-color: #ffcdd2;
    color: #c62828;
}

.action-btn.delete-btn:hover {
    background: #ffebee;
}

/* 导航分隔线 */
.nav-divider {
    height: 1px;
    background: #f0f0f0;
    margin: 16px 0;
}

/* IP管理标签页 */
.ip-management-tabs {
    margin-top: 24px;
}

.tab-headers {
    display: flex;
    border-bottom: 2px solid #f0f0f0;
    margin-bottom: 24px;
}

.tab-header {
    background: none;
    border: none;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tab-header:hover {
    color: #333;
    background: #fafafa;
}

.tab-header.active {
    color: #ff6b9d;
    border-bottom-color: #ff6b9d;
    background: #fff5f8;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* IP地址样式 */
.ip-address {
    font-family: 'Courier New', monospace;
    background: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 13px;
    color: #333;
}

.reason-text,
.description-text {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 搜索筛选样式 */
.search-filters {
    background: #fafafa;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.search-input-group {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.search-input {
    flex: 1;
    min-width: 200px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.filter-group {
    display: flex;
    gap: 12px;
    margin-top: 12px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

/* 用户信息样式 */
.user-avatar-small {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.user-avatar-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.user-meta {
    color: #666;
    font-size: 12px;
    margin-top: 2px;
}

/* 角色和状态徽章 */
.role-badge,
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.role-badge.admin {
    background: #ffebee;
    color: #c62828;
}

.role-badge.user {
    background: #e8f5e8;
    color: #2e7d32;
}

.status-badge.active {
    background: #e8f5e8;
    color: #2e7d32;
}

.status-badge.disabled {
    background: #fff3e0;
    color: #f57c00;
}

.status-badge.locked {
    background: #ffebee;
    color: #c62828;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 24px;
}

.page-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    color: #666;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.page-btn:hover {
    background: #f5f5f5;
    border-color: #ccc;
}

.page-btn.active {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: white;
    border-color: #ff9a9e;
}

.page-ellipsis {
    padding: 8px 4px;
    color: #999;
}

/* 表单帮助文本 */
.form-help {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
    display: block;
}

/* 工具栏样式 */
.toolbar {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* 列表控制 */
.list-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 14px;
    color: #666;
}

.pagination-info {
    font-size: 12px;
}

/* 空状态样式 */
.empty-cell {
    text-align: center;
    padding: 40px 20px;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.empty-icon {
    font-size: 48px;
    opacity: 0.5;
}

.empty-state h3 {
    color: #666;
    font-size: 16px;
    margin: 0;
}

.empty-state p {
    color: #999;
    font-size: 14px;
    margin: 0;
}

.empty-actions {
    display: flex;
    gap: 12px;
    margin-top: 16px;
}

/* 加载状态 */
.loading-cell {
    text-align: center;
    padding: 40px 20px;
}

.loading {
    color: #666;
    font-size: 14px;
}

/* 管理面板统一内容区样式 */
.admin-dashboard-content {
    padding: 24px;
    flex: 1;
}

/* 管理面板页面头部样式 */
.admin-page-header {
    margin-bottom: 24px;
}

.admin-page-header h1 {
    font-size: 28px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px 0;
}

.admin-page-header .page-subtitle {
    color: #666;
    font-size: 14px;
    margin: 0;
}

/* 修复统计卡片间距 */
.stats-grid {
    margin-bottom: 32px;
}

.stats-grid .stat-card {
    margin: 0;
}

/* 修复内容区块间距 */
.content-section {
    margin-bottom: 24px;
}

.content-section:last-child {
    margin-bottom: 0;
}

/* 修复表格容器间距 */
.ip-table,
.users-table,
.permissions-table-container {
    margin-top: 16px;
}

/* 修复搜索筛选器间距 */
.search-filters {
    margin-bottom: 24px;
}

/* 修复工具栏间距 */
.toolbar {
    margin-bottom: 16px;
}

/* 修复顶部栏工具栏布局 */
.top-bar .toolbar {
    margin-bottom: 0;
    margin-right: 16px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .admin-content {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
    }

    .search-input-group {
        flex-direction: column;
        align-items: stretch;
    }

    .search-input {
        min-width: auto;
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        padding: 16px;
    }

    .content-section {
        padding: 16px;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .data-table {
        font-size: 12px;
    }

    .data-table th,
    .data-table td {
        padding: 8px 4px;
    }

    .tab-headers {
        flex-direction: column;
    }

    .tab-header {
        border-bottom: none;
        border-left: 3px solid transparent;
        justify-content: flex-start;
    }

    .tab-header.active {
        border-bottom: none;
        border-left-color: #ff6b9d;
    }

    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }

    .list-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

/* 统计分析页面样式 */
.stats-overview {
    margin-bottom: 32px;
}

.charts-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
}

.chart-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.chart-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.chart-legend {
    display: flex;
    gap: 16px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.legend-color.upload {
    background: #ff6b9d;
}

.legend-color.download {
    background: #4ecdc4;
}

.chart-container {
    position: relative;
    height: 300px;
}

.detailed-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
}

.activity-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.activity-item {
    text-align: center;
    padding: 16px;
    background: #fafafa;
    border-radius: 8px;
}

.activity-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.activity-value {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.activity-trend {
    font-size: 12px;
    font-weight: 500;
}

.activity-trend.positive {
    color: #2e7d32;
}

.activity-trend.negative {
    color: #c62828;
}

.activity-trend.neutral {
    color: #666;
}

.storage-stats {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.storage-overview {
    padding: 20px;
    background: #fafafa;
    border-radius: 8px;
}

.storage-item {
    text-align: center;
}

.storage-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.storage-value {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 12px;
}

.storage-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.storage-used {
    height: 100%;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* IP地理位置样式 */
.ip-location {
    margin-top: 4px;
}

.location-text {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 12px;
    display: inline-block;
    margin-top: 2px;
}

.location-text.location-local {
    background-color: #e6f7ff;
    color: #1890ff;
}

.location-text.location-private {
    background-color: #f6ffed;
    color: #52c41a;
}

.location-text.location-foreign {
    background-color: #fff7e6;
    color: #fa8c16;
}

.location-text.location-unknown {
    background-color: #f5f5f5;
    color: #999;
}

.ip-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.ip-address {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-weight: 600;
}

.ip-type-badge {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
    display: inline-block;
    max-width: fit-content;
}

.ip-type-badge.single {
    background-color: #e6f7ff;
    color: #1890ff;
}

.ip-type-badge.range {
    background-color: #fff7e6;
    color: #fa8c16;
}

.ip-type-badge.cidr {
    background-color: #f6ffed;
    color: #52c41a;
}

.storage-info {
    font-size: 12px;
    color: #666;
}

.storage-breakdown {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.breakdown-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #fafafa;
    border-radius: 6px;
}

.breakdown-type {
    font-weight: 500;
    color: #333;
}

.breakdown-size {
    color: #666;
    font-size: 14px;
}

/* 系统设置页面样式 - 统一到content-section结构 */
.content-section .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 16px;
}

.content-section .info-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.content-section .info-item label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 4px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.content-section .info-item span {
    font-size: 16px;
    color: #212529;
    font-weight: 600;
}

.content-section .setting-item {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.setting-item {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.content-section .setting-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.content-section .setting-item label {
    display: block;
    font-weight: 600;
    color: #212529;
    margin-bottom: 8px;
    font-size: 14px;
}

.content-section .setting-control {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 6px;
}

.content-section .setting-control .form-control {
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    background-color: #ffffff;
}

.content-section .setting-control .form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.content-section .setting-control input[type="number"] {
    width: 120px;
}

.content-section .setting-control textarea {
    width: 100%;
    resize: vertical;
    min-height: 80px;
}

.content-section .unit-text {
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
}

.content-section .help-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
}

.content-section .setting-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 12px;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #2196F3;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.content-section .action-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 0;
    border-bottom: 1px solid #e9ecef;
}

.content-section .action-item:last-child {
    border-bottom: none;
}

.content-section .action-info h4 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: #212529;
}

.content-section .action-info p {
    margin: 0;
    font-size: 14px;
    color: #6c757d;
}

.btn.btn-outline {
    background: white;
    border: 1px solid #dee2e6;
    color: #495057;
    transition: all 0.3s ease;
}

.btn.btn-outline:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

/* 响应式适配 */
@media (max-width: 768px) {
    .content-section .info-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .content-section .setting-control {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .content-section .setting-control .form-control {
        max-width: 100%;
    }
    
    .content-section .action-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .content-section .setting-actions {
        flex-direction: column;
    }
}

.breakdown-percent {
    color: #ff6b9d;
    font-weight: 600;
    font-size: 14px;
}

.ranking-tabs {
    display: flex;
    gap: 8px;
}

.ranking-tab {
    padding: 6px 12px;
    border: 1px solid #ddd;
    background: white;
    color: #666;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
}

.ranking-tab:hover {
    background: #f5f5f5;
}

.ranking-tab.active {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: white;
    border-color: #ff9a9e;
}

.ranking-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 400px;
    overflow-y: auto;
}

.ranking-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px;
    background: #fafafa;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.ranking-item:hover {
    background: #f0f0f0;
}

.ranking-number {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 12px;
    flex-shrink: 0;
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 500;
    color: #333;
    font-size: 14px;
    margin-bottom: 2px;
}

.file-meta {
    color: #666;
    font-size: 12px;
}

.ranking-value {
    font-weight: 600;
    color: #ff6b9d;
    font-size: 14px;
}

/* 响应式设计 - 统计页面 */
@media (max-width: 1024px) {
    .charts-container {
        grid-template-columns: 1fr;
    }

    .detailed-stats {
        grid-template-columns: 1fr;
    }

    .activity-stats {
        grid-template-columns: 1fr 1fr;
    }
}

@media (max-width: 768px) {
    .charts-container {
        gap: 16px;
    }

    .chart-section {
        padding: 16px;
    }

    .chart-container {
        height: 250px;
    }

    .detailed-stats {
        gap: 16px;
    }

    .activity-stats {
        grid-template-columns: 1fr;
    }

    .storage-breakdown {
        gap: 8px;
    }

    .breakdown-item {
        padding: 6px 8px;
    }

    .ranking-tabs {
        flex-wrap: wrap;
    }

    .ranking-tab {
        flex: 1;
        text-align: center;
    }
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f8f9fa;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #212529;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background-color: #e9ecef;
    color: #495057;
}

.modal-body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    background-color: #f8f9fa;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #212529;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    background-color: #ffffff;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-group input[type="number"] {
    -moz-appearance: textfield;
}

.form-group input[type="number"]::-webkit-outer-spin-button,
.form-group input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* 用户文件页面样式 */
.user-profile-card {
    display: flex;
    gap: 20px;
    align-items: center;
    padding: 24px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.user-profile-card .user-avatar {
    flex-shrink: 0;
}

.user-profile-card .user-avatar img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #e9ecef;
}

.user-profile-card .user-details {
    flex: 1;
}

.user-profile-card .user-details h3 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: #212529;
}

.user-profile-card .user-meta {
    margin: 0 0 16px 0;
    color: #6c757d;
    font-size: 14px;
}

.user-profile-card .storage-info {
    max-width: 400px;
}

.user-profile-card .storage-text {
    font-size: 14px;
    color: #495057;
    margin-bottom: 8px;
}

.user-profile-card .progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.user-profile-card .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    transition: width 0.3s ease;
}

.search-filters {
    padding: 20px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-input-group {
    display: flex;
    gap: 12px;
    align-items: center;
}

.search-input {
    flex: 1;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.breadcrumb {
    margin-top: 12px;
    font-size: 14px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-light);
    margin-bottom: 8px;
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.2s ease;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    position: relative;
}

.breadcrumb a:hover {
    color: var(--primary-dark);
    background-color: var(--bg-light);
    transform: translateY(-1px);
}

.breadcrumb a:first-child::before {
    content: '🏠';
    margin-right: 4px;
    font-size: 12px;
}

.breadcrumb a:not(:first-child)::before {
    content: '📁';
    margin-right: 4px;
    font-size: 12px;
}

.breadcrumb span {
    color: var(--text-muted);
    margin: 0 2px;
    font-weight: 300;
}

.breadcrumb span::before {
    content: '/';
    color: var(--text-muted);
    font-weight: 300;
    margin: 0 4px;
}

.breadcrumb-separator {
    margin: 0 8px;
    color: #adb5bd;
}
