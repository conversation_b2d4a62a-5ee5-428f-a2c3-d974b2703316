/* 认证页面样式 */

.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 50%, #fbcfe8 100%);
    padding: 20px;
}

.auth-card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 40px;
    width: 100%;
    max-width: 400px;
    position: relative;
    overflow: hidden;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--secondary-color));
}

.auth-header {
    text-align: center;
    margin-bottom: 32px;
}

.auth-header h1 {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-header p {
    color: var(--text-secondary);
    font-size: 16px;
}

.auth-form {
    margin-bottom: 24px;
}

.auth-form .form-group {
    margin-bottom: 20px;
}

.auth-form .form-group:last-child {
    margin-bottom: 0;
}

.auth-form label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 6px;
}

.auth-form input {
    height: 48px;
    font-size: 16px;
    border: 2px solid var(--border-color);
    transition: all 0.2s ease;
}

.auth-form input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(255, 107, 157, 0.1);
}

.auth-form input:invalid {
    border-color: var(--error-color);
}

.auth-form .btn {
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    margin-top: 8px;
    position: relative;
    overflow: hidden;
}

.auth-form .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.auth-form .btn:hover::before {
    left: 100%;
}

.btn-loading {
    display: none;
}

.btn.loading .btn-text {
    display: none;
}

.btn.loading .btn-loading {
    display: inline-block;
}

.auth-footer {
    text-align: center;
    padding-top: 24px;
    border-top: 1px solid var(--border-light);
}

.auth-footer p {
    color: var(--text-secondary);
    font-size: 14px;
}

.auth-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.2s ease;
}

.auth-footer a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* 表单验证样式 */
.form-group.error input {
    border-color: var(--error-color);
    box-shadow: 0 0 0 4px rgba(244, 67, 54, 0.1);
}

.form-group.success input {
    border-color: var(--success-color);
    box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);
}

.form-error-message {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: var(--error-color);
    font-weight: 500;
}

/* 密码强度指示器 */
.password-strength {
    margin-top: 8px;
    height: 4px;
    background-color: var(--border-light);
    border-radius: 2px;
    overflow: hidden;
}

.password-strength-bar {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.password-strength-bar.weak {
    width: 33%;
    background-color: var(--error-color);
}

.password-strength-bar.medium {
    width: 66%;
    background-color: var(--warning-color);
}

.password-strength-bar.strong {
    width: 100%;
    background-color: var(--success-color);
}

/* 响应式设计 */
@media (max-width: 480px) {
    .auth-container {
        padding: 16px;
    }
    
    .auth-card {
        padding: 24px;
        max-width: none;
    }
    
    .auth-header h1 {
        font-size: 24px;
    }
    
    .auth-header p {
        font-size: 14px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-card {
    animation: fadeInUp 0.6s ease-out;
}

.auth-form .form-group {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.auth-form .form-group:nth-child(1) { animation-delay: 0.1s; }
.auth-form .form-group:nth-child(2) { animation-delay: 0.2s; }
.auth-form .form-group:nth-child(3) { animation-delay: 0.3s; }
.auth-form .form-group:nth-child(4) { animation-delay: 0.4s; }
.auth-form .form-group:nth-child(5) { animation-delay: 0.5s; }

.auth-form .btn {
    animation: fadeInUp 0.6s ease-out 0.6s both;
}

.auth-footer {
    animation: fadeInUp 0.6s ease-out 0.7s both;
}
