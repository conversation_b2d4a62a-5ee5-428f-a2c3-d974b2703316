/* 分享访问页面样式 */

body {
    background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 50%, #fbcfe8 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.share-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

/* 分享头部 */
.share-header {
    text-align: center;
    margin-bottom: 32px;
}

.share-header h1 {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.share-header p {
    font-size: 16px;
    color: var(--text-secondary);
}

/* 分享内容 */
.share-content {
    margin-bottom: 32px;
}

/* 密码表单 */
.form-card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 40px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.form-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--secondary-color));
}

.form-header {
    margin-bottom: 32px;
}

.share-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.form-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.form-header p {
    color: var(--text-secondary);
    font-size: 16px;
}

#verifyForm .form-group {
    margin-bottom: 24px;
}

#verifyForm input {
    width: 100%;
    padding: 16px;
    font-size: 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    text-align: center;
    transition: all 0.2s ease;
}

#verifyForm input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(255, 107, 157, 0.1);
}

/* 文件信息卡片 */
.file-card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    position: relative;
}

.file-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--secondary-color));
}

.file-header {
    display: flex;
    align-items: center;
    padding: 32px;
    background: linear-gradient(135deg, var(--bg-light), var(--secondary-color));
}

.file-icon {
    font-size: 48px;
    margin-right: 20px;
}

.file-details h2 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
    word-break: break-word;
}

.file-meta {
    display: flex;
    gap: 16px;
    font-size: 14px;
    color: var(--text-secondary);
}

/* 分享信息 */
.share-info {
    padding: 24px 32px;
    border-bottom: 1px solid var(--border-light);
}

.share-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
}

.stat-item {
    text-align: center;
    padding: 16px;
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
}

.stat-label {
    display: block;
    font-size: 12px;
    color: var(--text-muted);
    margin-bottom: 4px;
    text-transform: uppercase;
    font-weight: 600;
}

.stat-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

/* 文件操作 */
.file-actions {
    display: flex;
    gap: 16px;
    padding: 32px;
    justify-content: center;
}

.file-actions .btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    font-size: 16px;
    min-width: 140px;
    justify-content: center;
}

.btn-icon {
    font-size: 18px;
}

/* 文件预览 */
.file-preview {
    border-top: 1px solid var(--border-light);
    background-color: var(--bg-secondary);
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-light);
    background-color: var(--bg-primary);
}

.preview-header h3 {
    margin: 0;
    font-size: 16px;
    color: var(--text-primary);
}

.close-preview {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-muted);
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.close-preview:hover {
    background-color: var(--bg-light);
    color: var(--text-primary);
}

.preview-content {
    padding: 24px;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 错误卡片 */
.error-card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 40px;
    text-align: center;
}

.error-icon {
    font-size: 64px;
    margin-bottom: 24px;
}

.error-card h2 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.error-message {
    font-size: 16px;
    color: var(--text-secondary);
    margin-bottom: 24px;
}

.error-reasons {
    text-align: left;
    margin-bottom: 32px;
    padding: 20px;
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
}

.error-reasons h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: var(--text-primary);
}

.error-reasons ul {
    margin: 0;
    padding-left: 20px;
    color: var(--text-secondary);
}

.error-reasons li {
    margin-bottom: 4px;
}

.error-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
}

/* 分享页脚 */
.share-footer {
    text-align: center;
}

.share-tips {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    padding: 24px;
    margin-bottom: 16px;
}

.share-tips h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: var(--text-primary);
}

.share-tips ul {
    text-align: left;
    margin: 0;
    padding-left: 20px;
    color: var(--text-secondary);
}

.share-tips li {
    margin-bottom: 8px;
}

.powered-by {
    color: var(--text-muted);
    font-size: 14px;
}

.powered-by strong {
    color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .share-container {
        padding: 0 16px;
    }
    
    .form-card,
    .file-card {
        padding: 24px;
    }
    
    .file-header {
        flex-direction: column;
        text-align: center;
        padding: 24px;
    }
    
    .file-icon {
        margin-right: 0;
        margin-bottom: 16px;
    }
    
    .file-meta {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .share-stats {
        grid-template-columns: 1fr;
    }
    
    .file-actions {
        flex-direction: column;
        padding: 24px;
    }
    
    .error-actions {
        flex-direction: column;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-card,
.file-card,
.error-card {
    animation: fadeInUp 0.6s ease-out;
}

.share-tips {
    animation: fadeInUp 0.6s ease-out 0.2s both;
}
