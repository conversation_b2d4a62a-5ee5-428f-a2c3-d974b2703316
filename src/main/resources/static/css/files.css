/* 文件管理页面样式 */

.files-content {
    padding: 24px;
    flex: 1;
}

/* 面包屑导航 */
.breadcrumb {
    margin-top: 12px;
    font-size: 14px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-light);
    margin-bottom: 8px;
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.2s ease;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    position: relative;
}

.breadcrumb a:hover {
    color: var(--primary-dark);
    background-color: var(--bg-light);
    transform: translateY(-1px);
}

.breadcrumb a:first-child::before {
    content: '🏠';
    margin-right: 4px;
    font-size: 12px;
}

.breadcrumb a:not(:first-child)::before {
    content: '📁';
    margin-right: 4px;
    font-size: 12px;
}

.breadcrumb span {
    color: var(--text-muted);
    margin: 0 2px;
    font-weight: 300;
}

.breadcrumb span::before {
    content: '/';
    color: var(--text-muted);
    font-weight: 300;
    margin: 0 4px;
}

/* 工具栏 */
.toolbar {
    display: flex;
    gap: 12px;
    margin-right: 16px;
}

/* 搜索栏 */
.search-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px;
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.search-input-group {
    display: flex;
    gap: 12px;
    flex: 1;
    max-width: 400px;
}

.search-input {
    flex: 1;
    padding: 8px 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
}

/* 视图控制 */
.view-controls {
    display: flex;
    gap: 4px;
}

.view-btn {
    padding: 8px 12px;
    border: 2px solid var(--border-color);
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 16px;
}

.view-btn:hover {
    border-color: var(--primary-color);
    background-color: var(--bg-light);
}

.view-btn.active {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    color: white;
}

/* 批量操作栏 */
.batch-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 16px;
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-light));
    border-radius: var(--radius-md);
    border: 1px solid var(--primary-color);
}

.batch-info {
    font-weight: 600;
    color: var(--text-primary);
}

.batch-buttons {
    display: flex;
    gap: 8px;
}

.batch-buttons .btn {
    padding: 6px 12px;
    font-size: 12px;
    min-height: 32px;
}

/* 文件网格 */
.file-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
}

.file-grid.list-view {
    grid-template-columns: 1fr;
    gap: 8px;
}

/* 文件项 */
.file-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background-color: var(--bg-primary);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
}

.file-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.file-item.selected {
    border-color: var(--primary-color);
    background-color: var(--bg-light);
}

.file-item.folder-item {
    border-color: var(--accent-color);
}

.file-item.folder-item:hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--bg-light), var(--secondary-color));
}

/* 网格视图 */
.file-grid:not(.list-view) .file-item {
    flex-direction: column;
    text-align: center;
    padding: 20px;
}

.file-grid:not(.list-view) .file-checkbox {
    position: absolute;
    top: 12px;
    left: 12px;
}

.file-grid:not(.list-view) .file-icon {
    font-size: 48px;
    margin-bottom: 12px;
}

.file-grid:not(.list-view) .file-info {
    flex: 1;
    width: 100%;
}

.file-grid:not(.list-view) .file-actions {
    margin-top: 12px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.file-grid:not(.list-view) .file-item:hover .file-actions {
    opacity: 1;
}

/* 列表视图 */
.file-grid.list-view .file-item {
    padding: 12px 16px;
    border-radius: var(--radius-md);
}

.file-grid.list-view .file-checkbox {
    margin-right: 12px;
}

.file-grid.list-view .file-icon {
    font-size: 24px;
    margin-right: 12px;
    width: 32px;
    text-align: center;
}

.file-grid.list-view .file-info {
    flex: 1;
}

.file-grid.list-view .file-actions {
    margin-left: 12px;
}

/* 文件信息 */
.file-checkbox input {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
}

.file-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    word-break: break-word;
}

.file-meta {
    font-size: 12px;
    color: var(--text-muted);
}

/* 文件操作 */
.file-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 4px 8px;
    font-size: 12px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-primary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-secondary);
}

.action-btn:hover {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 分享按钮使用默认样式，与其他按钮保持一致 */
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: var(--text-muted);
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 20px;
    margin-bottom: 8px;
    color: var(--text-secondary);
}

.empty-state p {
    font-size: 14px;
    margin-bottom: 24px;
}

.empty-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-light);
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-muted);
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.modal-close:hover {
    background-color: var(--bg-light);
    color: var(--text-primary);
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px;
    border-top: 1px solid var(--border-light);
    background-color: var(--bg-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .files-content {
        padding: 16px;
    }
    
    .search-bar {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .search-input-group {
        max-width: none;
    }
    
    .file-grid {
        grid-template-columns: 1fr;
    }
    
    .file-grid:not(.list-view) .file-item {
        flex-direction: row;
        text-align: left;
        padding: 16px;
    }
    
    .file-grid:not(.list-view) .file-checkbox {
        position: static;
        margin-right: 12px;
    }
    
    .file-grid:not(.list-view) .file-icon {
        font-size: 24px;
        margin-bottom: 0;
        margin-right: 12px;
    }
    
    .file-grid:not(.list-view) .file-actions {
        margin-top: 0;
        margin-left: 12px;
        opacity: 1;
    }
    
    .toolbar {
        flex-direction: column;
        gap: 8px;
    }
    
    .batch-actions {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.file-item {
    animation: fadeInUp 0.3s ease-out;
}

.modal-content {
    animation: fadeInUp 0.3s ease-out;
}

/* 搜索结果样式 */
.search-results-header {
    grid-column: 1 / -1;
    margin-bottom: 16px;
    padding: 16px;
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-color);
}

.search-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: var(--text-secondary);
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
}

/* 空状态样式 */
.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 48px 24px;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 500;
}

.empty-state p {
    margin: 0 0 24px 0;
    font-size: 14px;
}

/* 加载状态样式 */
.loading-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 48px 24px;
    color: var(--text-secondary);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 全选控件样式 */
.selection-controls {
    display: flex;
    align-items: center;
    margin: 0 16px;
}

.select-all-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--text-secondary);
    cursor: pointer;
    user-select: none;
}

.select-all-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.select-all-label:hover {
    color: var(--text-primary);
}

/* 移动模态框样式 */
.folder-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    padding: 8px;
}

.folder-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: background-color 0.2s;
}

.folder-item:hover {
    background-color: var(--bg-light);
}

.folder-item.selected {
    background-color: var(--primary-color);
    color: white;
}

.folder-item.selected .folder-icon {
    filter: brightness(0) invert(1);
}

.folder-icon {
    font-size: 16px;
}

.folder-name {
    font-size: 14px;
    font-weight: 500;
}

/* 分享模态框样式 */
.share-form-section,
.share-result-section {
    transition: all 0.3s ease;
}

.input-group {
    display: flex;
    gap: 8px;
}

.input-group input {
    flex: 1;
}

.input-group .btn {
    flex-shrink: 0;
    padding: 8px 16px;
}

.share-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 16px;
    margin-top: 16px;
}

.share-info p {
    margin: 8px 0;
    font-size: 14px;
}

.share-info strong {
    color: #495057;
}

/* 表单样式增强 */
#shareModal .form-group {
    margin-bottom: 20px;
}

#shareModal .form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #495057;
}

#shareModal select,
#shareModal input[type="text"],
#shareModal input[type="number"],
#shareModal input[type="datetime-local"] {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#shareModal select:focus,
#shareModal input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#shareModal input[readonly] {
    background-color: #f8f9fa;
    cursor: default;
}

/* 分享链接样式 */
.share-url-link {
    color: #007bff !important;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    word-break: break-all;
    display: inline-block;
}

.share-url-link:hover {
    color: #0056b3 !important;
    text-decoration: underline;
    transform: translateY(-1px);
}

.share-url-link:active {
    transform: translateY(0);
}

/* 分享密码样式 */
.share-password {
    background-color: #f8f9fa;
    color: #495057;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', Courier, monospace;
    font-size: 14px;
    font-weight: 600;
    border: 1px solid #dee2e6;
    display: inline-block;
    user-select: all;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 8px;
}

.share-password:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 复制密码按钮 */
.copy-password-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.copy-password-btn:hover {
    background: linear-gradient(135deg, #218838, #1dc7a2);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

.copy-password-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

/* 分享信息区域增强 */
.share-info {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-top: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.share-info p {
    margin: 12px 0;
    font-size: 14px;
    line-height: 1.6;
}

.share-info strong {
    color: #343a40;
    font-weight: 600;
}
