/* 个人中心页面样式 */

.profile-content {
    padding: 24px;
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
}

/* 用户信息卡片 */
.profile-card {
    background: linear-gradient(135deg, var(--bg-primary), var(--bg-light));
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    margin-bottom: 24px;
}

.profile-header {
    display: flex;
    align-items: center;
    padding: 32px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
}

.avatar-section {
    margin-right: 24px;
}

.avatar-container {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.avatar-container:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.6);
}

.avatar-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.avatar-container:hover .avatar-overlay {
    opacity: 1;
}

.avatar-upload-btn {
    background: none;
    border: 2px solid white;
    color: white;
    padding: 8px 16px;
    border-radius: var(--radius-md);
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.avatar-upload-btn:hover {
    background-color: white;
    color: var(--primary-color);
}

.user-basic-info h2 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 700;
}

.username {
    font-size: 16px;
    opacity: 0.9;
    margin-bottom: 16px;
}

.user-badges {
    display: flex;
    gap: 8px;
}

.badge {
    padding: 4px 12px;
    border-radius: var(--radius-md);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.badge.admin {
    background-color: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.badge.user {
    background-color: rgba(33, 150, 243, 0.2);
    color: #2196f3;
    border: 1px solid rgba(33, 150, 243, 0.3);
}

.badge.status.active {
    background-color: rgba(76, 175, 80, 0.2);
    color: #4caf50;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

/* 用户统计 */
.profile-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1px;
    background-color: var(--border-light);
}

.stat-item {
    background-color: var(--bg-primary);
    padding: 24px;
    text-align: center;
    transition: background-color 0.2s ease;
}

.stat-item:hover {
    background-color: var(--bg-light);
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
}

/* 存储卡片 */
.storage-card {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    padding: 24px;
    margin-bottom: 24px;
}

.storage-card h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    color: var(--text-primary);
}

.storage-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.storage-text {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.storage-text .unlimited {
    color: var(--success-color);
}

.storage-bar {
    height: 12px;
    background-color: var(--border-light);
    border-radius: 6px;
    overflow: hidden;
}

.storage-used {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 6px;
    transition: width 0.3s ease;
}

/* 设置卡片 */
.settings-card {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    padding: 24px;
    margin-bottom: 24px;
}

.settings-card h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    color: var(--text-primary);
    padding-bottom: 12px;
    border-bottom: 2px solid var(--border-light);
}

/* 表单样式 */
.profile-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.profile-form .form-group {
    margin-bottom: 0;
}

.profile-form input.readonly {
    background-color: var(--bg-light);
    color: var(--text-muted);
    cursor: not-allowed;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    padding-top: 16px;
    border-top: 1px solid var(--border-light);
}

/* 账户信息 */
.account-info {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-light);
}

.info-item:last-child {
    border-bottom: none;
}

.info-item label {
    font-weight: 600;
    color: var(--text-secondary);
}

.info-item span {
    color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .profile-content {
        padding: 16px;
    }
    
    .profile-header {
        flex-direction: column;
        text-align: center;
        padding: 24px;
    }
    
    .avatar-section {
        margin-right: 0;
        margin-bottom: 20px;
    }
    
    .avatar-container {
        width: 100px;
        height: 100px;
    }
    
    .profile-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .settings-card {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .profile-stats {
        grid-template-columns: 1fr;
    }
    
    .stat-item {
        padding: 16px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.profile-card {
    animation: fadeInUp 0.6s ease-out;
}

.storage-card {
    animation: fadeInUp 0.6s ease-out 0.1s both;
}

.settings-card {
    animation: fadeInUp 0.6s ease-out;
}

.settings-card:nth-child(4) { animation-delay: 0.2s; }
.settings-card:nth-child(5) { animation-delay: 0.3s; }
.settings-card:nth-child(6) { animation-delay: 0.4s; }
